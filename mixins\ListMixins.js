/**
 * 新增修改完成调用 modalFormOk方法 编辑弹框组件ref定义为modalForm
 * data中url定义 list为查询列表  delete为删除单条记录
 */
import { post } from '@/lib/Util'
import moment from 'moment'
import { findAlertInfo, getCurrentUser } from '@/api/system'

export default {
  data() {
    return {
      loading: false,
      visibleEdit: false, // 编辑页面显示flag
      size: 'small', // medium / small / mini
      searchForm: {},
      url: {},
      currentUrl: [],
      userNo: [],
      page: {
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      tableData: [],
      selectedRowKeys: [], // table选中keys
      selectionRows: [], // table选中records
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date()).format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '昨日',
            onClick(picker) {
              const end = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 23:59:59')
              )
              const start = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('isoWeek')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  // mixins: [BaseMixins],
  created() {
    console.log('mixin 生效了')
    this.handleSearch(true)
    this.currentUrl = this.$router.currentRoute.path
  },
  methods: {
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      if (this.currentUrl == '/warning/WidgetEarlyWarning') {
        await post(getCurrentUser, {}).then(res => {
          this.userNo = res.data.userNo
        })
        // 搜索
        this.loading = true
        const { data } = await post(
          this.url.list,
          Object.assign({}, this.searchForm, {
            userNo: this.userNo,
            pageIndex: this.page.pageIndex,
            pageSize: this.page.pageSize
          })
        )
        // console.log(data)
        this.tableData = data ? data.content : []
        this.page.pageSize = data.pageable.pageSize
        this.page.totalPages = data.totalPages
        this.page.total = data.totalElements
        this.afterHandleSearch(this.tableData)
        this.loading = false
      } else {
        // 搜索
        this.loading = true
        const { data } = await post(
          this.url.list,
          Object.assign({}, this.searchForm, {
            pageIndex: this.page.pageIndex,
            pageSize: this.page.pageSize
          })
        )
        // console.log(data)
        this.tableData = data ? data.content : []
        this.page.pageSize = data.pageable.pageSize
        this.page.totalPages = data.totalPages
        this.page.total = data.totalElements
        this.afterHandleSearch(this.tableData)
        this.loading = false
      }
    },
    handleReset() {
      this.searchForm = {}
      this.handleSearch(true)
    },
    changeTableSize(size) {
      console.log('改变尺寸')
      this.size = size
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.pageIndex = val
      this.handleSearch()
    },
    beforeHandleSearch() {
      // console.log('before load data')
    },
    afterHandleSearch() {
      // console.log('after load data')
    },
    handleDelete: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm('是否确认删除此数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除操作
        post(this.url.delete, { id: data.id }).then(res => {
          this.handleSearch()
        })
      })
    },
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
    },
    onClearSelected() {
      this.selectedRowKeys = []
      this.selectionRows = []
    },
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    handleAdd: function() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.visible = true
    },
    handleToggleSearch() {
      this.toggleSearchStatus = !this.toggleSearchStatus
    },
    handleDetail: function(record) {
      // this.$refs.modalForm.edit(record)
      // this.$refs.modalForm.title = '详情'
      // this.$refs.modalForm.disableSubmit = true
    },
    getDict(value, list) {
      const match = this[list].find(item => item.value === value)
      return match ? match : {}
    }
  }
}
