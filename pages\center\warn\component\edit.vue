<template>
  <div>
    <el-dialog
      v-bind="$attrs"
      :title="title + '菜单'"
      :visible.sync="dialogVisible"
      @close="onClose"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        label-width="120px"
        size="medium"
      >
        <el-form-item
          label="规则名称"
          prop="code"
        >
          <el-input
            v-model="formData.ruleName"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入菜单名称"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { post } from '@/lib/Util'
import {
  findBasicDataConfigByType,
  resourceAdd,
  changeRuleName,
  resourceRelateRole,
  roleByRscID,
  roleListNoPage
} from '@/api/system'
import IconSelect from '@/components/IconSelect'
import IconPng from '@/components/IconPng'
import MenuSelect from '@/components/menuSelect'
import * as _ from 'lodash'
import UserSelect from '@/components/userSelect'

export default {
  components: {
    UserSelect,
    MenuSelect,
    IconPng,
    IconSelect
  },
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'editNode', 'mode', 'treeData', 'totalMenu'],
  data() {
    return {
      loading: false,
      title: '',
      editType: 'edit', // edit add
      formData: {
        ruleName: ''
      },
      rules: {
        customName: [
          {
            required: true,
            message: '请输入菜单名称',
            trigger: 'blur'
          }
        ]
      },
      typeOptions: [
        {
          label: '页面菜单',
          value: 'menu'
        },
        {
          label: '按钮',
          value: 'button'
        },
        {
          label: '小部件',
          value: 'plugin'
        }
      ],
      serviceList: [],
      roleList: [],
      originalRole: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set() {}
    },
    serviceObj: {
      get() {
        const match = this.serviceList.find(
          item => item.name === this.formData.ruleName
        )
        return {
          ip: match ? match.IP : '',
          port: match ? match.PORT : ''
        }
      },
      set() {}
    }
  },
  watch: {
    mode: function(value) {
      this.rules.pluginSize = [
        {
          required: value === 'plugin',
          message: '请选择默认尺寸',
          trigger: 'blur'
        }
      ]
    }
  },
  created() {},
  mounted() {
    //this.formData.customName = this.editNode.customName
  },
  methods: {
    getRoles() {
      post(roleByRscID, { rscID: this.editNode.id }).then(res => {
        this.formData.roles = res.data.map(item => item.id)
        this.originalRole = res.data.map(item => item.id)
      })
    },
    onOpen() {
      //console.log(this.editNode, 'this.editNode')
      //this.formData.customName = this.editNode.customName
    },
    async getRoleList() {
      const roleAll = await post(roleListNoPage, { id: this.id })
      if (!roleAll.success) return
      this.roleList = roleAll.data.map(item => {
        item.label = item.roleName
        item.key = item.id
        return item
      })
    },
    onClose() {
      this.$refs['form'].resetFields()
      this.formData = {
        ruleName: ''
      }
    },
    close() {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        this.$emit('success')
      })
    },
    handelConfirm() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.loading = true
        switch (this.editType) {
          case 'edit':
            let saveData = {}
            saveData.ruleID = this.editNode.id
            saveData.userNo = this.editNode.userNo
            saveData.id = this.editNode.ucwID
            saveData.status = this.editNode.status
            saveData.ruleName = this.formData.ruleName
            //console.log(list, 'list')
            post(changeRuleName, saveData).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('修改成功')
                //this.saveRole(res.data)
                this.close()
              }
            })
            break
        }
      })
    },
    async saveRole(id) {
      const addRoleIDs = this.formData.roles.filter(
        item => !this.originalRole.find(or => or === item)
      )
      const deleteRoleIDs = this.originalRole.filter(
        item => !this.formData.roles.find(or => or === item)
      )
      if (!addRoleIDs.length && !deleteRoleIDs.length) return
      // console.log(addRoleIDs, deleteRoleIDs)
      const roleRes = await post(resourceRelateRole, {
        id,
        addRoleIDs,
        deleteRoleIDs
      })
      !roleRes.success && this.$message.warning('分配角色失败！')
    },
    setIcon(name) {
      this.formData.icon = name
    },
    setDeskIcon(name) {
      this.formData.deskIcon = name
    },
    handleUrl(url) {
      return url
    },
    handleChangeMenu(value) {
      if (value === 'button' && this.editNode) {
        this.formData.url = this.editNode.url
      }
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
    }
  }
}
</script>
<style scoped>
.ip-text {
  font-size: 16px;
  margin-left: 10px;
}
</style>
