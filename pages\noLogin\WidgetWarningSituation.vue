<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="统计维度"
              prop="ruleName"
            >
              <el-radio 
                v-model="searchForm.mode"
                :label="1"
                border
                @change="handleSearch"
              >角色</el-radio>
              <el-radio
                v-model="searchForm.mode"
                :label="2"
                border
                @change="handleSearch"
              >模块</el-radio>
            </el-form-item>
            <template
              v-if="searchForm.mode === 1">
              <el-form-item
                label="产线"
                prop="ruleName"
              >
                <el-select
                  v-model="searchForm.productionLineName"
                  size="small"
                  multiple
                  placeholder="选择产线"
                  style="width: 120px"
                >
                  <el-option
                    :label="'第一炼钢厂'"
                    :value="'第一炼钢厂'"
                  />
                  <el-option
                    :label="'宽厚板厂'"
                    :value="'宽厚板厂'"
                  />
                  <el-option
                    :label="'板卷厂'"
                    :value="'板卷厂'"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="角色"
                prop="ruleName"
              >
                <el-select
                  v-model="searchForm.roleIDs"
                  size="small"
                  multiple
                  placeholder="选择角色"
                  style="width: 470px"
                >
                  <el-option
                    v-for="(item, index) in filterRoleList"
                    :key="index"
                    :label="item.roleName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </template>
            <el-form-item
              label="时间"
              prop="dateTime"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                :picker-options="pickerOptions"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right"
                style="width: 340px"/>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button
            type="primary"
            @click="exportDetail">详情导出</el-button>

          <el-button
            icon="ios-search"
            type="primary"
            @click="handleExport"
          >导出
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">

        <el-tabs v-model="tabActive">
          <el-tab-pane
            :name="'0'"
            label="处理统计表">
            <br>
            <el-table
              :data="tableData"
              size="small"
              border
              style="width: 100%"
            >
              <el-table-column
                :label="mode === 1 ? '角色名称' : '模块名称'"
                sortable
                prop="roleName"
              />
              <el-table-column
                label="报警总数"
                sortable
                prop="total"
              />
              <template v-if="mode === 1">
                <el-table-column
                  label="已处理"
                  sortable
                  prop="done"
                  min-width="100"
                />
                <el-table-column
                  label="未处理"
                  prop="unDone"
                  sortable
                  min-width="100"
                />
              </template>
              <template v-if="mode === 2">
                <el-table-column
                  label="一级报警"
                  prop="firstLevel"
                  sortable
                  min-width="100"
                />
                <el-table-column
                  label="二级报警"
                  prop="secondLevel"
                  sortable
                  min-width="100"
                />
                <el-table-column
                  label="三级报警"
                  prop="thirdLevel"
                  sortable
                  min-width="100"
                />
              </template>
              <el-table-column
                label="不标准建议数"
                prop="adviceAtypia"
                sortable
                min-width="100"
              />
              <el-table-column
                label="不标准分析数"
                prop="analysisAtypia"
                sortable
                min-width="100"
              />
              <el-table-column
                label="误报"
                prop="isFalse"
                sortable
                min-width="100"
              />
              <el-table-column
                label="已处理"
                prop="done"
                sortable
                min-width="100"
              />
              <el-table-column
                label="未处理"
                prop="unDone"
                sortable
                min-width="100"
              />
              <el-table-column
                label="处置数量"
                prop="alertAdviceCount"
                sortable
                min-width="100"
              />
              <el-table-column
                label="完成率"
                sortable
                prop="rate"
              >
                <template v-slot="{ row }">
                  {{ row.rate }}%
                </template>
              </el-table-column>
              <el-table-column
                label="2小时内处理完"
                sortable
                prop="twoHourDone"
                min-width="100"
              />
              <el-table-column
                label="4小时内处理完"
                sortable
                prop="fourHourDone"
                min-width="100"
              />
              <el-table-column
                label="8小时内处理完"
                sortable
                prop="eightHourDone"
                min-width="100"
              />
              <el-table-column
                label="8小时内未处理完"
                sortable
                prop="gtEightHourDone"
                min-width="120"
              />
              <el-table-column
                label="操作"
                prop="createDateTime"
                width="80"
              >
                <template v-slot="{ row }">
                  <el-button
                    type="text"
                    @click="exportDetail(row)">详情导出</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  exportAlertInfoByModuleCodes,
  exportAlertInfoByRoleIDs,
  exportDoneRate,
  exportDoneRateByModuleCodes,
  findModuleInfoList,
  getDoneRateByModuleCodes,
  getDoneRateByRoleIDs,
  roleList
} from '@/api/system'
import moment from 'moment'
import BarsChart from '@/components/chart/bars-chart'

export default {
  name: 'WidgetWarningSituation',
  components: { BarsChart },
  layout: 'staticPage',
  mixins: [listMixins],
  data() {
    return {
      tabActive: '0',
      searchForm: {
        productionLineName: [],
        mode: 1,
        dateTime: [
          moment()
            .subtract(1, 'day')
            .format('yyyy-MM-DD') + ' 08:00:00',
          moment().format('yyyy-MM-DD') + ' 08:00:00'
        ]
      },
      mode: 1,
      url: {
        list: getDoneRateByRoleIDs
      },
      tableData: [],
      moduleList: [],
      roleList: [],
      chartData: {
        chart: [],
        chart1: [],
        chart2: [],
        xData: []
      }
    }
  },
  computed: {
    filterRoleList: function() {
      if (!this.searchForm.productionLineName.length) return this.roleList
      const arr = []
      this.searchForm.productionLineName.forEach(item => {
        arr.push(
          ...this.roleList.filter(role => role.roleName.indexOf(item) !== -1)
        )
      })
      return arr
    }
  },
  watch: {
    'searchForm.dateTime': {
      handler: function() {
        if (this.searchForm.dateTime && this.searchForm.dateTime.length) {
          this.searchForm.startTime = this.searchForm.dateTime[0]
          this.searchForm.endTime = this.searchForm.dateTime[1]
        } else {
          this.searchForm.startTime = ''
          this.searchForm.endTime = ''
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        // this.searchForm.moduleCodes = res.data.map(item => item.ID)
      })
      post(roleList, {
        pageIndex: 1,
        pageSize: 1000,
        roleType: 2,
        productionLineName: this.searchForm.productionLineName
      }).then(res => {
        this.roleList = res.data.content || []
      })
    },
    async beforeHandleSearch() {
      // if (
      //   this.searchForm.mode === 1 &&
      //   (!this.searchForm.roleIDs || !this.searchForm.roleIDs.length) &&
      //   (!this.searchForm.productionLineName ||
      //     !this.searchForm.productionLineName.length)
      // ) {
      //   this.$message({
      //     message: '请选择产线或角色！',
      //     type: 'warning',
      //     duration: 1000
      //   })
      //   return new Promise(resolve => resolve(false))
      // }
      if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.$message.warning({
          message: '请选择时间范围！',
          type: 'warning',
          duration: 1000
        })
        return new Promise(resolve => resolve(false))
      }
      return new Promise(resolve => resolve(true))
    },
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      // 搜索
      this.loading = true
      const { data } = await post(
        this.searchForm.mode === 1 ? this.url.list : getDoneRateByModuleCodes,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data
      this.mode = this.searchForm.mode
      this.loading = false
      if (this.searchForm.mode === 1) {
        this.chartData.xData = this.tableData.map(item => item.roleName)
        this.chartData.chart2 = [
          {
            name: '完成率',
            data: this.tableData.map(item => item.rate)
          }
        ]
        this.chartData.chart = [
          {
            name: '已处理',
            data: this.tableData.map(item => item.done)
          },
          {
            name: '未处理',
            data: this.tableData.map(item => item.done)
          }
        ]
      } else {
        this.chartData.xData = this.tableData.map(item => item.roleName)
        this.chartData.chart2 = [
          {
            name: '完成率',
            data: this.tableData.map(item => item.rate)
          }
        ]
        this.chartData.chart = [
          {
            name: '一级报警',
            data: this.tableData.map(item => item.firstLevel)
          },
          {
            name: '二级报警',
            data: this.tableData.map(item => item.secondLevel)
          },
          {
            name: '三级报警',
            data: this.tableData.map(item => item.thirdLevel)
          }
        ]
      }
    },
    handleExport() {
      // if (
      //   this.searchForm.mode === 1 &&
      //   (!this.searchForm.roleIDs || !this.searchForm.roleIDs.length) &&
      //   (!this.searchForm.productionLineName ||
      //     !this.searchForm.productionLineName.length)
      // ) {
      //   this.$message({
      //     message: '请选择产线或角色！',
      //     type: 'warning',
      //     duration: 1000
      //   })
      //   return new Promise(resolve => resolve(false))
      // }
      if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.$message.warning({
          message: '请选择时间范围！',
          type: 'warning',
          duration: 1000
        })
        return new Promise(resolve => resolve(false))
      }
      this.loading = true
      post(
        this.searchForm.mode === 1
          ? exportDoneRate
          : exportDoneRateByModuleCodes,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警消息.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    exportDetail(row) {
      // if (
      //   this.searchForm.mode === 1 &&
      //   (!this.searchForm.roleIDs || !this.searchForm.roleIDs.length) &&
      //   (!this.searchForm.productionLineName ||
      //     !this.searchForm.productionLineName.length)
      // ) {
      //   this.$message({
      //     message: '请选择产线或角色！',
      //     type: 'warning',
      //     duration: 1000
      //   })
      //   return new Promise(resolve => resolve(false))
      // }
      if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.$message.warning({
          message: '请选择时间范围！',
          type: 'warning',
          duration: 1000
        })
        return new Promise(resolve => resolve(false))
      }
      this.loading = true
      post(
        this.searchForm.mode === 1
          ? exportAlertInfoByRoleIDs
          : exportAlertInfoByModuleCodes,
        Object.assign(
          {},
          this.searchForm,
          this.searchForm.mode === 1
            ? {
                roleIDs: row.roleID ? [row.roleID] : []
              }
            : {
                moduleCodes: [row.moduleCode]
              }
        ),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '角色报警详情.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    handleWarning(data) {
      // window.parent.postMessage(
      //   {
      //     type: 'link',
      //     url: data.warningEventBody.alertUrl,
      //     title: 'data.warningEventBody.title'
      //   },
      //   '*'
      // )
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
/deep/ .el-radio {
  margin-right: 10px;
}
.content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-operate {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
    .operate-icon {
      margin-left: 8px;
    }
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
.page-content {
  padding: 10px 5px;
}
</style>
