<template>
  <div>
    <el-dialog
      :top="'2%'"
      :visible.sync="navVisible"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      :width="'98%'"
      :show-close="false"
      :append-to-body="true"
      lock-scroll
      class="hmi-dialog"
      @close="navVisible = false">
      <div
        slot="title"
        class="header">
        <div class="title">
          智慧看板
        </div>
        <div class="oper">
          <el-tooltip
            class="item"
            effect="dark"
            content="关闭"
            placement="top">
            <i
              class="el-icon-close"
              @click="navVisible = false"
            />
          </el-tooltip>
        </div>
      </div>
      <div class="nav-wrapper">
        <div class="nav-title">
          <img
            src="../../assets/images/screen/screen-nav-title.png"
            alt=""
          >
        </div>
        <div class="nav-list-wrapper">
          <el-row :gutter="60">
            <el-col :span="6">
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
              <div class="nav-list">
                <div class="nav-item">
                  测试
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { findBasicDataConfigByType } from '@/api/system'
import { post } from '@/lib/Util'

export default {
  name: 'ScreenNav',
  data: () => {
    return {
      navVisible: true,
      serviceList: [],
      navList: [
        {
          name: '智慧生产',
          imgSrc: require('../../assets/desktop/nav/zhsc.png'),
          left: '15%',
          right: 0,
          top: '28%',
          serviceName: ''
        },
        {
          name: '智慧质量',
          imgSrc: require('../../assets/desktop/nav/zhzl.png'),
          left: 'auto',
          right: '15%',
          top: '28%',
          serviceName: ''
        },
        {
          name: '智慧能源',
          imgSrc: require('../../assets/desktop/nav/zhny.png'),
          left: '5%',
          right: 0,
          top: '45%',
          serviceName: ''
        },
        {
          name: '智慧成本',
          imgSrc: require('../../assets/desktop/nav/zhcb.png'),
          left: 'auto',
          right: '5%',
          top: '45%',
          serviceName: ''
        },
        {
          name: '智慧运维',
          imgSrc: require('../../assets/desktop/nav/zhyw.png'),
          left: '10%',
          right: '',
          top: '62%',
          serviceName: ''
        },
        {
          name: '协同运管',
          imgSrc: require('../../assets/desktop/nav/xtyg.png'),
          left: '',
          right: '10%',
          top: '62%',
          serviceName: ''
        },
        {
          name: '生产晨会看板',
          imgSrc: require('../../assets/desktop/nav/zhkb.png'),
          left: '27%',
          right: '',
          top: '79%',
          serviceName: 'kpi'
        },
        {
          name: '页面访问统计看板',
          imgSrc: require('../../assets/desktop/nav/xtgl.png'),
          left: '',
          right: '27%',
          top: '79%',
          serviceName: 'res'
        }
      ]
    }
  },
  computed: {
    ...mapState('menu', ['allMenus'])
  },
  mounted() {
    this.findBasicDataConfigByType()
  },
  methods: {
    openPage(nav) {
      if (!nav.serviceName) {
        return this.$message.warning('未获取到菜单配置，请联系管理员')
      }
      const matchMenu = this.allMenus.find(
        item => item.name === nav.name && item.serviceName === nav.serviceName
      )
      if (!matchMenu) {
        return this.$message.warning('您暂无权限查看此模块')
      }
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
      this.navList.forEach(item => {
        const matchService = this.serviceList.find(
          service => service.cname === item.name
        )
        if (matchService) {
          item.serviceName = matchService.name
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.nav-wrapper {
  .nav-list-wrapper {
  }
}

.hmi-dialog {
  /deep/ .el-dialog {
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
    background: transparent;
  }
  /deep/ .el-dialog__header {
    padding: 0;
  }
  /deep/ .el-dialog__body {
    height: 90vh;
    background-color: #cbd0e3;
    padding: 15px;
  }
  .header {
    padding: 4px 12px;
    height: 36px;
    background-color: #13356f;
    color: #fff;
    font-size: 18px;
    letter-spacing: 1px;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    > div {
    }
    .oper {
      > i {
        margin-right: 4px;
        cursor: pointer;
      }
    }
  }
}
.iframe-wrapper {
  height: 100%;
}
</style>
