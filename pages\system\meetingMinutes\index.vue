<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="search-wrapper">
        <el-form
          ref="form"
          :label-width="'80px'"
          :model="searchForm"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label="会议内容"
            prop="nickname"
          >
            <el-input
              v-model="searchForm.content"
              clearable
              size="small"
              placeholder="请输入会议内容"
              style="width: 130px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="主题"
            prop="nickname"
          >
            <el-input
              v-model="searchForm.title"
              clearable
              size="small"
              placeholder="请输入主题"
              style="width: 130px"
              type="text"
            />
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button
          icon="ios-search"
          type="primary"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button
          v-command="'/system/meetingMinutes/edit'"
          icon="el-icon-circle-plus-outline"
          type="success"
          @click="handleAdd"
        >新增
        </el-button>
      </div>
    </div>
    <div
      class="page-card shadow-light">
      <el-table
        v-loading="loading"
        :data="serviceList"
        :size="size"
        class="custom-table"
        border
        style="width: 100%; background-color: #eee"
      >
        <el-table-column
          label="序号"
          type="index"
          width="180"
        />
        <el-table-column
          label="组织部门"
          prop="hostOrgName"/>
        <el-table-column
          label="主持人"
          prop="host"/>
        <el-table-column
          label="主题"
          prop="title"/>
        <el-table-column
          label="会议内容"
          prop="content"/>
        <el-table-column
          label="备注"
          prop="remark"/>
        <el-table-column
          label="附件"
          prop="attachID">
          <template
            v-slot="{row}"
          >
            <template v-for="item in row.attachList">
              <i
                :key="item.id"
                :title="item.name"
                class="play-icon el-icon-reading"
                @click="showVideo(item)"/>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="270"
        >
          <template
            v-slot="{row}"
          >
            <span>
              <el-button
                slot="reference"
                type="text"
                @click="handleEdit(row)"
              >修改
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-row
        align="middle"
        class="table-pagination"
        justify="end"
        type="flex"
      >
        <el-pagination
          :current-page="page.pageIndex"
          :page-size="page.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </div>
    <Edit
      ref="modalForm"
      @success="handleSearch"
      @show="showVideo"
    />
    <el-dialog
      v-el-drag-dialog
      v-if="popVisible"
      :title="'预览会议纪要'"
      :top="'20px'"
      :width="'1200px'"
      :visible.sync="popVisible"
      v-bind="$attrs"
      :destroy-on-close="true"
      v-on="$listeners"
    >
      <pdf-view :id="popVideoId"/>
    </el-dialog>
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import pdf from 'vue-pdf'
import {
  meetingRecordPage,
  deleteImg,
  downloadFileById,
  listImg
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import uuid from 'uuid'
import { imgIp } from '@/config'
import { pdfIp } from '~/config'
import PdfView from '~/components/pdfView'

export default {
  layout: 'menuLayout',
  name: 'system-promotionPdf',
  components: {
    PdfView,
    Edit,
    pdf
  },
  mixins: [listMixins],
  data: () => {
    return {
      relatedId: '49d9a2cc-9b4a-4f7c-987a-4e3271c46884',
      popVisible: false,
      popVideoId: '',
      serviceList: [],
      //检索内容
      searchForm: {
        content: '',
        title: ''
      },
      //分页
      page: {
        pageIndex: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    showSrc: function() {
      return pdfIp + 'attach/downloadFileById/' + this.popVideoId
    }
  },
  async created() {
    this.handleSearch()
  },
  methods: {
    async handleSearch(reset = false) {
      post(meetingRecordPage, {
        content: this.searchForm.content,
        title: this.searchForm.title,
        pageIndex: this.page.pageIndex,
        pageSize: this.page.pageSize
      }).then(res => {
        this.serviceList = res.data.content
        this.page.total = res.data.totalElements
        console.log(this.serviceList)
      })
    },
    showVideo(item) {
      console.log('id==', item.path)
      const extension = item.name
        .split('.')
        .pop()
        .toLowerCase()
      if (extension === 'pdf') {
        this.popVideoId = item.id
        this.popVisible = true
      } else {
        this.downloadFile(item.path)
      }
    },
    // 分页
    // 每页显示的条数
    handleSizeChange(val) {
      this.page.pageSize = val
      this.page.pageIndex = 1
      this.handleSearch()
    },
    // 显示第几页
    handleCurrentChange(val) {
      this.page.pageIndex = val
      this.handleSearch()
    },
    //下载文件
    downloadFile(url) {
      const link = document.createElement('a')
      link.href = url
      link.download = '' // 空字符串表示浏览器会根据 URL 自动命名
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.gray {
  background-color: #999;
}
.play-icon {
  font-size: 26px;
  cursor: pointer;
  margin-right: 10px;
}
.video-box {
  padding-top: 55%;
  position: relative;
  video {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
  }
}
</style>
