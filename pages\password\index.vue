<template>
  <div class="content">
    <div class="form-con">
      <h3 class="text-center form-tit">修改密码</h3>
      <el-form
        ref="loginForm"
        :model="formData"
        :rules="rules"
        @keydown.enter.native="changePassword">
        <el-form-item prop="loginName">
          <el-input
            v-model="formData.userNo"
            placeholder="请输入用户名"
            size="large"
            readonly
          >
            <template slot="prepend">
              <i class="el-icon-user"/>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="newPassword">
          <el-input
            v-model="formData.newPassword"
            placeholder="请输入新密码"
            size="large"
            type="password">
            <template slot="prepend">
              <i class="el-icon-lock"/>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="rePassword">
          <el-input
            v-model="formData.rePassword"
            placeholder="请再次输入新密码"
            size="large"
            type="password">
            <template slot="prepend">
              <i class="el-icon-lock"/>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            :loading="btnLoading"
            class="width-full"
            long
            size="large"
            type="primary"
            style="width: 100%"
            @click="changePassword">确认</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { post } from '@/lib/Util'
import { updatePwd } from '@/api/system'
import MD5 from 'md5'

export default {
  layout: 'logoLayout',
  name: 'Password',
  filters: {},
  props: {},
  data() {
    const pwdCheck = async (rule, value, callback) => {
      let reg = /^(?=.*?[0-9]).{6,16}$/
      if (value.length < 6) {
        this.changeFlag = 2
        return callback(new Error('密码不能少于6位！'))
      } else if (value.length > 16) {
        this.changeFlag = 2
        return callback(new Error('密码最长不能超过16位！'))
      } else if (!reg.test(value)) {
        this.changeFlag = 2
        return callback(
          new Error('密码输入有误，请输入6—16位数字、字母或特殊字符！')
        )
      } else {
        this.changeFlag = 1
        callback()
      }
    }
    // 重复密码验证
    const pwdAgainCheck = async (rule, value, callback) => {
      if (value.length < 1) {
        this.changeAgainFlag = 2
        return callback(new Error('重复密码不能为空！'))
      } else if (this.formData.newPassword !== this.formData.rePassword) {
        this.changeAgainFlag = 2
        return callback(new Error('两次输入密码不一致！'))
      } else {
        this.changeAgainFlag = 1
        callback()
      }
    }
    return {
      changeFlag: 0,
      changeAgainFlag: 0,
      btnLoading: false,
      formData: {
        userNo: localStorage.getItem('userId'),
        newPassword: '',
        rePassword: ''
      },
      rules: {
        newPassword: [
          { required: true, validator: pwdCheck, trigger: 'change' }
        ],
        rePassword: [
          { required: true, validator: pwdAgainCheck, trigger: 'change' }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    // this.$Spin.hide()
  },
  methods: {
    // ...mapActions(['handleLogin']),
    // 提交
    changePassword() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          post(updatePwd, {
            userNo: this.formData.userNo,
            password: MD5(this.formData.newPassword)
          }).then(res => {
            if (res.success) {
              this.$router.replace('/')
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.center {
  text-align: center;
}
/deep/ .el-input-group__prepend {
  font-size: 26px;
  padding: 0 10px;
}
/deep/ .el-input__inner {
  height: 50px;
}
/deep/ .el-button {
  height: 50px;
  font-size: 16px;
}
.content {
  width: 424px;
  .form-tit {
    margin-bottom: 25px;
  }
  .form-con {
    margin: 32px 0;
    width: 424px;
    background: #fff;
    padding: 32px 32px;
    border-radius: 8px;
    .ivu-input {
      border: 1px solid #e8e8ee;
      border-radius: 4px;
      font-size: 16px;
      color: #525252;
      padding: 0 20px;
      &:focus {
        border: 1px solid #0097f6;
        box-shadow: none;
      }
    }
    .ivu-form-item-error-tip {
      height: 30px;
      line-height: 30px;
      padding: 0;
      color: #f66;
      top: 90%;
    }
    .ivu-form-item {
      margin-bottom: 22px;
    }
    .remember {
      margin: -10px 0 10px;
    }
    .ivu-input,
    .ivu-btn {
      height: 48px;
    }
    .ivu-btn {
      font-size: 16px;
    }
    .ivu-input-group-prepend {
      padding: 4px 15px;
    }
    .code-input {
      width: 172px;
    }
    .codeUrl {
      height: 80%;
      position: absolute;
      z-index: 3;
      top: 0;
      bottom: 0;
      margin: auto;
      right: 24px;
      border-radius: 0 4px 4px 0;
      cursor: pointer;
    }
  }
}
</style>
