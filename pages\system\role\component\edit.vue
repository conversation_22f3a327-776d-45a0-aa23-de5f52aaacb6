<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '角色'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="160px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="角色编码"
          prop="roleCode"
        >
          <el-input
            v-model="formData.roleCode"
            :maxlength="25"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入角色编码"
          />
        </el-form-item>
        <el-form-item
          label="角色名称"
          prop="roleName"
        >
          <el-input
            v-model="formData.roleName"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入角色名称"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="status"
        >
          <el-switch
            v-model.number="formData.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item
          :rules="[{ required: true, message: '选择角色类型' }]"
          label="角色类型"
          prop="roleType"
        >
          <el-select
            v-model="formData.roleType"
            size="small"
            placeholder="选择角色类型"
          >
            <el-option
              v-for="(item, index) in roleTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <template>
          <el-form-item
            label="所属机构"
            prop="orgID"
          >
            <el-select
              v-model="formData.orgID"
              size="small"
              multiple
              placeholder="选择所属机构"
            >
              <el-option
                v-for="(item, index) in orgList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="模块"
            prop="moduleCode"
          >
            <el-select
              v-model="formData.moduleCode"
              size="small"
              clearable
              filterable
              multiple
              placeholder="选择模块"
            >
              <el-option
                v-for="(item, index) in moduleList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </template>
        <el-form-item
          label="描述"
          prop="desc"
        >
          <el-input
            v-model="formData.desc"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入所属组织"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit } from '@/api/system'
import * as _ from 'lodash'
import { post } from '@/lib/Util'

export default {
  components: {},
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    orgList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    },
    roleTypeList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      url: {
        edit: roleEdit,
        add: roleAdd
      },
      formData: {},
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      rules: {
        roleCode: [
          {
            required: true,
            message: '请输入角色编号',
            trigger: 'blur'
          }
        ],
        roleName: [
          {
            required: true,
            message: '请输入角色名称',
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择角色状态',
            type: 'number',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('角色编辑页面')
  },
  methods: {
    afterClear() {
      this.$nextTick(() => {
        this.formData.status = 1
      })
    },
    typeChange(e) {
      if (e === 1) {
        this.formData.moduleName = null
        this.formData.orgID = null
      }
    },
    /**
     * 开启编辑
     * @param data 编辑元数据
     */
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, data, {
        orgID: data.orgID ? data.orgID.split(',') : [],
        moduleCode: data.moduleCode ? data.moduleCode.split(',') : []
      })
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            const params = _.cloneDeep(this.formData)
            params.orgID = params.orgID ? params.orgID.join(',') : ''
            params.moduleCode = params.moduleCode
              ? params.moduleCode.join(',')
              : ''
            post(this.url.edit, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            const params = _.cloneDeep(this.formData)
            params.orgID = params.orgID ? params.orgID.join(',') : ''
            params.moduleCode = params.moduleCode
              ? params.moduleCode.join(',')
              : ''
            post(this.url.add, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
