<template>
  <div class="page-wrapper">
    <nuxt class="nuxt-view"/>
  </div>
</template>

<script>
export default {
  head() {
    return {}
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  beforeCreate() {},
  created() {},
  mounted() {},
  methods: {}
}
</script>
<style scoped lang="less">
body,
html {
  height: 100%;
  width: 100%;
}
.page-wrapper {
  height: 100vh;
  //padding: 10px; 暂时注释
  background: #f8f8f8;
}
</style>
