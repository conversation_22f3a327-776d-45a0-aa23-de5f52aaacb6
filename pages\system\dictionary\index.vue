<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keydown.enter.native.prevent.stop="handleSearch(true)"
          >
            <el-form-item
              label="字典名："
              prop="module"
            >
              <el-input
                v-model="searchForm.name"
                placeholder="请输入关键字" />
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="字典名"
            prop="name"
            show-overflow-tooltip
          />
          <el-table-column
            label="编码"
            prop="code"
            width="150"
          />
          <el-table-column
            label="描述"
            prop="description"
          />
          <el-table-column
            fixed="right"
            label="操作"
            style="white-space: nowrap"
          >
            <template
              slot-scope="{row, $index}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail(row)"
                >详情
                </el-button>
              </span>
              <template>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :ques-type="searchForm.quesType"
      :id="activeId"
      :code="activeCode"
      @success="handleSearch"
    />
    <Edit
      ref="modalForm"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDelete,
  dictionaryDtlFindByDictCode,
  dictionaryFind,
  dictionarySave,
  matterTop,
  weeklySummaryDelete,
  weeklySummaryFind,
  weeklySummarySave
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Detail from './component/detail'

export default {
  layout: 'menuLayout',
  name: 'system-dictionary',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {},
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: dictionaryFind, //分页接口地址
        delete: dictionaryDelete, //删除接口地址
        save: dictionarySave,
        getDict: dictionaryDtlFindByDictCode
      },
      moduleList: [],
      workList: ENUM.workList,
      userId: null,
      editRole: null,
      serviceList: [],
      timer: null,
      activeId: '',
      activeCode: '',
      mode: 'daterange'
    }
  },
  computed: {},
  async created() {
    this.handleSearch(true)
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(row) {
      this.activeId = row.id
      this.activeCode = row.code
      this.$refs.modalDetailForm.visible = true
      this.$nextTick(() => {
        this.$refs.modalDetailForm.handleSearch(true)
      })
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
