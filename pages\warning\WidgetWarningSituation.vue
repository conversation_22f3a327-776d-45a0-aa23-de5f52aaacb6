<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="统计维度"
              prop="ruleName"
            >
              <el-radio
                v-model="searchForm.mode"
                :label="1"
                border
                @change="handleSearch"
              >角色</el-radio>
              <el-radio
                v-model="searchForm.mode"
                :label="2"
                border
                @change="handleSearch"
              >模块</el-radio>
            </el-form-item>
            <template
              v-if="searchForm.mode === 1">
              <el-form-item
                label="产线"
                prop="ruleName"
              >
                <el-select
                  v-model="searchForm.productionLineName"
                  size="small"
                  multiple
                  placeholder="选择产线"
                  style="width: 120px"
                >
                  <el-option
                    :label="'第一炼钢厂'"
                    :value="'第一炼钢厂'"
                  />
                  <el-option
                    :label="'宽厚板厂'"
                    :value="'宽厚板厂'"
                  />
                  <el-option
                    :label="'板卷厂'"
                    :value="'板卷厂'"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="角色"
                prop="ruleName"
              >
                <el-select
                  v-model="searchForm.roleIDs"
                  size="small"
                  multiple
                  collapse-tags
                  placeholder="选择角色"
                  style="width: 160px"
                >
                  <el-option
                    v-for="(item, index) in filterRoleList"
                    :key="index"
                    :label="item.roleName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="报警内容"
                prop="alertContent"
              >
                <el-input
                  v-model="searchForm.alertContent"
                  clearable
                  size="small"
                  placeholder="报警内容"
                  style="width: 130px"
                  type="text"
                />
              </el-form-item>
            </template>
            <el-form-item
              label="时间"
              prop="dateTime"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                :picker-options="pickerOptions"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right"
                style="width: 320px"/>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>

          <el-button
            type="primary"
            @click="exportDetail">详情导出</el-button>

          <el-button
            icon="ios-search"
            type="primary"
            @click="handleExport"
          >导出
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">

        <el-tabs
          v-model="tabActive"
          @tab-click="banzuTab">
          <el-tab-pane
            :name="'0'"
            label="处理统计表">
            <br>
            <el-table
              :data="tableData"
              size="small"
              border
              height="calc(100vh - 274px)"
              style="width: 100%"
            >
              <el-table-column
                :label="mode === 1 ? '角色名称' : '模块名称'"
                sortable
                prop="roleName"
              />
              <el-table-column
                label="报警总数"
                sortable
                prop="total"
              />
              <el-table-column
                label="已处理"
                sortable
                prop="done"
              />
              <el-table-column
                label="未处理"
                prop="unDone"
                sortable
              >
                <template v-slot="{ row }">
                  <span
                    style="text-decoration: underline; cursor: pointer;color: #4458fe"
                    @click="showHistory(row.roleID)">{{ row.unDone }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="不标准建议数"
                prop="adviceAtypia"
                sortable
                min-width="100"
              />
              <el-table-column
                label="不标准分析数"
                prop="analysisAtypia"
                sortable
                min-width="100"
              />
              <el-table-column
                label="误报"
                prop="isFalse"
                sortable
                min-width="100"
              />
              <el-table-column
                label="处置数量"
                prop="alertAdviceCount"
                sortable
                min-width="100"
              />
              <el-table-column
                label="完成率"
                sortable
                prop="rate"
              >
                <template v-slot="{ row }">
                  {{ row.rate }}%
                </template>
              </el-table-column>
              <el-table-column
                label="2小时内处理完"
                sortable
                prop="twoHourDone"
                min-width="100"
              />
              <el-table-column
                label="8小时内处理完"
                sortable
                prop="eightHourDone"
                min-width="100"
              />
              <el-table-column
                label="8小时内未处理完"
                sortable
                prop="gtEightHourDone"
                min-width="120"
              />
              <el-table-column
                label="操作"
                prop="createDateTime"
                width="80"
              >
                <template v-slot="{ row }">
                  <el-button
                    type="text"
                    @click="exportDetail(row)">详情导出</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane
            :name="'1'"
            label="统计图">
            <div
              v-if="tabActive == 1"
              style="height: 450px">
              <pie-chart :chart-data="chartData.chart3"/>
          </div></el-tab-pane>
          <el-tab-pane
            :name="'2'"
            label="班组统计"
          >
            <div
              v-if="tabActive == 2"
              style="height: 100%"
            >
              <el-row :gutter="40">
                <br>
                <el-col :span="12">
                  <el-table
                    :data="tableDatabanzu"
                    highlight-current-row
                    size="small"
                    border
                    style="width: 100%; overflow-y: scroll"
                    @current-change="getData"
                  >
                    <el-table-column
                      label="所属模块"
                      prop="moduleName"
                      width="75"
                    />
                    <el-table-column
                      label="报警描述"
                      prop="alertContent"
                      min-width="100"
                    />
                    <el-table-column
                      label="报警时间"
                      prop="createDateTime"
                      width="135"
                    />
                    <!--                    <el-table-column
                      label="操作"
                      prop="createDateTime"
                      width="80"
                    >
                      <template v-slot="{ row }">
                        <el-button
                          type="text"
                          @click="showTeam(row.warningRuleID)">查看</el-button>
                      </template>
                    </el-table-column>-->
                  </el-table>
                  <el-row
                    align="middle"
                    class="table-pagination"
                    justify="end"
                    type="flex"
                  >
                    <el-pagination
                      :current-page="page.pageIndex"
                      :page-size="page.pageSize"
                      :page-sizes="[10, 20, 30, 40]"
                      :total="page.total"
                      layout="total, sizes, prev, pager, next, jumper"
                      background
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                    />
                  </el-row>
                </el-col>
                <el-col :span="12">
                  <div style="height: 640px; width: auto">
                    <bars-chart
                      :key="0"
                      :chart-data="dataOption.series"
                      :x-data="dataOption.xAxis[0].data"
                      :bar-width="64"
                      :show-label="true"
                    />
                  </div>
                </el-col>
              </el-row>
          </div></el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <warning-info-detail
      ref="modalDetailForm"
      :id="popHistory.id"/>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import {
  exportAlertInfoByModuleCodes,
  exportAlertInfoByRoleIDs,
  exportDoneRate,
  exportDoneRateByModuleCodes,
  findFinishSummary,
  findModuleInfoList,
  getDoneRateByModuleCodes,
  getDoneRateByRoleIDs,
  roleList,
  findTrendByWaringRuleIDAndTime,
  WarningInfoList
} from '@/api/system'
import moment from 'moment'
import BarsChart from '@/components/chart/bars-chart'
import WarningInfoDetail from '@/pages/warning/component/warningInfoDetail'
import PieChart from '@/pages/visit10/screen/component/pie-chart'

export default {
  name: 'WidgetWarningSituation',
  components: { PieChart, WarningInfoDetail, BarsChart },
  layout: 'menuLayout',
  data() {
    return {
      // 班组统计
      dataOption: {
        xAxis: [
          {
            type: 'category',
            data: []
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            data: [],
            name: '报警次数'
          }
        ]
      },
      tabActive: '0',
      searchForm: {
        productionLineName: [],
        mode: 1,
        dateTime: [
          moment()
            .subtract(1, 'day')
            .format('yyyy-MM-DD') + ' 08:00:00',
          moment().format('yyyy-MM-DD') + ' 08:00:00'
        ]
      },
      mode: 1,
      url: {
        list: getDoneRateByRoleIDs,
        banzu: WarningInfoList,
        tongji: findFinishSummary
      },
      page: {
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      tableData: [],
      tableDatabanzu: [],
      moduleList: [],
      roleList: [],
      chartData: {
        chart: [],
        chart1: [],
        chart2: [],
        chart3: [],
        xData: []
      },
      popHistory: {
        id: ''
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date()).format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '昨日',
            onClick(picker) {
              const end = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 23:59:59')
              )
              const start = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('isoWeek')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {
    filterRoleList: function() {
      if (!this.searchForm.productionLineName.length) return this.roleList
      const arr = []
      this.searchForm.productionLineName.forEach(item => {
        arr.push(
          ...this.roleList.filter(role => role.roleName.indexOf(item) !== -1)
        )
      })
      return arr
    }
  },
  watch: {
    'searchForm.dateTime': {
      handler: function() {
        if (this.searchForm.dateTime && this.searchForm.dateTime.length) {
          this.searchForm.startTime = this.searchForm.dateTime[0]
          this.searchForm.endTime = this.searchForm.dateTime[1]
        } else {
          this.searchForm.startTime = ''
          this.searchForm.endTime = ''
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.loadData()
    this.handleSearch()
  },
  methods: {
    // 历史报警记录
    showHistory(id) {
      this.popHistory.id = id
      this.$refs.modalDetailForm.visible = true
      this.$refs.modalDetailForm.searchForm.dateTime =
        this.searchForm.dateTime || []
      this.$refs.modalDetailForm.showHistory(id, true)
    },
    getFirstRowId() {
      if (this.tableDatabanzu.length > 0) {
        const firstRow = this.tableDatabanzu[0]
        this.getData(firstRow)
      } else {
        console.log('表格数据为空')
      }
    },
    //班组统计清空时间
    banzuTab() {
      if (this.tabActive == 2) {
        this.searchForm.dateTime = [
          moment()
            .startOf('month')
            .format('yyyy-MM-DD') + ' 00:00:00',
          moment().format('yyyy-MM-DD') + ' 23:59:59'
        ]
        post(WarningInfoList, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize,
          roleIDs: this.searchForm.roleIDs,
          warningType: 5
        }).then(res => {
          this.tableDatabanzu = res.data.content
          this.page.total = res.data.totalElements
          this.handleSearch()
          this.getFirstRowId()
        })
      }
    },
    // 班组统计柱图
    getData(row) {
      if (row == null || row.warningRuleID == null) {
        return
      }
      post(findTrendByWaringRuleIDAndTime, {
        warningRuleID: row.warningRuleID
      }).then(res => {
        // 解析数据
        const xData = res.data.team.map(team => team.name)
        const yData = res.data.team.map(team => team.count)
        // 更新数据选项
        this.dataOption.xAxis[0].data = xData
        this.dataOption.series[0].data = yData
      })
    },
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        // this.searchForm.moduleCodes = res.data.map(item => item.ID)
      })
      post(roleList, {
        pageIndex: 1,
        pageSize: 1000,
        roleType: 2,
        productionLineName: this.searchForm.productionLineName
      }).then(res => {
        this.roleList = res.data.content || []
      })
    },
    async beforeHandleSearch() {
      if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.$message.warning({
          message: '请选择时间范围！',
          type: 'warning',
          duration: 1000
        })
        return new Promise(resolve => resolve(false))
      }
      return new Promise(resolve => resolve(true))
    },
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list || !this.url.banzu) {
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      // 搜索
      this.loading = true
      const { data } = await post(
        this.searchForm.mode === 1 ? this.url.list : getDoneRateByModuleCodes,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      this.tableData = data
      this.mode = this.searchForm.mode
      this.loading = false
      // 查询处理时间统计
      await post(this.url.tongji, {
        startTime: this.searchForm.startTime,
        endTime: this.searchForm.endTime,
        roleIDs: this.searchForm.roleIDs ? this.searchForm.roleIDs : [],
        productionLineName: this.searchForm.productionLineName
          ? this.searchForm.productionLineName
          : []
      }).then(res => {
        this.chartData.chart3 = [
          {
            value: res.data.twoHourDone,
            name: '2小时内处理',
            label: { formatter: ['2小时内处理：{c}条({d}%)'].join('\n') }
          },
          {
            value: res.data.eightHourDone,
            name: '2-8小时处理',
            label: { formatter: ['2-8小时处理：{c}条({d}%)'].join('\n') }
          },
          {
            value: res.data.gtEightHourDone,
            name: '8小时以上处理',
            label: { formatter: ['8小时以上处理：{c}条({d}%)'].join('\n') }
          }
        ]
      })
      //班组统计表格
      if (this.tabActive == 2) {
        post(WarningInfoList, {
          startTime: this.searchForm.startTime,
          endTime: this.searchForm.endTime,
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize,
          roleIDs: this.searchForm.roleIDs[0],
          alertContent: this.searchForm.alertContent,
          warningType: 5
        }).then(res => {
          this.tableDatabanzu = res.data.content
          this.page.total = res.data.totalElements
          this.getFirstRowId()
        })
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.pageSize = val
      this.handleSearch()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.pageIndex = val
      this.handleSearch()
    },
    handleExport() {
      /*if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.$message.warning({
          message: '请选择时间范围！',
          type: 'warning',
          duration: 1000
        })
        return new Promise(resolve => resolve(false))
      }*/
      this.loading = true
      post(
        this.searchForm.mode === 1
          ? exportDoneRate
          : exportDoneRateByModuleCodes,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警消息.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    exportDetail(row) {
      if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.$message.warning({
          message: '请选择时间范围！',
          type: 'warning',
          duration: 1000
        })
        return new Promise(resolve => resolve(false))
      }
      this.loading = true
      post(
        this.searchForm.mode === 1
          ? exportAlertInfoByRoleIDs
          : exportAlertInfoByModuleCodes,
        Object.assign(
          {},
          this.searchForm,
          this.searchForm.mode === 1
            ? {
                roleIDs: row.roleID ? [row.roleID] : []
              }
            : {
                moduleCodes: row.moduleCode ? [row.moduleCode] : []
              }
        ),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '角色报警详情.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
/deep/ .el-radio {
  margin-right: 10px;
}
.content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-operate {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
    .operate-icon {
      margin-left: 8px;
    }
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
