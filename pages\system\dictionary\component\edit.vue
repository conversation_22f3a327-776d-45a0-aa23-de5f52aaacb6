<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '字典'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入字典名',
              trigger: 'change'
            }
          ]"
          label="字典名"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            placeholder="请输入字典名" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入编码',
              trigger: 'change'
            }
          ]"
          label="编码"
          prop="code"
        >
          <el-input
            v-model="formData.code"
            placeholder="请输入编码" />
        </el-form-item>
        <el-form-item
          label="描述"
          prop="description"
        >
          <el-input
            v-model="formData.description"
            placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  dictionarySave,
  matterSave,
  saveFeedback,
  uploadFile,
  weeklySummarySave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: dictionarySave,
        add: dictionarySave,
        file: uploadFile
      },
      moduleList: ENUM.moduleList,
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    weekChange() {
      this.$nextTick(() => {
        this.formData.weekDate = this.$moment(this.formData.weekDate)
          .startOf('isoWeek')
          .add(4, 'days')
          .format('yyyy-MM-DD')
      })
    },
    async handelUpload() {
      // 删除图片
      if (this.deleteIds.length) {
        const del = await post(deleteFileByIds, { ids: this.deleteIds })
      }
      //上传
      if (!this.$refs.upload.uploadFiles.length) return
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      const res = await post(this.url.file, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (res.success) {
        this.uploadFiles = res.data
        return Promise.resolve(true)
      } else {
        this.$message.warning('图片上传失败！')
        this.loading = false
        return Promise.resolve(false)
      }
      return Promise.reject(false)
    },
    httpRequest(params) {},
    submitBefore() {},
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
