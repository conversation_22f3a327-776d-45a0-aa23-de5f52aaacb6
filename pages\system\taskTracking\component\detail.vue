<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '任务'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          label="模块名称："
          prop="serviceName"
        >
          {{ formData.serviceName }}
        </el-form-item>
        <el-form-item
          filterable
          label="任务名称："
          prop="taskName"
        >
          {{ formData.taskName }}
        </el-form-item>
        <el-form-item
          label="任务类型："
          prop="taskType"
        >
          {{ getName('taskTypeList', formData.taskType).label }}
        </el-form-item>
        <el-form-item
          label="北科负责人："
          prop="serviceUser"
        >
          {{ formData.serviceUser.split('|')[1] }}
        </el-form-item>
        <el-form-item
          label="板材负责人："
          prop="devUser"
        >
          {{ formData.devUser.split('|')[1] }}
        </el-form-item>
        <el-form-item
          label="计划完成时间："
          prop="planCompleteDate"
        >
          {{ formData.planCompleteDate }}
        </el-form-item>
        <el-form-item
          label="实际完成时间："
          prop="actualCompleteDate"
        >
          {{ formData.actualCompleteDate }}
        </el-form-item>
        <el-form-item
          label="状态："
          prop="status"
        >
          <el-tag
            :type="getName('statusList', formData.status).type"
            disable-transitions
          >{{ getName('statusList', formData.status).label }}
          </el-tag>
        </el-form-item>
        <div
          v-for="(item, index) in formData.reportList"
          :key="index"
          class="model-item"
        >
          <el-form-item
            :prop="'reportList.' + index + '.weekSubmitUser'"
            label="填报人："
          >
            {{ item.weekSubmitUser }}
          </el-form-item>
          <el-form-item
            :prop="'reportList.' + index + '.weekDate'"
            label="填报时间："
          >
            {{ item.weekDate }}
          </el-form-item>
          <el-form-item
            :prop="'reportList.' + index + '.currentProgress'"
            label="本周进展："
          >
            {{ item.weekProgress }}
          </el-form-item>
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请输入下周计划',
                trigger: 'change'
              }
            ]"
            :prop="'reportList.' + index + '.nextWeekPlan'"
            label="下周计划："
          >
            {{ item.nextWeekPlan }}
          </el-form-item>
          <el-form-item
            :prop="'reportList.' + index + '.unfinishedReason'"
            label="未完成原因："
          >
            {{ item.unfinishedReason }}
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  taskTrackingDelete,
  taskTrackingFind,
  taskTrackingSave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'
import UserSelect from '@/components/userSelect'

export default {
  components: { UserSelect, ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    taskTypeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    },
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        list: taskTrackingFind, //分页接口地址
        edit: taskTrackingSave,
        add: taskTrackingSave,
        delete: taskTrackingDelete //删除接口地址
      },
      formData: {
        modelNo: null,
        reportList: []
      },
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {
    detail: {
      deep: true,
      handler: function() {
        console.log(this.detail)
        this.formData = this.detail
        if (!this.formData.weekDate) return (this.formData.reportList = [])
        this.formData.reportList = this.formData.weekDate
          .split('|||')
          .map((item, index) => {
            return {
              unfinishedReason: this.formData.unfinishedReason
                ? this.formData.unfinishedReason.split('|||')[index]
                : '',
              weekSubmitUser: this.formData.weekSubmitUser
                ? this.formData.weekSubmitUser.split('|||')[index]
                : '',
              nextWeekPlan: this.formData.nextWeekPlan
                ? this.formData.nextWeekPlan.split('|||')[index]
                : '',
              weekProgress: this.formData.weekProgress
                ? this.formData.weekProgress.split('|||')[index]
                : '',
              weekDate: this.formData.weekDate
                ? this.formData.weekDate.split('|||')[index]
                : ''
            }
          })
      }
    }
  },
  created() {
    // console.log('')
  },
  methods: {
    addModel() {
      this.formData.reportList.push({})
    },
    deleteModel(index) {
      this.formData.reportList.splice(index, 1)
    },
    clearModel() {
      this.formData.reportList = []
    },
    weekChange() {
      this.$nextTick(() => {
        this.formData.weekDate = this.$moment(this.formData.weekDate)
          .startOf('isoWeek')
          .add(4, 'days')
          .format('yyyy-MM-DD')
      })
    },
    submitBefore() {
      // 提交前操作
      this.formData.type = 2
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {
        reportList: []
      }
    },

    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, data)
      // 处理填报
      if (!this.formData.weekDate) return (this.formData.reportList = [])
      this.formData.reportList = this.formData.weekDate
        .split('|||')
        .map((item, index) => {
          console.log(item)
          return {
            unfinishedReason: this.formData.unfinishedReason
              ? this.formData.unfinishedReason.split('|||')[index]
              : '',
            weekSubmitUser: this.formData.weekSubmitUser
              ? this.formData.weekSubmitUser.split('|||')[index]
              : '',
            nextWeekPlan: this.formData.nextWeekPlan
              ? this.formData.nextWeekPlan.split('|||')[index]
              : '',
            currentProgress: this.formData.currentProgress
              ? this.formData.currentProgress.split('|||')[index]
              : '',
            weekDate: this.formData.weekDate
              ? this.formData.weekDate.split('|||')[index]
              : ''
          }
        })
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          const params = Object.assign(this.formData, {
            unfinishedReason: this.formData.reportList
              .map(item => item.unfinishedReason)
              .join('|||'),
            weekSubmitUser: this.formData.reportList
              .map(item => item.weekSubmitUser)
              .join('|||'),
            nextWeekPlan: this.formData.reportList
              .map(item => item.nextWeekPlan)
              .join('|||'),
            currentProgress: this.formData.reportList
              .map(item => item.currentProgress)
              .join('|||'),
            weekDate: this.formData.reportList
              .map(item => item.weekDate)
              .join('|||')
          })
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            this.submitBefore()
            post(this.url.edit, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            this.submitBefore()
            post(this.url.add, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style
  scoped
  lang="less"
>
.model-item {
  border: 1px solid #dedddd;
  padding: 20px 20px 20px 0;
  margin-bottom: 10px;
}
</style>
