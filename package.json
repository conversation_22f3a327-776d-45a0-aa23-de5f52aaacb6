{"name": "nercar-web", "version": "1.0.0", "description": "My super Nuxt.js project", "author": "@awen", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development nuxt", "build": "cross-env NODE_ENV=production nuxt build", "start": "cross-env NODE_ENV=production nuxt start", "generate": "cross-env NODE_ENV=production nuxt generate", "lint": "eslint --ext .js,.vue --ignore-path .gitignore .", "precommit": "npm run lint"}, "dependencies": {"@nuxtjs/axios": "^5.0.0", "@nuxtjs/style-resources": "^1.2.1", "cross-env": "^5.2.0", "crypto-js": "^3.1.9-1", "docxtemplater": "^3.31.6", "docxtemplater-image-module-free": "^1.1.1", "echarts": "^5.4.1", "echarts-gl": "^2.0.9", "element-resize-detector": "^1.2.1", "element-ui": "^2.15.10", "event-stream": "^4.0.1", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "fuse.js": "^6.6.2", "html2canvas": "^1.0.0-rc.7", "jsencrypt": "^3.0.0-rc.1", "jspdf": "^2.1.1", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "less": "^3.11.1", "less-loader": "^5.0.0", "lodash": "^4.17.19", "md5": "^2.2.1", "moment": "^2.28.0", "nuxt": "^2.17.2", "pizzip": "^3.1.3", "qrcode": "^1.4.4", "screenfull": "^5.0.0", "uuid": "^3.4.0", "vue": "^2.6.11", "vue-grid-layout": "^2.3.12", "vue-i18n": "^8.2.1", "vue-pdf": "^4.3.0", "vue-server-renderer": "^2.6.11", "vuex-persistedstate": "^2.5.4", "xlsx": "^0.15.1"}, "devDependencies": {"babel-eslint": "^8.2.1", "compression-webpack-plugin": "^4.0.1", "eslint": "^4.0.0", "eslint-config-prettier": "^3.1.0", "eslint-loader": "^2.0.0", "eslint-plugin-prettier": "2.6.2", "eslint-plugin-vue": "^4.0.0", "jsonwebtoken": "^8.5.1", "nodemon": "^1.11.0", "nuxt-precompress": "^0.5.9", "prettier": "1.14.3", "vue-template-compiler": "^2.6.11", "webpack-node-externals": "^1.7.2"}}