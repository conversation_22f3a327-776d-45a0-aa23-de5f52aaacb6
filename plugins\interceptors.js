// interceptors.js 请求拦截器
import { logout, redirectLogin } from '@/lib/Menu'

export default ({ app }, inject) => {
  // 请求拦截器
  app.$axios.interceptors.request.use(
    config => {
      // console.log('请求拦截器', config)
      // 有需要的话可以在请求头中添加token
      // 请求头添加token
      const token = localStorage.getItem('token')
      config.headers.Authorization = 'Bearer ' + token
      return config
    },
    err => {
      console.warn(err)
      return new Promise.resolve(err)
    }
  )

  // 响应拦截器
  app.$axios.interceptors.response.use(
    response => {
      // console.log('响应拦截器：', response)
      return response
    },
    err => {
      // 返回状态码不为200时候的错误处理
      switch (err.response.status) {
        case 401:
          // token失效，删除本地token,重定向到后端登录接口
          logout(app)
          break
        case 403:
          console.warn('抱歉，您的权限受限')
          break
        default:
          break
      }
      return Promise.resolve(err)
    }
  )
  // 错误拦截器
  app.$axios.onError(error => {
    //console.log('错误拦截器：', error)
    return error
  })
}
