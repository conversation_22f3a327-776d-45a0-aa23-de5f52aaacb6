<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="role.roleName + '--权限管理'"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        label-width="120px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="角色名称："
          prop="roleName"
        >
          {{ role.roleName || '' }}
        </el-form-item>
        <el-form-item
          label="角色权限："
          prop="org"
        >
          <el-tree
            ref="tree"
            :data="data"
            :default-checked-keys="defaultSelected"
            :default-expanded-keys="defaultExpanded"
            :props="defaultProps"
            node-key="id"
            show-checkbox
          >
            <template 
              v-slot="{ node, data }" 
            >
              {{ data.type === 'plugin' ? '(小部件) -- ' : '' }}{{ node.label }}
            </template>

          </el-tree>
          
        </el-form-item>

      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  filterResourceSelected,
  relateResource,
  resourceListNoPage,
  roleById
} from '@/api/system'
import { post } from '@/lib/Util'
import { generateTree, getMenuData } from '@/lib/Menu'

export default {
  components: {},
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'role'],
  data() {
    return {
      loading: false,
      url: {},
      data: [],
      totalData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultSelected: [],
      defaultExpanded: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set() {}
    }
  },
  watch: {},
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      // 获取所有资源
      post(resourceListNoPage, {}).then(res => {
        if (res.success) {
          this.totalData = res.data
          this.data = generateTree(res.data).sort(
            (a, b) => (a.type < b.type ? -1 : 1)
          )
        }
      })
      // 获取角色下资源
      post(filterResourceSelected, { roleID: this.role.id }).then(res => {
        if (res.success) {
          this.defaultSelected = res.data
            .filter(item => item.isSelected === '1')
            .map(item => item.id)
          this.$nextTick(() => {
            res.data.filter(item => item.isSelected === '0').forEach(item => {
              this.$refs.tree && this.$refs.tree.setChecked(item.id, false)
            })
          })
        }
      })
    },
    handelConfirm() {
      this.loading = true
      const selectedList = this.$refs.tree.getCheckedNodes()
      // return
      this.handleMenu(selectedList, selectedList)
      post(relateResource, {
        id: this.role.id,
        resourceIDs: selectedList.map(item => item.id)
      }).then(res => {
        this.loading = false
        if (res.success) {
          this.$message.success('分配成功！')
          this.close()
        }
      })
    },

    // 递归添加父级级单
    handleMenu(menuData, addMenu) {
      const addList = []
      // console.log(menuData, addMenu)
      addMenu.forEach(item => {
        if (!item.parentId) return
        if (
          !menuData.find(i => {
            return i.id && i.id === item.parentId
          })
        ) {
          const match = this.totalData.find(i => i.id === item.parentId)
          match && menuData.push(match)
          match && addList.push(match)
        }
      })
      if (addList.length) {
        this.handleMenu(menuData, addList)
      }
    },

    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style scoped>
</style>
