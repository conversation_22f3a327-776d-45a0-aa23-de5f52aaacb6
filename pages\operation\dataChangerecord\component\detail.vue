<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'详情'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1000px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="0"
        size="medium"
      >
        <el-form-item
          prop="userNo"
        >
          <p style="font-size: 20px; margin-bottom: 10px">服务名称：</p>
          <el-input
            v-model="formData.serviceName"
            :disabled="true"
            type="textarea"/>
        </el-form-item>
        <el-form-item
          label=""
          prop="remark"
        >
          <p style="font-size: 20px; margin-bottom: 10px">原始数据</p>
          <el-input
            v-model="formData.originData"
            :disabled="true"
            type="textarea"
            placeholder="请输入" />
        </el-form-item>
        <el-form-item
          label=""
          prop="remark"
        >
          <p style="font-size: 20px; margin-bottom: 10px">新数据</p>
          <el-input
            v-model="formData.newData"
            :disabled="true"
            type="textarea"
            placeholder="请输入" />
        </el-form-item>
        <el-form-item
          :disabled="true"
          label=""
          prop="remark"
        >
          <p style="font-size: 20px; margin-bottom: 10px">备注</p>
          <el-input
            v-model="formData.remark"
            :disabled="true"
            type="textarea"
            placeholder="请输入" />
        </el-form-item>
      </el-form>
      <!-- <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div> -->
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { dataChangeRecordSave, dataChangeRecordUpdate } from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import UserSelect from '@/components/userSelect'

export default {
  components: { UserSelect, ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: dataChangeRecordUpdate,
        add: dataChangeRecordSave
      },
      formData: {},
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultSelected: [],
      defaultExpanded: [],
      data1: [],
      defaultProps1: {
        children: 'children',
        label: 'name'
      },
      defaultSelected1: [],
      defaultExpanded1: []
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = data
      // this.formData.serviceName = data.serviceName + '|' + data.serviceName
      // console.log('editFormData', data.id)
    },
    clearForm() {
      this.formData = {}
      this.defaultSelected1 = []
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          if (this.formData.id) {
            post(this.url.edit, {
              id: this.formData.id,
              status: 1,
              serviceName: this.formData.serviceName.split('|')[0],
              originData: this.formData.originData.split('|')[0],
              newData: this.formData.newData.split('|')[0],
              remark: this.formData.remark
              //   extraRemark: this.formData.extraRemark
            }).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('修改成功！')
                this.close()
              }
            })
          } else {
            console.log('formData', this.formData)
            post(this.url.add, {
              serviceName: this.formData.serviceName.split('|')[0],
              originData: this.formData.originData.split('|')[0],
              newData: this.formData.newData.split('|')[0]
            }).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('新增成功！')
                this.close()
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
