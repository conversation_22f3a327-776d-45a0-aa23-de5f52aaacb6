<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <!--            <el-form-item
              label="设备名称"
              prop="deviceName"
            >
              <el-input
                v-model="searchForm.deviceName"
                clearable
                size="small"
                placeholder="设备名称"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="区域名称"
              prop="ruleName"
            >
              <el-input
                v-model="searchForm.areaName"
                clearable
                size="small"
                placeholder="区域名称"
                style="width: 130px"
                type="text"
              />
            </el-form-item>-->
            <el-form-item
              label="报警内容"
              prop="alertContent"
            >
              <el-input
                v-model="searchForm.alertContent"
                clearable
                size="small"
                placeholder="报警内容"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <!--            <el-form-item
              label="角色"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.roleID"
                size="small"
                placeholder="选择角色"
                style="width: 130px"
              >
                <el-option
                  v-for="(item, index) in roleList"
                  :key="index"
                  :label="item.roleName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>-->
            <el-form-item
              label="模块"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.moduleCode"
                size="small"
                clearable
                style="width: 130px"
                placeholder="选择模块"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.Name"
                  :value="item.ID"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警类型"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.warningType"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警类型"
              >
                <el-option
                  v-for="(item, index) in warningTypeList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警级别"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.alertLevel"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警级别"
              >
                <el-option
                  v-for="(item, index) in alertLevelList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.status"
                size="small"
                clearable
                style="width: 130px"
                placeholder="状态"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="时间"
              prop="dateTime"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                :picker-options="pickerOptions"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right"
                style="width: 320px"/>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button
            type="warning"
            @click="handleMultiple()"
          >批量处理
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">

        <el-table
          :data="tableData"
          :row-class-name="getRowClass"
          size="small"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"/>
          <el-table-column
            label="所属模块"
            prop="moduleName"
            width="100"
          >
            <!--          <template-->
            <!--            v-slot="{row}"-->
            <!--          >-->
            <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
            <!--          </template>-->
          </el-table-column>
          <el-table-column
            label="报警描述"
            prop="alertContent"
            min-width="100"
          />
          <el-table-column
            label="设备名"
            show-overflow-tooltip
            prop="deviceName"
            width="180"
          />
          <el-table-column
            label="区域名"
            prop="areaName"
            width="100"
          />
          <el-table-column
            label="报警类型"
            prop="alertTypeName"
            width="95"
          />
          <el-table-column
            label="报警等级"
            prop="alertLevel"
            width="72"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.alertLevel == 1"
                type="danger">一级</el-tag>
              <el-tag
                v-if="row.alertLevel == 2"
                type="warning">二级</el-tag>
              <el-tag
                v-if="row.alertLevel == 3">三级</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            prop="status"
            width="75"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.status == 0"
                type="danger">
                未处理
              </el-tag>
              <el-tag
                v-if="row.status == 2"
                type="success">
                已处理
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="是否误报"
            prop="status"
            width="70"
          >
            <template v-slot="{row}">
              <span
                v-if="row.isFalse">
                是
              </span>
              <span
                v-else>
                否
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="处理人"
            prop="dealUser"
            width="80"
          />
          <el-table-column
            label="报警分析"
            prop="alertAnalysis"
            width="180"
          />
          <el-table-column
            label="处理意见"
            prop="alertAdvice"
            width="180"
          />
          <el-table-column
            label="报警时间"
            prop="createDateTime"
            width="135"
          />
          <el-table-column
            label="操作"
            prop="createDateTime"
            width="80"
          >
            <template v-slot="{ row }">
              <el-button
                type="text"
                @click="handleSingle(row)">处理</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <el-dialog
      v-el-drag-dialog
      :top="'3%'"
      :title="'数据修正'"
      :width="'90%'"
      :visible.sync="popHandleVisible"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >
      <el-row :gutter="40">
        <el-col :span="16">
          <div class="tit">
            报警历史记录
          </div>
          <el-table
            :data="popHistory.list"
            :max-height="'400px'"
            :row-class-name="getSameClass"
            size="small"
            stripe
            border
            style="width: 100%"
          >
            <el-table-column
              label="所属模块"
              prop="moduleName"
              width="70"
            >
              <!--          <template-->
              <!--            v-slot="{row}"-->
              <!--          >-->
              <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
              <!--          </template>-->
            </el-table-column>
            <el-table-column
              label="报警描述"
              prop="alertContent"
              min-width="100"
            />
            <el-table-column
              label="设备名"
              show-overflow-tooltip
              prop="deviceName"
              width="120"
            />
            <el-table-column
              label="区域名"
              prop="areaName"
              width="100"
            />
            <el-table-column
              label="报警类型"
              prop="alertTypeName"
              width="80"
            />
            <el-table-column
              label="报警等级"
              prop="alertLevel"
              width="70"
            >
              <template v-slot="{row}">
                <el-tag
                  v-if="row.alertLevel == 1"
                  type="danger">一级</el-tag>
                <el-tag
                  v-if="row.alertLevel == 2"
                  type="warning">二级</el-tag>
                <el-tag
                  v-if="row.alertLevel == 3">三级</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="处理人"
              prop="dealUser"
              width="80"
            />
            <el-table-column
              label="报警分析"
              prop="alertAnalysis"
              width="160"
            />
            <el-table-column
              label="处理意见"
              prop="alertAdvice"
              width="160"
            />
            <el-table-column
              label="报警时间"
              prop="createDateTime"
              width="85"
            />
          </el-table>
          <div style="text-align: right; margin-top: 10px">
            <el-pagination
              :current-page="popHistory.pageIndex"
              :page-size="popHistory.pageSize"
              :page-sizes="[10, 20, 30, 40, 100]"
              :total="popHistory.total"
              background
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handlePopSizeChange"
              @current-change="handlePopCurrentChange"
            />
          </div>
        </el-col>
        <el-col :span="8">
          <el-form
            ref="form"
            :model="formData"
            label-width="120px"
            size="medium"
            style="margin-top: 35px"
          >
            <el-form-item
              :rules="[
                {
                  required: true,
                  message: '请输入报警分析',
                  trigger: 'change'
                }
              ]"
              :prop="'alertAdvice'"
              label="报警分析："
            >
              <el-input
                v-model="formData.alertAnalysis"
                :rows="4"
                type="textarea"
                placeholder="请输入报警分析"
              />
            </el-form-item>
            <el-form-item
              :rules="[
                {
                  required: true,
                  message: '请输入处理意见',
                  trigger: 'change'
                }
              ]"
              :prop="'alertAdvice'"
              label="处理意见："
            >
              <el-input
                v-model="formData.alertAdvice"
                :rows="4"
                type="textarea"
                placeholder="请输入处理意见"
              />
            </el-form-item>
          </el-form>
          <div class="text-center"/>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="popHandleVisible = false">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
    <warning-detail
      ref="modalDetailForm"
      :id="popHistory.id"/>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  batchSaveInfos,
  exportDoneRate,
  exportHistory,
  exportInfos,
  findBasicDataConfigByType,
  findHistoryByAlertID,
  findHistoryByWarningRuleID,
  findModuleInfoList,
  findRuleAlertCount,
  roleList,
  fixWarningInfoList,
  WarningInfoListNew
} from '@/api/system'
import { exportWarningRule, findWarningRule } from '~/api/system'
import WarningDetail from '@/pages/widget/component/warningDetail'

export default {
  name: 'warning-widgetWarningHandle',
  components: { WarningDetail },
  layout: 'menuLayout',
  mixins: [listMixins],
  data() {
    return {
      popVisible: false,
      ruleNumList: {
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      popVisible1: false,
      popHistory: {
        id: null,
        warningRuleID: null,
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      searchForm: {
        isFalse: 0
      },
      url: {
        list: fixWarningInfoList
      },
      tableData: [],
      moduleList: [],
      roleList: [],
      serviceList: [],
      warningTypeList: [
        // 0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        /*{
          value: 5,
          name: '生产过程监测'
        },*/
        {
          value: 6,
          name: '趋势校验报警'
        }
      ],
      isConfirm: [
        {
          value: 0,
          name: '未确认'
        },
        {
          value: 1,
          name: '确认'
        }
      ],
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ],
      statusList: [
        {
          value: 0,
          name: '未处理'
        },
        /*{
          value: 1,
          name: '已挂起'
        },*/
        {
          value: 2,
          name: '已处理'
        }
        /*{
          value: 3,
          name: '持续中'
        },
        {
          value: 9,
          name: '审核中'
        }*/
      ],
      multipleSelection: [],
      handleList: [],
      formData: {
        alertAnalysis: '',
        alertAdvice: ''
      },
      popHandleVisible: false
    }
  },
  computed: {},
  watch: {
    'searchForm.dateTime': function() {
      if (this.searchForm.dateTime && this.searchForm.dateTime.length) {
        this.searchForm.startTime = this.searchForm.dateTime[0]
        this.searchForm.endTime = this.searchForm.dateTime[1]
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    }
  },
  mounted() {
    this.loadData()
    // this.findBasicDataConfigByType()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        this.moduleList = res.data || []
      })
      post(roleList, {
        pageIndex: 1,
        pageSize: 1000,
        roleType: 2
      }).then(res => {
        this.roleList = res.data.content || []
      })
    },
    beforeHandleSearch() {
      this.searchForm.roleID &&
        (this.searchForm.roleIDs = [this.searchForm.roleID])
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content.map(item => {
        return {
          value: item.name,
          label: item.cname
        }
      })
    },
    // 历史报警记录
    showHistory(warningRuleID, reset = false) {
      this.popHistory.warningRuleID = warningRuleID
      this.popHistory.list = []
      reset && (this.popHistory.pageIndex = 1)
      post(
        findHistoryByWarningRuleID,
        Object.assign(
          { warningRuleID: warningRuleID },
          {
            pageIndex: this.popHistory.pageIndex,
            pageSize: this.popHistory.pageSize
          }
        )
      ).then(res => {
        this.popHistory.list = res ? res.data.content : []
        this.popHistory.pageSize = res.data.pageable.pageSize
        this.popHistory.totalPages = res.data.totalPages
        this.popHistory.total = res.data.totalElements
      })
    },
    handlePopSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.popHistory.pageSize = val
      this.showHistory(this.popHistory.warningRuleID)
    },
    handlePopCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.popHistory.pageIndex = val
      this.showHistory(this.popHistory.warningRuleID)
    },
    handleWarning(data) {
      // window.parent.postMessage(
      //   {
      //     type: 'link',
      //     url: data.warningEventBody.alertUrl,
      //     title: 'data.warningEventBody.title'
      //   },
      //   '*'
      // )
    },
    getRowClass({ row, rowIndex }) {
      if (
        this.$moment().format('YYYY-MM-DD') ===
        this.$moment(row.createDateTime).format('YYYY-MM-DD')
      ) {
        return 'select-row'
      }
      return ''
    },
    getSameClass({ row, rowIndex }) {
      if (row.id === this.popHistory.id) {
        return 'select-row'
      }
      return ''
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleMultiple() {
      if (!this.multipleSelection.length) {
        return this.$message.warning('请选择需要操作的数据！')
      }
      this.handleList = this.multipleSelection
      this.formData = {
        alertAnalysis: '',
        alertAdvice: ''
      }
      this.popHandleVisible = true
    },
    handleSingle(row) {
      this.handleList = [row]
      Object.assign(this.formData, row)
      this.popHistory.id = row.id
      this.showHistory(row.warningRuleID, true)
      this.popHandleVisible = true
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          const params = {
            list: this.handleList.map(item => {
              item.alertAdvice = this.formData.alertAdvice
              item.alertAnalysis = this.formData.alertAnalysis
              return item
            })
          }
          post(batchSaveInfos, params).then(res => {
            this.loading = false
            if (res.success) {
              this.popHandleVisible = false
              this.$message.success('修改成功！')
            } else {
              this.$message.warning('保存失败！')
            }
          })
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-operate {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
    .operate-icon {
      margin-left: 8px;
    }
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
/deep/ .select-row {
  background: #ffeff0 !important;
}
/deep/
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped.select-row
  td.el-table__cell {
  background: #ffeff0 !important;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
.tit {
  font-size: 18px;
  margin-bottom: 10px;
}
</style>
