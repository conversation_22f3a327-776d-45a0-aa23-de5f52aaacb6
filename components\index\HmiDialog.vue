<template>
  <el-dialog
    :top="'8vh'"
    :visible.sync="dialogVisible"
    :mini-id="miniId"
    :close-on-click-modal="false"
    :width="width+'px'"
    :append-to-body="true"
    :show-close="false"
    class="hmi-dialog">
    <div
      slot="title"
      class="header">
      <div class="title">{{ title }}</div>
      <div class="oper">
        <i
          class="el-icon-minus"
          @click="miniBtn()"
        />
        <i
          class="el-icon-close"
          @click="closeFn()"
        />
      </div>
    </div>
    <slot name="content"/>
  </el-dialog>
</template>

<script>
export default {
  name: 'HmiDialog',
  props: {
    title: {
      type: String,
      default: ''
    },
    miniId: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 1800
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      miniApp: [],
      miniAppList: []
    }
  },
  mounted() {
    this.getDefalutMiniAppList()
  },
  methods: {
    getDefalutMiniAppList() {
      if (!localStorage.getItem('miniAppList')) {
        localStorage.setItem('miniAppList', JSON.stringify([]))
      }
      this.miniAppList = JSON.parse(localStorage.getItem('miniAppList'))
    },
    miniBtn() {
      // console.log('最小化')
      if (this.miniAppList.indexOf(this.miniId) < 0) {
        this.miniAppList.push(this.miniId)
      }
      localStorage.setItem('miniAppList', JSON.stringify(this.miniAppList))
      this.$emit('miniBtn')
      //通过监听watch告诉defalt.vue数组改变
      this.$store.state.miniAppWatch = Math.random()
    },
    closeFn() {
      this.$emit('closeFn')
    }
  }
}
</script>

<style scoped lang="less">
.hmi-dialog {
  /deep/ .el-dialog {
    border-radius: 10px;
    overflow: hidden;
  }
  /deep/ .el-dialog__header {
    padding: 0;
  }
  /deep/ .el-dialog__body {
    min-height: 300px;
    background-color: #cbd0e3;
    padding: 15px;
  }
  .header {
    padding: 4px 12px;
    height: 36px;
    background-color: #2f85e7;
    color: #fff;
    font-size: 18px;
    letter-spacing: 1px;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    > div {
    }
    .oper {
      > i {
        margin-right: 4px;
        cursor: pointer;
      }
    }
  }
}
</style>
