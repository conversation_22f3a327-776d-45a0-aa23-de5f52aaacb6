<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="规则名称"
              prop="ruleName"
            >
              <el-input
                v-model="searchForm.ruleName"
                clearable
                size="small"
                placeholder="请输入规则名称"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="模块"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.moduleCode"
                size="small"
                placeholder="选择模块"
                style="width: 130px"
                @change="handleSearch(true)"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.Name"
                  :value="item.ID"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="产线"
              prop="productionLineName"
            >
              <el-select
                v-model="searchForm.productionLineName"
                size="small"
                placeholder="选择产线"
                style="width: 130px"
                filterable
                @change="handleSearch(true)"
              >
                <el-option
                  v-for="(item, index) in productLineList"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="设备名称"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.deviceName"
                size="small"
                placeholder="选择设备"
                style="width: 130px"
                filterable
                @change="handleSearch(true)"
              >
                <el-option
                  v-for="(item, index) in deviceList"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="区域名称"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.areaName"
                size="small"
                placeholder="选择区域"
                style="width: 130px"
                filterable
                @change="handleSearch(true)"
              >
                <el-option
                  v-for="(item, index) in areaList"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警类型"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.warningType"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警类型"
              >
                <el-option
                  v-for="(item, index) in warningTypeList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警级别"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.alertLevel"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警级别"
              >
                <el-option
                  v-for="(item, index) in alertLevelList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleExport"
          >导出
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">

        <el-table
          :data="tableData"
          size="small"
          border
          style="width: 100%"
        >
          <el-table-column
            label="模块名称"
            prop="moduleName"
            width="100"
          />
          <el-table-column
            label="规则名称"
            prop="ruleName"
            min-width="100"
          />
          <el-table-column
            label="规则描述"
            prop="ruleDesc"
            min-width="100"
          />
          <el-table-column
            label="指标数值"
            prop="ruleValue"
            width="80"
          />
          <el-table-column
            label="区域名称"
            prop="areaName"
            width="80"
          />
          <el-table-column
            label="产线名称"
            prop="productionLineName"
            width="90"
          />
          <el-table-column
            label="设备名称"
            prop="deviceName"
            width="80"
          />
          <el-table-column
            label="点位名称"
            prop="pointName"
            width="140"
          />
          <el-table-column
            label="报警等级"
            prop="alertLevel"
            width="80"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.alertLevel == 1"
                type="danger">一级</el-tag>
              <el-tag
                v-if="row.alertLevel == 2"
                type="warning">二级</el-tag>
              <el-tag
                v-if="row.alertLevel == 3">三级</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="推送角色"
            prop="pushRoleName"
            width="100"
          />
          <el-table-column
            label="责任人"
            prop="liablePersonName"
            width="100"
          />
          <el-table-column
            label="创建时间"
            prop="createDateTime"
            width="140"
          />
          <el-table-column
            label="是否推送"
            prop="status"
            width="80"
          >
            <template v-slot="{ row }">
              {{ row.pushMode == 1 ? '推送': '不推送' }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            prop="createDateTime"
            width="80"
          >
            <template v-slot="{ row }">
              <el-button
                type="text"
                @click="handleStatus(row)">{{ row.pushMode == 0 ? '推送': '不推送' }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  changePushMode,
  findAlertInfo,
  findAreaInfoList,
  findDeviceInfoList,
  findModuleInfoList,
  findProductionLineInfoList
} from '@/api/system'
import { exportWarningRule, findWarningRule } from '~/api/system'

export default {
  name: 'warning-widgetWarningRule',
  layout: 'menuLayout',
  mixins: [listMixins],
  data() {
    return {
      searchForm: {
        userNo: localStorage.getItem('userId')
      },
      url: {
        list: findWarningRule,
        status: changePushMode
      },
      tableData: [],
      moduleList: [],
      areaList: [],
      productLineList: [],
      deviceList: [],
      warningTypeList: [
        // 0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        {
          value: 5,
          name: '生产过程监测'
        },
        {
          value: 6,
          name: '趋势校验报警'
        }
      ],
      isConfirm: [
        {
          value: 0,
          name: '未确认'
        },
        {
          value: 1,
          name: '确认'
        }
      ],
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        this.moduleList = res.data || []
      })
      post(findProductionLineInfoList, {}).then(res => {
        this.productLineList = res.data.map(item => {
          return item[0]
        })
      })
      post(findDeviceInfoList, {}).then(res => {
        this.deviceList = res.data.map(item => {
          return item[0]
        })
      })
      post(findAreaInfoList, {}).then(res => {
        this.areaList = res.data.map(item => {
          return item[0]
        })
      })
    },
    handleExport() {
      this.loading = true
      post(
        exportWarningRule,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '预警规则.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    handleWarning(data) {
      // window.parent.postMessage(
      //   {
      //     type: 'link',
      //     url: data.warningEventBody.alertUrl,
      //     title: 'data.warningEventBody.title'
      //   },
      //   '*'
      // )
    },
    handleStatus(row) {
      this.$confirm('是否确认修改推送状态?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 操作
        post(this.url.status, {
          id: row.id,
          status: row.pushMode == 1 ? 0 : 1
        }).then(res => {
          // 成功
          this.$message.success('修改成功！')
          this.handleSearch()
        })
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.widget-list {
  flex: 1;
  overflow: auto;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
