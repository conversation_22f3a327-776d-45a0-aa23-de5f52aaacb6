<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'字典详情'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1200px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <div class="page-content">
        <div
          class="page-operate"
          style="align-items: flex-start">
          <div class="search-wrapper">
            <el-form
              ref="form"
              :model="searchForm"
              inline
              @keydown.enter.native.stop.prevent="handleSearch(true)"
            >
              <el-form-item
                label="值："
                prop="module"
              >
                <el-input
                  v-model="searchForm.value"
                  placeholder="请输入关键字" />
              </el-form-item>
            </el-form>
          </div>
          <div
            class="text-right"
            style="white-space: nowrap">
            <el-button
              icon="ios-search"
              type="primary"
              @click="handleSearch"
            >搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button
              icon="el-icon-circle-plus-outline"
              type="success"
              @click="handleAdd"
            >新增
            </el-button>
          </div>
        </div>
        <div class="page-card shadow-light">
          <el-table
            v-loading="loading"
            :data="tableData"
            :size="size"
            border
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              type="index"
            />
            <el-table-column
              label="编码"
              prop="code"
              width="150"
            />
            <el-table-column
              label="值"
              prop="value"
              show-overflow-tooltip
            />
            <el-table-column
              v-if="code === 'modelNo'"
              label="模块名"
              prop="value1"
              show-overflow-tooltip
            >
              <template
                v-slot="{row}"
              >
                <el-tag
                  v-if="row.value1"
                  disable-transitions
                >{{ getName('moduleList', row.value1).label }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="描述"
              prop="dict.description"
            />
            <el-table-column
              fixed="right"
              label="操作"
              style="white-space: nowrap"
            >
              <template
                slot-scope="{row,$index}"
              >
                <template>
                  <span>
                    <el-button
                      size="small"
                      type="text"
                      @click="handleEdit(row)"
                    >编辑
                    </el-button>
                  </span>
                  <span>
                    <el-divider direction="vertical" />
                    <el-button
                      slot="reference"
                      type="text"
                      @click="handleDelete(row)"
                    >{{ '删除' }}
                    </el-button>
                  </span>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <el-row
            align="middle"
            class="table-pagination"
            justify="end"
            type="flex"
          >
            <el-pagination
              :current-page="page.pageIndex"
              :page-size="page.pageSize"
              :page-sizes="[10, 20, 30, 40]"
              :total="page.total"
              background
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </el-row>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <edit-dtl
      ref="modalForm"
      :id="id"
      :code="code"
      @success="handleSearch"
    />
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlDelete,
  dictionaryDtlFind,
  dictionaryDtlFindByDictCode,
  dictionaryDtlSave
} from '@/api/system'
import { post } from '@/lib/Util'
import EditDtl from './editDtl'
import { ENUM } from '@/lib/Constant'

export default {
  components: { EditDtl },
  mixins: [listMixins, EditMixins],
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: ''
    },
    code: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      visible: false,
      searchForm: {
        mode: 'daterange'
      },
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: dictionaryDtlFind, //分页接口地址
        delete: dictionaryDtlDelete, //删除接口地址
        save: dictionaryDtlSave,
        getDict: dictionaryDtlFindByDictCode
      },
      moduleList: [],
      workList: ENUM.workList,
      userId: null,
      editRole: null,
      serviceList: [],
      timer: null,
      activeIndex: 0,
      mode: 'daterange'
    }
  },
  computed: {},
  async created() {
    post(this.url.getDict, {
      dictCode: 'serviceByEval'
    }).then(res => {
      this.moduleList = res.data.map(item => {
        return {
          value: item.code,
          label: item.value
        }
      })
    })
    this.handleSearch(true)
  },
  destroyed() {},
  methods: {
    async handleSearch(reset = false) {
      if (!this.id) return (this.loading = false)
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({ dictId: this.id }, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data.content : []
      this.page.pageSize = data.pageable.pageSize
      this.page.totalPages = data.totalPages
      this.page.total = data.totalElements
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },

    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  &:hover {
    background: #eee;
    color: #fff;
  }
}
</style>
