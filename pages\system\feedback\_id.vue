<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label=""
              prop="title"
            >
              <el-input
                v-model="searchForm.title"
                clearable
                size="small"
                placeholder="输入标题"
                suffix-icon="el-icon-search"
                style="width: 100px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label=""
              prop="quesUserNo"
            >
              <el-input
                v-model="searchForm.quesUserNo"
                clearable
                size="small"
                placeholder="输入提出者工号"
                suffix-icon="el-icon-search"
                style="width: 100px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="提出人单位"
              prop="quesOrgCode"
            >
              <select-org
                v-model="searchForm.quesOrgCode"
                :multiple="false"/>
            </el-form-item>
            <el-form-item
              label="提出时间"
              prop="startTime"
            >
              <el-date-picker
                v-model="searchForm.dateRange"
                :value-format="'yyyy-MM-dd'"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 215px"
                @input="$forceUpdate()"/>
            </el-form-item>
            <el-form-item
              label="应用"
              prop="serviceNo"
            >
              <el-select
                v-model="searchForm.serviceNo"
                :style="{width: '160px'}"
                size="small"
                clearable
                placeholder="选择应用"
              >
                <el-option
                  v-for="(item, index) in serviceList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="quesType"
            >
              <el-select
                v-model="searchForm.handleStatus"
                :style="{width: '100px'}"
                size="small"
                clearable
                placeholder="选择状态"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>

          <el-button
            icon="ios-search"
            size="small"
            type="primary"
            @click="importDateVisible = true"
          >反馈统计
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="标题"
            prop="title"
            min-width="150"
            show-overflow-tooltip
          >
            <template v-slot="{ row }">
              <div class="one-line">
                <span v-if="row.warning">
                  <el-tag
                    :type="'warning'"
                    disable-transitions
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tag>
                </span>
                {{ row.title }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="描述"
            prop="description"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属应用"
            prop="serviceNo"
          >
            <template
              v-slot="{row}"
            >
              {{ getServiceName(row.serviceNo).label }}
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            prop="createDateTime"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            label="计划完成时间"
            prop="planFinishDate"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="建议人"
            prop="quesUserName"
            show-overflow-tooltip
          />
          <el-table-column
            label="建议人单位"
            prop="quesOrgName"
            show-overflow-tooltip
          />
          <el-table-column
            label="状态"
            prop="handleStatus"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="getName('statusList', row.handleStatus).type"
                disable-transitions
              >{{ getName('statusList', row.handleStatus).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="未处理时长"
            prop="diff"
            min-width="135"
            show-overflow-tooltip
          >
            <template
              v-slot="{row}"
            >
              <timer
                v-if="row.handleStatus != 6"
                :time="row.diff"
                :handle-diff="row.handleDiff" />
            </template>
          </el-table-column>
          <el-table-column
            label="处理人"
            prop="handleUserName"
            show-overflow-tooltip
          />
          <el-table-column
            label="处理意见"
            prop="handleDesc"
            show-overflow-tooltip
          />
          <el-table-column
            label="处理时间"
            prop="handleTime"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            fixed="right"
            label="操作"
            width="200px"
          >
            <template
              v-slot="{row}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail(row)"
                >详情
                </el-button>
              </span>
              <template v-if="row.quesUserNo === userId">
                <span v-if="row.handleStatus == 1">
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleRemind(row)"
                  >催办
                  </el-button>
                </span>
                <span v-if="[1, 3].includes(row.handleStatus)">
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span v-if="row.handlerStatus == 2">
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleClose(row)"
                  >关闭
                  </el-button>
                </span>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
              <template v-else>
                <span v-if="row.handleStatus == 1 || row.handleStatus == 3 || ((row.handleStatus == 2 || row.handleStatus == 5) && row.handleUserNo === userId)">
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handlePop(row)"
                  >处理
                  </el-button>
                </span>
              </template>

            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :service-list="serviceList"
      :type-list="typeList"
      :is-data-cause-list="isDataCauseList"
      :ques-type="searchForm.quesType"
      @success="handleSearch"
    />
    <Edit
      ref="modalForm"
      :service-list="serviceList"
      :type-list="typeList"
      :is-data-cause-list="isDataCauseList"
      :ques-type="searchForm.quesType"
      @success="handleSearch"
    />
    <Handle
      ref="modalHandleForm"
      :service-list="serviceList"
      :type-list="typeList"
      :is-data-cause-list="isDataCauseList"
      :ques-type="searchForm.quesType"
      @success="handleSearch"
    />
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'600px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="问题反馈统计">
      <template v-slot:title>
        <div class="custom-dialog-title">
          问题反馈统计
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导出日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"
            type="daterange"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            :loading="loading"
            type="primary"
            @click="handleStatisticsExport()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  delFeedback,
  dictionaryDtlFindByDictCode,
  findBasicDataConfigByType,
  findFeedback,
  roleDelete,
  exportUserFeedBackExcel,
  roleList,
  saveFeedback
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Handle from '@/pages/system/feedback/component/handle'
import Detail from '@/pages/system/feedback/component/detail'
import Timer from '@/pages/system/feedback/component/timer'
import SelectOrg from '@/components/SelectOrg'

export default {
  layout: 'menuLayout',
  name: 'Role',
  components: {
    SelectOrg,
    Timer,
    Detail,
    Handle,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      importDateVisible: false,
      importDate: null,
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: findFeedback, //分页接口地址
        delete: delFeedback, //删除接口地址
        save: saveFeedback,
        getDict: dictionaryDtlFindByDictCode
      },
      userId: null,
      roleStatusList: ENUM.roleStatus,
      editRole: null,
      serviceList: [],
      typeList: [{ label: '建议', value: 1 }, { label: 'BUG', value: 2 }],
      statusList: [
        { label: '待处理', value: 1, type: 'warning' },
        { label: '处理中', value: 5, type: '' },
        { label: '已处理', value: 2, type: 'success' },
        { label: '催办', value: 3, type: 'danger' },
        { label: '已关闭', value: 4, type: 'info' },
        { label: '退回', value: 6, type: 'info' }
      ],
      isDataCauseList: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
        { label: '未知', value: '' }
      ],
      timer: null
    }
  },
  created() {
    this.searchForm.quesType = parseInt(this.$route.params.id)
    this.handleSearch(true)
    this.getServiceInfo()
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    afterHandleSearch(tableData) {
      this.updateDiff()
    },
    beforeHandleSearch() {
      this.searchForm.startDate = this.searchForm.dateRange
        ? this.searchForm.dateRange[0]
        : ''
      this.searchForm.endDate = this.searchForm.dateRange
        ? this.searchForm.dateRange[1]
        : ''
    },
    updateDiff() {
      this.tableData.forEach(item => {
        if (
          item.handleStatus === 1 &&
          this.$moment().format('yyyy-MM-DD') > item.planFinishDate
        ) {
          item.warning = true
        }
        const diff = this.$moment().diff(
          this.$moment(item.createDateTime),
          'seconds'
        )
        item.diff = diff
        if (item.handleTime) {
          item.handleDiff = this.$moment(item.handleTime).diff(
            this.$moment(item.createDateTime),
            'seconds'
          )
        }
      })
    },
    getName: function(list, status) {
      return this[list].find(item => item.value === status) || {}
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.value === name) || {}
    },

    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
      this.$refs.modalForm.attachList = row.attachList || []
    },

    // 详情
    handleDetail: function(row) {
      this.$refs.modalDetailForm.edit(row)
      this.$refs.modalDetailForm.visible = true
      this.$refs.modalDetailForm.attachList = row.attachList || []
    },

    // 处理
    handlePop: function(row) {
      this.$refs.modalHandleForm.edit(row)
      this.$refs.modalHandleForm.visible = true
      this.$refs.modalHandleForm.attachList = row.attachList || []
    },

    // 催办
    handleRemind: function(row) {
      this.$confirm(`是否确认催办此条记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 催办操作
        post(
          this.url.save,
          Object.assign({}, row, {
            handleStatus: 3
          })
        ).then(() => {
          this.handleSearch()
        })
      })
    },
    // 关闭
    handleClose: function(row) {
      this.$confirm(`是否确认关闭此条记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 催办操作
        post(
          this.url.save,
          Object.assign({}, row, {
            handleStatus: 4
          })
        ).then(() => {
          this.handleSearch()
        })
      })
    },

    async getServiceInfo() {
      const { data: module } = await post(this.url.getDict, {
        dictCode: 'service'
      })
      this.serviceList = module.map(item => {
        return {
          value: item.code,
          label: item.value
        }
      })
      return new Promise(resolve => resolve(true))
    },
    async handleStatisticsExport() {
      this.loading = true
      post(
        exportUserFeedBackExcel,
        Object.assign(
          {},
          {
            startDate: this.importDate[0],
            endDate: this.importDate[1]
          },
          { pageSize: 10000 }
        ),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '问题反馈统计(' +
            this.importDate[0] +
            '-' +
            this.importDate[1] +
            ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
