const path = 'res/'

// 登陆
export const login = path + 'login/loginPageAuthVerify'
export const getLoginMode = path + 'login/getLoginMode'
export const updatePwd = path + 'user/updatePwd'
// 系统管理api-url
// 用户列表
export const userList = path + 'user/findAllUser'
// 用户删除
export const userDelete = path + 'user/doDeleteUser'
// 用户添加
export const userAdd = path + 'user/doCreateUser'
// 用户修改
export const userEdit = path + 'user/doUpdaterUser'
// 用户详情
export const userById = path + 'user/findOneUserByID'
// 用户关联角色
export const relateRole = path + 'user/relateRole'

// 角色列表
export const roleList = path + 'role/findAllRole'
// 角色删除
export const roleDelete = path + 'role/doChangeStatus'
// 角色添加
export const roleAdd = path + 'role/doCreateRole'
// 角色编辑
export const roleEdit = path + 'role/doUpdateRole'
// 角色列表无分页
export const roleListNoPage = path + 'role/findAllRoleNoPage'
// 角色详情
export const roleById = path + 'role/findOneRoleByID'
// 根据用户查询角色
export const roleByRscID = path + 'role/findRoleByRscID'
// 根据用户查询角色
export const roleByUser = path + 'role/findRoleByUserID'
// 根据用户查询角色
export const filterResourceSelected = path + 'resource/filterResourceSelected'
// 关联人员
export const relateUser = path + 'role/relateUser'
// 关联菜单
export const relateResource = path + 'role/relateResource'

// 资源列表
export const resourceList = path + 'resource/findAllResource'
// 批量更新资源
export const batchUpdateResource = path + 'resource/batchUpdateResource'
// 资源删除
export const resourceDelete = path + 'resource/doDeleteResource'
// 资源添加
export const resourceAdd = path + 'resource/doAddResource'
// 资源编辑
export const resourceEdit = path + 'resource/doUpdateResource'
// 资源列表无分页
export const resourceListNoPage = path + 'resource/findAllResourceNoPage'
// 根据角色过滤已选择的菜单
export const filterResourceSelectedByRole = path + 'UCR/findAllRscByUserNo'
//获取首页展示菜单
export const getHomePageResource = path + 'UCR/findFirstPage'
//保存首页展示菜单(勾选或者取消勾选，目前限制四个)
export const saveHomePageResource = path + 'UCR/saveFirstPage'
// 资源列表无分页 (不限状态 包括已经禁用)
export const findAllOfResource = path + 'resource/findAllOfResource'
// 查询子级资源
export const resourceByParent = path + 'resource/findResourceByParentID'
// 关联角色
export const resourceRelateRole = path + 'resource/relateRole'

// 根据组织编号获取组织列表
export const orgListByCode = path + 'org/findListByOrgCode'

// 根据组织编号查询所有组织列表
export const allOrgListByCode = path + 'org/findAllOrgByOrgCode'

// 根据组织编号查询所有组织列表
export const findOrgByUserNo = path + 'org/findOrgByUserNo'

// 所有组织
export const allOrgList = path + 'org/findAll'
// icon列表
export const listImg = path + 'iconImg/listImg'
// 删除icon
export const deleteImg = path + 'iconImg/delete'
// uploadImg
export const uploadImg = path + 'iconImg/uploadImg'

// 根据类型获取基础数据
export const findBasicDataConfigByType =
  'idm/basicDataConfig/findBasicDataConfigByType.form'

export const findAlertInfo =
  path + 'warningEvent/findAlertInfoByMultipleChoices'

export const getCurrentUser = path + '/user/getCurrUser'

export const appLog = path + 'pageLog/save'
export const appLogMultiCondition = path + 'pageLog/findByMultiCondition'

// 应用访问量
export const findServiceVisitNumByLoginTime =
  path + 'pageLog/findAccessNumByLoginTime2'
// 导出excel
export const exportExcelByLoginTime = path + 'pageLog/exportExcel'
//用户访问记录行导出excel
export const pageLogExportExcel = path + '/pageLog/exportPageAccessListByUserNo'
//用户访问记录10s行导出excel
export const tenPageLogExportExcel =
  path + '/pageLogSecret/exportPageAccessListByUserNo'
// 导出word
export const exportWordByLoginTime = path + 'pageLog/exportWord2'

// 建议分页列表
export const findFeedback = path + 'feedBack/findPageByMultiCondition'
// 建议删除
export const delFeedback = path + 'feedBack/delete'
// 建议删除
export const saveFeedback = path + 'feedBack/save'
// 上传附件
export const uploadFile = path + 'attach/uploadFile'
// 下载附件
export const downloadFileById = path + 'attach/downloadFileById/'
// 删除附件
export const deleteFileByIds = path + 'attach/delete'
// 查找附件
export const attachFindAllByMultiCondition =
  path + 'attach/findAllByMultiCondition'

// 维保记录台账
export const errorRecord = path + 'errorRecord/findAll'
export const errorRecordSave = path + 'errorRecord/saveRecord'
// 删除
export const matterDeleteByIds = path + '/coordinateMatter/deleteByIds'
// 删除
export const matterTop = path + '/coordinateMatter/topping'

// 查询所有
export const matterFindAll = path + '/coordinateMatter/findAll'

// 按多条件查找分页
export const matterFindFeedback =
  path + '/coordinateMatter/findPageByMultiCondition'

//保存
export const matterSave = path + '/coordinateMatter/save'

// 项目进度管理
// 删除
export const progressDelete = path + '/progressMgt/delete'

// 按多条件查找分页
export const progressFind = path + '/progressMgt/findPageByMultiCondition'

//保存
export const progressSave = path + '/progressMgt/save'

// 上线进度管理
// 删除
export const onlineDelete = path + '/onlineMgt/delete'

// 按多条件查找分页
export const onlineFind = path + '/onlineMgt/findPageByMultiCondition'

//保存
export const onlineSave = path + '/onlineMgt/save'

// 周总结管理
// 删除
export const weeklySummaryDelete = path + '/weeklySummary/delete'

// 按多条件查找分页
export const weeklySummaryFind =
  path + '/weeklySummary/findPageByMultiCondition'
//保存
export const weeklySummarySave = path + '/weeklySummary/save'
//保存
export const weeklySummarySaveList = path + '/weeklySummary/saveList'
//导出
export const weeklySummaryExportExcel = path + '/weeklySummary/export'

//按字典编码查找字典详情列表
export const dictionaryDtlFindByDictCode =
  path + '/dictionaryDtl/findByDictCode'
//字典详情按多条件查找分页
export const dictionaryDtlFind =
  path + '/dictionaryDtl/findPageByMultiCondition'
export const dictionaryDtlSave = path + '/dictionaryDtl/save'
export const dictionaryDtlDelete = path + '/dictionaryDtl/delete'
//字典按多条件查找分页
export const dictionaryFind = path + '/dictionary/findPageByMultiCondition'
export const dictionarySave = path + '/dictionary/save'
export const dictionaryDelete = path + '/dictionary/delete'
// 页面访问记录查询
export const pageLogMultiCondition = path + 'pageLog/findByMultiCondition'
// 添加用户访问记录
export const pageLogSave = 'res/pageLog/save'
// 添加用户访问记录
export const findAccessNumByLoginTime = 'res/pageLog/findAccessNumByLoginTime'
// 获取页面访问量看板
export const getPageLogLookBoard = 'res/pageLog/getPageLogLookBoard'
// 按组织获取页面访问量看板
export const getPageLogLookBoardByOrg = 'res/pageLog/getPageLogLookBoardByOrg'
// 看板领导访问记录排行
export const getLeaderAccess = 'res/pageLog/getLeaderAccess'
// 查找用户页面访问明细报表
export const findPageAccessListByUserNo =
  'res/pageLog/findPageAccessListByUserNo'
// 查找用户页面访问量报表
export const findUserPageAccessList = 'res/pageLog/findUserPageAccessList'
// 查找每个页面访问列表
export const findEachPageAccessList = 'res/pageLog/findEachPageAccessList'
// 按资源id查找用户页面访问列表
export const findUserPageAccessListByResourceId =
  'res/pageLog/findUserPageAccessListByResourceId'
// owner访问主档
export const getMainHandleUser = 'res/pageLog/getMainHandleUser'
// owner访问明细档
export const getDetailHandleUser = 'res/pageLog/getDetailHandleUser'
// 页面访问记录导出
export const exportPageExcel = 'res/pageLog/exportPageExcel'
// 用户访问记录导出
export const exportUserExcel = 'res/pageLog/exportUserExcel'
// 用户访问记录导出2
export const exportUserDetExcel = 'res/pageLog/exportUserDetExcel'
// 用户访问记录导出2
export const exportLeaderAndOrgExcel = 'res/pageLog/exportLeaderAndOrgExcel'
// 应访问用户统计
export const exportShouldUserAccessExcel =
  'res/pageLog/exportShouldUserAccessExcel'
// 应用访问统计表
export const getCountInfoByDay4DSB = 'res/pageLog/getCountInfoByDay4DSB'

// 用户访问记录相关接口 10s访问版本
export const pageLogSecret = {
  // 页面访问记录查询
  appLogMultiCondition: path + 'pageLogSecret/findByMultiCondition',
  // 添加用户访问记录
  pageLogSave: 'res/pageLogSecret/save',
  // 添加用户访问记录
  findAccessNumByLoginTime: 'res/pageLogSecret/findAccessNumByLoginTime',
  // 获取页面访问量看板
  getPageLogLookBoard: 'res/pageLogSecret/getPageLogLookBoard',
  // 按组织获取页面访问量看板
  getPageLogLookBoardByOrg: 'res/pageLogSecret/getPageLogLookBoardByOrg',
  // 看板领导访问记录排行
  getLeaderAccess: 'res/pageLogSecret/getLeaderAccess',
  // 查找用户页面访问明细报表
  findPageAccessListByUserNo: 'res/pageLogSecret/findPageAccessListByUserNo',
  // 查找用户页面访问量报表
  findUserPageAccessList: 'res/pageLogSecret/findUserPageAccessList',
  // 查找每个页面访问列表
  findEachPageAccessList: 'res/pageLogSecret/findEachPageAccessList',
  // 按资源id查找用户页面访问列表
  findUserPageAccessListByResourceId:
    'res/pageLogSecret/findUserPageAccessListByResourceId',
  // owner访问主档
  getMainHandleUser: 'res/pageLogSecret/getMainHandleUser',
  // owner访问明细档
  getDetailHandleUser: 'res/pageLogSecret/getDetailHandleUser',
  // 页面访问记录导出
  exportPageExcel: 'res/pageLogSecret/exportPageExcel',
  // 页面访问记录导出
  exportUserPageDetExcel: 'res/pageLogSecret/exportUserPageDetExcel',
  // 用户访问记录导出
  exportUserExcel: 'res/pageLogSecret/exportUserExcel',
  // 用户访问记录导出2
  exportUserDetExcel: 'res/pageLogSecret/exportUserDetExcel',
  // 用户访问记录导出2
  exportLeaderAndOrgExcel: 'res/pageLogSecret/exportLeaderAndOrgExcel'
}

// 导出用户反馈统计Excel
export const exportUserFeedBackExcel = 'res/feedBack/exportUserFeedBackExcel'

// 调试记录分页列表
export const findDebugRecord = path + 'debugRecord/findPageByMultiCondition'
// 调试记录添加
export const saveDebugRecord = path + 'debugRecord/save'
// 调试记录删除
export const delDebugRecord = path + 'debugRecord/delete'

// 预警信息历史记录查询
export const findHistoryByWaringEventID =
  path + 'WarningInfo/findHistoryByWaringEventID'

// 查询预警规则
export const findWarningRule = path + 'WarningRule/findPageByMultiCondition'
// 查询预警规则
export const changePushMode = path + 'WarningRule/changePushMode'
// 导出预警规则
export const exportWarningRule = path + 'WarningRule/exportRules'
// 报警根据规则分组统计
export const findRuleAlertCount = path + 'WarningInfo/findRuleAlertCount'
// 历史记录查询
export const findSummaryByWarningRuleID =
  path + '/WarningInfo/findSummaryByWarningRuleID'
// 历史记录查询
export const findHistoryByAlertID = path + 'WarningInfo/findHistoryByAlertID'
// 历史记录导出
export const exportHistory = path + 'WarningInfo/exportHistory'
// 历史记录导出
export const exportHistoryByWarningRuleID =
  path + 'WarningInfo/exportHistoryByWarningRuleID'
// 导出根据角色获取处理
export const exportDoneRate = path + 'WarningInfo/exportDoneRate'
// 导出根据角色获取处理详情
export const exportAlertInfoByRoleIDs =
  path + 'WarningInfo/exportAlertInfoByRoleIDs'
// 导出根据模块获取处理详情
export const exportAlertInfoByModuleCodes =
  path + 'WarningInfo/exportAlertInfoByModuleCodes'
// 导出根据模块获取处理
export const exportDoneRateByModuleCodes =
  path + 'WarningInfo/exportDoneRateByModuleCodes'
// 根据角色获取处理情况
export const getDoneRateByRoleIDs = path + 'WarningInfo/getDoneRateByRoleIDs'
// 完成情况百分比
export const findFinishSummary = path + 'WarningInfo/findFinishSummary'
// 根据模块取处理情况
export const getDoneRateByModuleCodes =
  path + 'WarningInfo/getDoneRateByModuleCodes'

// 查询报警记录
export const WarningInfoList = path + 'WarningInfo/findPageByMultiCondition'
// 查询数据修正
export const fixWarningInfoList =
  path + 'WarningInfo/findFixPageByMultiCondition'
export const WarningInfoListUnPack =
  path + 'WarningInfo/findPageByMultiConditionUnPack'
export const findHistoryByWarningRuleID =
  path + 'WarningInfo/findHistoryByWarningRuleID'
export const exportInfos = path + 'WarningInfo/exportInfos'
export const WarningInfoListNew =
  path + 'WarningInfo/findPageByMultiConditionNew'
export const findHistoryByRoleAndTime =
  path + 'WarningInfo/findHistoryByRoleAndTime'

// 班组统计
export const findTrendByWaringRuleIDAndTime =
  path + 'WarningInfo/findTrendByWaringRuleIDAndTime'
// 指标专项攻关帮扶清单
export const checkQuotaInfo = path + 'QuotaInfo/checkQuotaInfo'
export const confirmQuotaInfo = path + 'QuotaInfo/confirmQuotaInfo'
export const deleteInfo = path + 'QuotaInfo/deleteInfo'
export const findQuotaInfoList = path + 'QuotaInfo/findQuotaInfoList'
export const finishInfo = path + 'QuotaInfo/finishInfo'
export const saveInfo = path + 'QuotaInfo/saveInfo'
// 报警转隐患
export const AlarmToHiddenDanger =
  path + 'HiddenDanger/findPageByMultiCondition'
// 帮扶清单
export const AssistanceInfoDelete = path + 'AssistanceInfo/deleteInfo'
export const AssistanceInfo = path + 'AssistanceInfo/findPageByMultiCondition'
export const AssistanceInfoFinish = path + 'AssistanceInfo/finishInfo'
export const AssistanceInfoSave = path + 'AssistanceInfo/saveInfo'
// 误报列表
export const WarningFalseInfoList =
  path + 'WarningInfo/findFalsePageByMultiCondition'
export const saveFalseInfo = path + 'WarningInfo/saveFalseInfo'
// 误报导出
export const exportFalseInfos = path + 'WarningInfo/exportFalseInfos'

// 导出预警规则
export const findModuleInfoList = path + 'WarningRule/findModuleInfoList'
// 获取区域列表
export const findAreaInfoList = path + 'WarningRule/findAreaInfoList'
// 获取设备列表
export const findDeviceInfoList = path + 'WarningRule/findDeviceInfoList'
// 获取区域列表
export const findProductionLineInfoList =
  path + 'WarningRule/findProductionLineInfoList'
// 报警原因批量处理
export const batchSaveInfos = path + 'WarningInfo/batchSaveInfos'

// 应用评价
// 按多条件查找分页
export const evaluationFind = path + '/appEval/findPageByMultiCondition'
//保存
export const evaluationSave = path + '/appEval/save'
//删除
export const evaluationDelete = path + '/appEval/delete'
//批量保存
export const evaluationSaveAll = path + '/appEval/saveAll'
//按周查询
export const findWeekAppEval = path + '/appEval/findWeekAppEval'
//删除by多条件
export const evaluationDeleteByMultiKey = path + '/appEval/deleteByMultiKey'
//删除by多条件
export const evaluationExportPdf = path + '/appEval/exportPdf'
//导出excel
export const evaluationExportExcel = path + '/appEval/exportSummaryByTime'

//查询应用评价统计
export const findGroupByServerAndModel =
  path + '/appEval/findGroupByServerAndModel'

// 跟踪
// 按多条件查找分页
export const taskTrackingFind = path + '/taskTracking/findPageByMultiCondition'
//保存
export const taskTrackingSave = path + '/taskTracking/save'
//删除
export const taskTrackingDelete = path + '/taskTracking/delete'
//得分统计
export const findTaskScoreStatistics =
  path + '/taskTracking/findTaskScoreStatistics'

// 系统公告
// 按多条件查找分页
export const sysNoticeFind = path + '/sysNotice/findPageByMultiCondition'
//保存
export const sysNoticeSave = path + '/sysNotice/save'
//删除
export const sysNoticeDelete = path + '/sysNotice/delete'

// 权限申请
// 按多条件查找分页
export const menuApplyFind = path + '/menu/find'
//保存
export const menuApplySave = path + '/menu/save'
export const menuApplyUpdate = path + '/menu/update'
//删除
export const menuApplyDelete = path + '/menu/delete'

// 剔除人员名单
// 按多条件查找分页
export const eliminatedPersonnelListFind =
  path + '/extraUser/findPageByMultiCondition'
// 保存
export const eliminatedPersonnelListSave = path + '/extraUser/save'
export const eliminatedPersonnelListUpdate = path + '/extraUser/update'
//数据修改记录
export const dataChangeRecordFind = path + '/dataChangeRecord/findAll'
// 保存
export const dataChangeRecordSave = path + '/dataChangeRecord/saveRecord'
export const dataChangeRecordUpdate = path + '/dataChangeRecord/updateRecord'

//会议纪要
// 按多条件查找分页
export const meetingRecordPage =
  path + '/meetingRecord/findPageByMultiCondition'
// 保存
export const meetingRecordSave = path + '/meetingRecord/save'
// 修改
export const meetingRecordEdit = path + '/meetingRecord/update'
// 上传附件
export const meetingRecordUpload = path + '/meetingRecord/uploadFile'
// 获取附件列表
export const findAttachesList = path + '/meetingRecord/findAllAttaches'

//个人中心
//用户新增全部菜单查询
export const findAllRscByUserNo = path + 'UCR/findAllRscByUserNo'
//用户已选择菜单查询
export const findSelectResourceByUserNo =
  path + 'UCR/findSelectResourceByUserNo'
//新增菜单保存
export const saveOrUpdate = path + 'UCR/saveOrUpdate'
//修改菜单名称保存/
export const changeRscName = path + 'UCR/changeRscName'
//列表禁用
export const changeStatus = path + 'UCR/changeStatus'
//报警规则配置
export const findAllRuleByUserNo = path + 'UCW/findAllRuleByUserNo'
//新增规则
export const warnsaveOrUpdate = path + 'UCW/saveOrUpdate'
//修改规则名称
export const changeRuleName = path + 'UCW/changeRuleName'
//列表禁用
export const warnchangeStatus = path + 'UCW/changeStatus'

// 人员权限变更 - 获取所有记录
export const personnelAuthorityChangeFindAll = path + 'userChangeRecord/findAll'
//变更用户关联的角色
export const changeUserRole = path + 'userChangeRecord/changeUserRelation'
