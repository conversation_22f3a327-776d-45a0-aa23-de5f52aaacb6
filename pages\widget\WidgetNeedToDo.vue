<template>
  <div
    slot="content"
    class="content">
    <el-button @click="postMessage">发信息</el-button>
    <el-table
      :data="tableData"
      :show-header="false"
      size="small"
      stripe
      style="width: 100%"
    >
      <el-table-column
        label="标题"
        prop="title"
        min-width="100"
      />
      <el-table-column
        label="日期"
        prop="date"
        width="140"
      />
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'WidgetNeedToDo',
  layout: 'staticPage',
  data() {
    return {
      tableData: [
        {
          date: '2022-05-02 15:50:34',
          title: '待办事项内容'
        },
        {
          date: '2022-05-04 15:50:34',
          title: '待办事项内容'
        },
        {
          date: '2022-05-01 15:50:34',
          title: '待办事项内容'
        },
        {
          date: '2022-05-03 15:50:34',
          title: '待办事项内容'
        },
        {
          date: '2022-05-03 15:50:34',
          title: '待办事项内容'
        },
        {
          date: '2022-05-03 15:50:34',
          title: '待办事项内容'
        }
      ]
    }
  },
  methods: {
    postMessage() {
      window.parent.postMessage({ data: 1 }, 'http://**************:9730/')
    }
  }
}
</script>

<style scoped>
.content {
  height: 100%;
  background: #fff;
}
</style>
