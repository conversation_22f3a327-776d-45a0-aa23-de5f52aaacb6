##侧边栏案例使用说明


- 组件  
`layouts/component/SideBar.vue` -- 侧边栏组件  
`layouts/component/MenuItem.vue `-- 侧边栏递归子组件  
`components/IconPng.vue` -- 字体图标组件


- 依赖  (按对应路径添加)  
`plugins/route.js `-- 路由拦截器（更新获取菜单资源功能）   
  请求参数serviceName更改为各自应用的编号.如智能桌面为 res   

       const data = await post(resourceListNoPage, {
         userNo: localStorage.getItem('userId'),
         serviceName: 'res'
       })


`lib/Menu.js `-- 菜单树生成工具  
`store/menu.js` -- store子模块（缓存菜单资源）  


侧边栏组件使用

        <side-bar/>



##按钮选项指令说明

`plugins/directive.js ` -- 指令插件   

    Vue.directive('command', {  
        // 当被绑定的元素插入到 DOM 中时……  
        inserted: function(el, binding) {    
        // 聚焦元素
        // console.log('command show control =========')
        let resources = app.store.state['menu']['pageButtonPower'] || []
        if (resources.indexOf(binding.value) === -1) {
        // el.style.display = 'none'
        el.remove()
        }
        }
    })

使用方式： v-command="按钮资源URL" ,如下：

    <el-button v-command="'/system/user/role'" size="small" type="text" @click="distributeRole(row)">分配角色</el-button>

    注： 按钮资源的URL在添加时应保证全局唯一性，否则可能会出现权限误判

##站内系统头部隐藏标识使用
智慧桌面访问子系统，头部隐藏标识使用方式（推荐）
应用桌面访问子系统会在URL

路由拦截器添加以下代码，添加位置详见`plugin/route.js`：

    // 系统初始化时，设置头部显示
    app.store.commit('menu/showHeader', true)
    // 判断是否显示头部
    query.showHeader !== undefined &&
    app.store.commit('menu/showHeader', !!parseInt(query.showHeader))
    await sleep(0)

store文件添加state: showHeader，详见 `store/menu.js` 
组件使用showHeader判断是否显示
      
    // template
    <el-header v-if="showHeader">
      <el-row :gutter="20">
            ————————————————
    // script
    computed: {
    ...mapState('menu', ['showHeader'])
    },     
     


## element-ui主题引入修改

`plugin/element-ui.js`配置element默认主题：

设置组件默认大小为small:

    Vue.use(Element, {
      locale,
      size: 'small'
    })

`assets/css`目录下解压theme.zip

`nuxt.config.js`修改内容引入主题：

    css: [
      '@/assets/css/theme/index.css'
    ]