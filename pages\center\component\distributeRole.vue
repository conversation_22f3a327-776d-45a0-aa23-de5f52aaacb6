<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :width="'1000px'"
      v-bind="$attrs"
      title="角色分配"
      @close="close"
      @open="onOpen"
      v-on="$listeners"
    >
      <div class="text-center">
        <el-transfer
          v-model="value"
          :button-texts="['删除', '添加']"
          :data="data"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }"
          :render-content="renderFunc"
          :right-default-checked="selected"
          :titles="['待选角色', '已选角色']"
          filterable
          style="text-align: left; display: inline-block"
          @change="handleChange"
        />
      </div>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import { relateRole, roleByUser, roleListNoPage } from '@/api/system'

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['visibleDistribute', 'id'],
  data() {
    return {
      loading: false,
      visible: this.visibleDistribute,
      data: [],
      value: [],
      originData: [],
      renderFunc(h, option) {
        return (
          <span title={option.label}>
            {option.roleCode} - {option.label}
          </span>
        )
      },
      selected: []
    }
  },
  created() {
    this.getRole()
  },
  methods: {
    async getRole() {
      const roleAll = await post(roleListNoPage, { id: this.id })
      if (!roleAll.success) return
      this.data = roleAll.data.map(item => {
        item.label = item.roleName
        item.key = item.id
        return item
      })
      const roleNow = await post(roleByUser, { userID: this.id })
      if (!roleNow.success) return
      this.value = roleNow.data.map(item => item.id)
      this.originData = roleNow.data.map(item => item.id)
    },
    handleChange(value, direction, movedKeys) {
      console.log(value, direction, movedKeys)
    },
    onOpen() {
      // 打开
    },
    onClose() {
      // this.$refs['elForm'].resetFields()
    },
    close() {
      this.$emit('update:visibleDistribute', false)
    },
    handelConfirm() {
      // 提交用户信息
      console.log(this.value)
      this.loading = true
      const addIDs = []
      this.value.forEach(item => {
        if (this.originData.indexOf(item) === -1) {
          addIDs.push(item)
        }
      })
      post(relateRole, {
        id: this.id,
        addIDs: addIDs,
        removeIDs: this.originData.filter(
          item => this.value.indexOf(item) === -1
        )
      }).then(res => {
        this.loading = false
        if (res.success) {
          this.$message.success('分配角色成功！')
          this.close()
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
/deep/ .el-transfer-panel {
  width: 350px;
}
</style>
