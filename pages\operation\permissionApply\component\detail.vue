<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'详情'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="800px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="0"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择用户',
              trigger: 'change'
            }
          ]"
          prop="userName"
        >
          <p style="font-size: 20px; margin-bottom: 10px">授权人员：</p>
          <el-table
            v-loading="loading"
            :data="userList"
            :size="'small'"
            border
            style="width: 100%"
          >
            <el-table-column
              label="工号"
              prop="userNo"
            />
            <el-table-column
              label="姓名"
              prop="userName"
            />
          </el-table>

        </el-form-item>
        <el-form-item
          prop="remark"
        >
          <p style="font-size: 20px; margin-bottom: 10px">备注：</p>
          {{ formData.remark || '无' }}

        </el-form-item>
      </el-form>
      <p style="font-size: 20px; margin-bottom: 20px">申请权限（标蓝为申请内容）</p>
      <div style="max-height: 500px; overflow: auto">
        <el-tree
          v-if="visible"
          ref="tree1"
          :data="data1"
          :default-checked-keys="defaultSelected1"
          :default-expanded-keys="defaultExpanded1"
          :props="defaultProps1"
          node-key="id"
        >
          <template
            v-slot="{ node, data }"
          >
            <p :class="{'active': node.checked}">
              {{ data.type === 'plugin' ? '(小部件) -- ' : '' }}{{ node.label }}
            </p>
          </template>

        </el-tree>
      </div>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  dictionaryDtlFindByDictCode,
  resourceListNoPage,
  saveFeedback,
  uploadFile
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { ENUM } from '@/lib/Constant'
import { generateTree } from '@/lib/Menu'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: saveFeedback,
        add: saveFeedback,
        file: uploadFile,
        getDict: dictionaryDtlFindByDictCode
      },
      formData: {},
      userList: [],
      data1: [],
      defaultProps1: {
        children: 'children',
        label: 'name'
      },
      defaultSelected1: [],
      defaultExpanded1: []
    }
  },
  computed: {},
  watch: {
    detail: {
      deep: true,
      handler: function() {
        this.formData = this.detail
        this.formData.newRscList = this.formData.newRscList || ''
        this.defaultSelected1 = this.formData.newRscList.split(',')
        this.defaultExpanded1 = this.formData.newRscList.split(',')
        this.userList = (this.formData.userNo || '').split(',').map(item => {
          return {
            userNo: item.split('|')[0],
            userName: item.split('|')[1]
          }
        })
        console.log(this.userList)
      }
    }
  },
  async created() {
    // console.log('')
    this.getMenuList()
  },
  methods: {
    show(e) {
      console.log(e)
    },
    getMenuList() {
      post(resourceListNoPage).then(res => {
        this.data1 = generateTree(res.data.filter(item => item.status !== 0))
          .sort((a, b) => (a.type < b.type ? -1 : 1))
          .map(item => {
            item.disabled = true
            return item
          })
      })
    },
    onOpen() {
      this.formData = this.detail
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },
    clearForm() {
      this.formData = {}
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  &:hover {
    background: #eee;
    color: #fff;
  }
}
/deep/ .active {
  color: rgba(38, 132, 239, 0.95);
}
</style>
