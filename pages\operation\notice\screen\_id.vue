<template>
  <!--质量处晨会看板-->
  <div class="screen-wrapper">
    <div class="screen-header">
      <div class="screen-header-inner">
        <div class="header-left header-side">
          <div class="tab-box">
            <!--            发布部门：&#45;&#45;&#45;&#45;-->
          </div>
        </div>
        <div class="header-title">
          <span class="header-arrow"/>
          <span class="header-arrow header-arrow-right"/>
          <img

            src="../../../../assets/images/screen/header-bg.png"
            alt=""
            @drag.prevent
          >
          <div class="header-text">板材事业部信息发布</div>
        </div>
        <div class="header-right header-side">
          <div class="tab-box">
            <!--            发布时间：&#45;&#45;&#45;&#45;-->
          </div>
        </div>
      </div>
    </div>  
    <div class="screen-content ">
      <div class="carousel-wrapper">
        <el-carousel
          ref="carousel"
          :autoplay="false"
          @change="changeIndex">
          <el-carousel-item
            v-for="(item, index) in noticeList"
            :key="item.id">
            <!-- 文字-->
            <template v-if="item.noticeType == 1">
              <div class="notice-text">
                <p class="">
                  {{ item.content }}
                </p>
              </div>
            </template>
            <!-- 图片-->
            <template v-if="item.noticeType == 2">
              <el-carousel
                :indicator-position="'none'"
                :autoplay="true">
                <el-carousel-item
                  v-for="(file) in item.fileList"
                  :key="file.id">
                  <img
                    :src="downloadUrl + file.id"
                    class="notice-img"
                    alt=""
                  >
                </el-carousel-item>
              </el-carousel>
            </template>
            <!-- 视频-->
            <template v-if="item.noticeType == 3 && index === active">
              <video
                v-for="file in item.fileList"
                :key="file.id"
                :src="downloadUrl + file.id"
                autoplay
                class="video"/>
            </template>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import {
  attachFindAllByMultiCondition,
  downloadFileById,
  sysNoticeFind
} from '@/api/system'
import { imgIp } from '@/config'

export default {
  layout: 'screenLayout',
  data: () => {
    return {
      active: 0,
      noticeDept: null,
      noticeList: [],
      switchTask: null,
      downloadUrl: imgIp + downloadFileById
    }
  },
  computed: {},
  created() {
    this.noticeDept = parseInt(this.$route.params.id)
    this.getNotice()
  },
  mounted() {},
  methods: {
    getNotice() {
      post(sysNoticeFind, {
        status: 2,
        // noticeDept: this.noticeDept,
        pageIndex: 1,
        pageSize: 1000
      }).then(res => {
        console.log(res)
        const currentTime = this.$moment().format('YYYY-MM-DD HH:mm:ss')
        const activeNotice = res.data.content.filter(
          item => currentTime >= item.startTime && currentTime < item.endTime
        )
        activeNotice.forEach(item => {
          item.fileList = []
          if (item.noticeType !== 1) {
            // 获取附件
            post(attachFindAllByMultiCondition, {
              relatedId: item.id
            }).then(res => {
              Object.assign(item, {
                fileList: res.data
              })
            })
          }
        })
        this.noticeList = activeNotice
        this.$nextTick(() => {
          this.startTask()
        })
      })
    },
    changeIndex(index) {
      this.active = index
      this.startTask()
    },
    startTask() {
      this.switchTask && clearTimeout(this.switchTask)
      const timer =
        0 +
        (this.noticeList[this.active] &&
          this.noticeList[this.active].switchTime)
      this.switchTask = setTimeout(() => {
        this.active =
          this.noticeList.length === this.active + 1 ? 0 : this.active + 1
        this.$refs.carousel.next()
        this.startTask()
      }, timer * 1000)
    }
  }
}
</script>

<style scoped lang="less">
.screen-wrapper {
  position: relative;
  display: flex;
  height: 100vh;
  width: 100vw;
  flex-direction: column;
  background: #041a21 url('../../../../assets/images/screen/screen-bg.png')
    repeat center;
  &:after {
    content: '';
    position: absolute;
    left: 25px;
    right: 25px;
    height: 4px;
    bottom: 8px;
    background: url('../../.././../assets/images/screen/footer-line.png')
      no-repeat;
    background-size: 100% 100%;
  }
  .screen-header {
    height: 71px;
    margin: 0 25px;
    &-inner {
      position: relative;
      display: flex;
      width: 100%;
    }
    .header-side {
      position: relative;
      flex: 1;
      margin: 7px 0;
      white-space: nowrap;
      border-top: 1px solid #136480;
      align-items: center;
      display: flex;
      overflow: hidden;
      .tab-box {
        flex: 1;
        overflow-y: hidden;
        overflow-x: auto;
      }
      &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        left: 0;
        bottom: 0;
        background: url(../../../../assets/images/screen/header-line.png) repeat
          left;
      }
    }
    .header-left {
      margin-right: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:after {
        content: '';
        position: absolute;
        left: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      .tab-box {
        flex: 1;
        text-align: left;
        color: rgba(156, 196, 241, 0.74);
      }
    }
    .header-right {
      margin-left: 8px;
      text-align: right;
      color: rgba(156, 196, 241, 0.74);
      &:after {
        content: '';
        position: absolute;
        right: -16px;
        top: 0;
        bottom: 0;
        padding: 0;
        margin: auto;
        display: block;
        width: 12px;
        height: 40px;
        box-sizing: border-box;
        background: #1fc6ff;
        transform: rotate(180deg);
        clip-path: polygon(
          5px 0,
          calc(100%) 0,
          calc(100%) 1px,
          5px 1px,
          1px 5px,
          1px calc(100% - 5px),
          5px calc(100% - 1px),
          100% calc(100% - 1px),
          100% 100%,
          calc(100% - 5px) 100%,
          5px 100%,
          0 calc(100% - 5px),
          0 5px
        );
      }
      &:before {
        background: url(../../../../assets/images/screen/header-line2.png)
          repeat right;
      }
    }
    .header-btn {
      position: relative;
      display: inline-block;
      height: 35px;
      margin: 0 5px;
      padding: 0 10px;
      min-width: 14%;
      background: rgba(31, 198, 255, 0.12);
      border: 1px solid rgba(31, 198, 255, 0.2);
      font-weight: 400;
      font-size: 16px;
      line-height: 35px;
      text-align: center;
      color: #1fc6ff;
      cursor: pointer;
      &.active {
        color: #fff;
        font-weight: bold;
        background: rgba(31, 198, 255, 0.3);
      }
      &.disabled {
        color: #999;
        font-weight: bold;
        background: rgba(70, 77, 79, 0.2);
      }
      &:after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border: 1px solid rgba(31, 198, 255, 0.2);
      }
    }
    .header-title {
      position: relative;
      height: 71px;
      padding: 3px 0;
      img {
        display: block;
        height: 100%;
        margin: auto;
        user-select: none;
        -webkit-user-drag: none;
      }
      .header-text {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        padding-left: 65px;
        background: url(../../../../assets/images/screen/header-logo.png)
          no-repeat left;
        background-size: 50px;
        height: 71px;
        line-height: 71px;
        font-size: 32px;
        color: #fff;
        font-weight: bold;
        letter-spacing: 1px;
        white-space: nowrap;
      }
    }
  }
  .screen-content {
    flex: 1;
    margin: 25px 25px 32px;
    overflow: hidden;
    position: relative;
  }
  .header-arrow {
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 80px;
    height: 20px;
    background-image: linear-gradient(100grad, #1ec5fe, #106685);
    animation: move-arrows 1s linear infinite;
    transform-origin: center;
    mask-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAUCAYAAAAa2LrXAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAH8SURBVHgB7ZhLSgNBEIb/6uBbURcKgo/ZuHChCLoxopgjeAO9gd4gnkBvoDfwCBFFoxDxBSq4iUFwIfhANCYhKXvEQMg0JulpSC38FiGpRfFRNVXpHsCAd8jTXpITXoL74ID+FPeOnPDCTIpb4ABJfgoGOQYSzFgqtWITIfHlerJYVAUMPGUxhZBI81OGyCYIP50lwsroIW8jBN1fmNJ5Wn7zjY0e8AzCIMwvWMAvLOvPdPlnWMnBdhwT47MiX7giCvMjU1DvFo/bkPC/lmN6ZHYy87QKC4ZS3Nmmx4QJnRX57jMLdAoLJPkpUzAdozTlEENVp70jtto5j7P0mevAfnWnh494EhZI8lNoQFIv7zX9eMfhSDJSwvhYkidggRQ/Qg2M41LEhn6847DANC6I4OZ+jm5gQbP9ahZQgqRkv7oK2GxJyX6q7ozthpjy14478hwiX5P86hvhpO4uB44NcX1s2IAFpu7mFa4fo3QLC5rpV/tP5L94f/qRZLlaSPCjhuSArUyU1uFIrqhw9xClK1ggxY/qlpN0lRPkF6kOeGfcxwUkXcn579jyWcScFU+YX+AYw1nsupLzef7AnKviSfQLFFDfBdf1Mnl1Iefz2oULnadgI2dCml+ggOl5OteL0b+k76k8rBZyJS+z9PauL+kl4GmgA5cIiTS/b5T7QUH8z5WLAAAAAElFTkSuQmCC');
    mask-size: contain;
  }
  .header-arrow-right {
    left: auto;
    right: 15px;
    transform: rotate(180deg);
  }
  @keyframes move-arrows {
    0% {
      mask-position: 0 0;
    }
    100% {
      mask-position: 40px 0;
    }
  }
}
.notice-text {
  color: #20efe8;
  h3 {
    font-size: 40px;
    text-align: center;
    margin-bottom: 3%;
    margin-top: 2%;
  }
  p {
    font-size: 30px;
    text-indent: 2em;
    letter-spacing: 5px;
    margin: 0 10%;
    line-height: 2;
  }
}
.video {
  height: 100%;
  width: 100%;
}
.notice-img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
/deep/ .carousel-wrapper {
  height: 100%;
  width: 100%;
  .el-carousel {
    height: 100%;
    .el-carousel__container {
      height: 100%;
      .el-carousel__arrow {
        width: 40px;
        height: 40px;
        i {
          font-size: 25px;
        }
      }
      .el-carousel__arrow--right {
        right: 10px;
      }
      .el-carousel__arrow--left {
        left: 10px;
      }
      .el-carousel__item {
        .img-box {
          height: 100%;
          background-size: contain;
          max-width: 800px;
          margin: 0 auto;
        }
      }
    }
  }
  .carousel-item {
    height: 100%;
    width: 100%;
  }
  .carousel-panel {
    margin: 20px;
  }
}
</style>
