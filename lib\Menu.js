import { post } from '@/lib/Util'
import jwt from 'jsonwebtoken'
import { getLoginMode, pageLogSave, resourceListNoPage } from '@/api/system'
import { findOneUserByUserNo } from '@/api/desktop'
const sleep = function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
/**
 * 根据后端全量列表生成树状菜单结构数据
 * @param menuList 需处理菜单列表
 * @param pid  父级id
 * @returns {*[]}
 */
export const getMenuData = function(menuList = [], pid = '', pages) {
  const data = []
  const parent = menuList.find(item => item.id === pid)
  // 本次递归第一级菜单
  for (let item of menuList) {
    if (!item.parentId) item.parentId = ''
    item.disabled = !item.status
    if (item.parentId === pid) {
      const index = menuList.indexOf(item)
      item.routes = parent.routes + ' > ' + item.routes
      data.push({
        id: item.id,
        parentId: item.parentId,
        serviceName: item.serviceName,
        ip: item.ip,
        port: item.port,
        name: item.name,
        icon: item.icon,
        deskIcon: item.deskIcon,
        url: item.url,
        type: item.type,
        code: item.code,
        isShow: item.isShow,
        sort: item.sort,
        status: item.status,
        routes: item.routes,
        pluginSize: item.pluginSize,
        handleDeveloper: item.handleDeveloper,
        handleUser: item.handleUser,
        drop: null
      })
    }
  }
  // 根据sort排序
  data.sort((a, b) => a.sort - b.sort)
  // 本次递归二级菜单
  for (let item of data) {
    item.children = getMenuData(menuList, item.id, pages)
    if (!item.children.length) {
      delete item.children
      pages.push({
        id: item.id,
        name: item.name,
        routes: item.routes
      })
    }
  }
  return data
}
/**
 * 生成树结构
 * @param list
 * @returns {*[]}
 */
export const generateTree = function(list, type = 1) {
  const roots = []
  const pages = []
  const menus = list.map(item => {
    item.routes = item.name
    list.findIndex(node => node.id === item.parentId) === -1 && roots.push(item)
    return Object.assign({}, item)
  })
  roots.forEach(item => {
    item.children = getMenuData(menus, item.id, pages)
  })
  if (type === 1) {
    return roots
  } else {
    return { roots, pages }
  }
}

/**
 * 获取菜单完整路径
 */
export function getPagesRoute(menus, page) {
  const routes = []
  routes.push(page)
  getParent(routes, menus, page)
  return Object.assign({}, page, {
    routes: routes.map(item => item.name).join('->')
  })
}

function getParent(routes, menus, page) {
  const match = menus.find(item => item.id === page.parentId)
  // console.log(match, page)
  if (match) {
    routes.shift(match)
    getParent(routes, menus, page, match)
  }
}

/* 不缓存添加到tag标签上的页面 */
export const notKeepalivePage = [
  '/',
  '/login',
  '/password',
  '/widget/WidgetEarlyWarning',
  '/widget/WidgetServiceVisitTrend'
]

/**
 * 打开新页面
 * @param app vm实力
 * @param name 路由名
 * @param path 路由路径
 * @param fullPath 完整路径
 * @param params 路由参数
 * @param query 路由query参数
 */
export const openNewPage = function(app, name, path, fullPath, params, query) {
  if (!app.store) {
    return
  }
  // 过滤url参数，去除主系统添加的额外参数
  const filterFullPath = filterUrl(fullPath)
  let pageOpenedList = app.store.state.menu.pageOpenedList
  let openedPageLen = pageOpenedList.length
  let i = 0
  let tagHasOpened = false
  while (i < openedPageLen) {
    if (path === pageOpenedList[i].path) {
      // 页面已经打开
      app.store.commit('menu/updateOpenedList', {
        index: i,
        params: params,
        fullPath: filterFullPath,
        query: query
      })
      tagHasOpened = true
      break
    }
    i++
  }
  if (!tagHasOpened) {
    !notKeepalivePage.find(item => item === path) &&
      app.store.commit('menu/setPageTag', {
        name: name,
        path: path,
        fullPath: filterFullPath,
        params: params,
        query: query
      })
  }
  app.store.commit('menu/setCurrentPageName', filterFullPath)
  visitCount(filterFullPath, app.store.state.menu)
}

// 标签固定页配置（固定在左侧）
// [{
//   path: '/feature/factory?factory=1',
//   fullPath: '/feature/factory?factory=1',
//   params: {},
//   query: {},
//   fixed: true
// }]
const fixedPageList = []
/**
 * 初始化pageOpenedList,添加固定页
 * @param app nuxt实例
 */
export const initialOpenedList = function(app) {
  const openList = app.store.state.menu.pageOpenedList.filter(
    item => !fixedPageList.find(fixed => fixed.fullPath === item.fullPath)
  )
  // 塞入固定页
  openList.unshift(...fixedPageList)
  app.store.commit('menu/setOpenedList', openList)
}

/**
 * 登陆重定向
 */
export function redirectLogin() {
  const userName = localStorage.getItem('userId'),
    href =
      window.location.pathname === '/login'
        ? window.location.origin
        : window.location.href,
    redirectUrl = `http://172.25.63.126:9701/login/ssoLogin?userId=${userName}&url=${href}`
  localStorage.removeItem('userId')
  window.location.href = redirectUrl
}

/**
 * 退出登陆
 */
export async function logout(vm) {
  // 清楚用户数据
  localStorage.removeItem('token')
  // 删除菜单、按钮权限数据
  const store = vm.$store || vm.store
  await goToLogin(vm)
  store.commit('menu/clearPageTag', [])
  store.commit('menu/setMenuStore', [])
  store.commit('menu/setAllMenus', [])
  store.commit('menu/setUserMenuList', [])
  store.commit('menu/setPageButtonPower', [])
  store.commit('desktop/activeIndex', null)
  store.commit('desktop/desktopList', [])
  store.commit('desktop/desktopData', {})
  store.commit('desktop/shortcutList', [])
  store.commit('desktop/miniAppList', [])
}

export async function goToLogin(app) {
  const login = await app.$postService(getLoginMode, {})
  if (login.data.loginMode === '2') {
    const _router = app.$router || app.router
    _router.history.current.path !== '/login' && _router.replace('/login')
    return true
  } else {
    // 重定向搭到后端登录请求
    redirectLogin()
  }
}

/**
 * 记录用户访问情况
 * @param fullPath
 * @param menu
 */
let visitCountTask = null
const visitCountInterval = 2000 // 有效访问时间
export async function visitCount(fullPath, menu) {
  visitCountTask && clearTimeout(visitCountTask)
  await sleep(0)
  // 匹配菜单数据
  const matchMenu = menu.allMenus.find(item => item.url === fullPath)
  const token = localStorage.getItem('token')
  if (!matchMenu || !token) return
  // 判断是否为叶子节点
  const children = menu.allMenus.filter(
    item => item.parentId === matchMenu.id && item.type === 'menu'
  )
  if (children.length) return
  // 保存用户访问记录
  visitCountTask = setTimeout(() => {
    post(pageLogSave, {
      resourceId: matchMenu.id,
      userNo: localStorage.getItem('userId')
    })
  }, visitCountInterval)
}

/**
 * 删除url中某个参数,并修改地址栏
 */
export function funcUrlDel() {
  const url = filterUrl(window.location.href)
  window.history.replaceState(null, null, url)
}

/**
 * 删除url中指定参数,并返回
 */
export function filterUrl(path) {
  if (path.indexOf('?') === -1) return path
  const baseUrl = path.split('?')[0] + '?'
  const query = path.split('?')[1]
  const obj = {}
  const arr = query.split('&')
  for (let i = 0; i < arr.length; i++) {
    arr[i] = arr[i].split('=')
    obj[arr[i][0]] = arr[i][1]
  }
  ;['userId', 'token', 'org', 'showHeader'].forEach(item => delete obj[item])
  const url =
    baseUrl +
    JSON.stringify(obj)
      .replace(/[\"\{\}]/g, '')
      .replace(/\:/g, '=')
      .replace(/\,/g, '&')
  return url.endsWith('?') ? url.split('?')[0] : url
}

export async function getMenus(store) {
  const userNo = localStorage.getItem('userId')
  post(findOneUserByUserNo, { userNo }).then(res => {
    if (res.success) {
      localStorage.setItem('userDetail', JSON.stringify(res.data))
    }
  })
  const data = await post(resourceListNoPage, {
    userNo
  })
  if (data.success) {
    const list = data.data.map(item => {
      delete item.createDateTime
      delete item.createUserNo
      delete item.desc
      delete item.updateDateTime
      delete item.updateUserNo
      return item
    })
    const menu = list.filter(item => item.type === 'menu')
    const widget = list.filter(item => item.type === 'plugin')
    const button = list
      .filter(item => item.type === 'button')
      .map(item => item.url)
    const menuTree = generateTree(menu, 2)
    // console.log(menuTree)
    // 缓存菜单、按钮权限数据
    store.commit('menu/setAllMenus', list)
    store.commit('menu/setMenuStore', menuTree.roots)
    store.commit('menu/setMenuPages', menuTree.pages)
    store.commit('menu/setUserMenuList', [])
    store.commit('menu/setPageButtonPower', button)
    store.commit('menu/widgetList', generateTree(widget))
  }
  return new Promise(resolve => resolve(true))
}

// 验证token状态
export function getTokenStatus() {
  const token = localStorage.getItem('token')
  if (!token) return false
  //
  let userInfo = jwt.decode(token)
  const timeNow = new Date().getTime()
  return timeNow < userInfo.exp * 1000
}
