// store/icon.js
// This module is used to cache icon data to reduce API calls

export const state = () => ({
  // Cache for icon data, keyed by icon ID
  iconCache: {}
})

export const mutations = {
  // Add or update an icon in the cache
  setIcon(state, { id, resource }) {
    state.iconCache = {
      ...state.iconCache,
      [id]: resource
    }
  }
}

export const actions = {
  // 获取图标资源，如果缓存中有则直接返回，否则从服务器获取
  async fetchIcon({ commit, getters }, id) {
    // 如果缓存中已有该图标，直接返回
    if (getters.hasIcon(id)) {
      return getters.getIcon(id)
    }

    // 否则从服务器获取
    try {
      const { post } = require('@/lib/Util')
      const res = await post('/res/iconImg/findById', { id })

      if (res.success && res.data) {
        // 将图标添加到缓存
        commit('setIcon', { id, resource: res.data.resource })
        return res.data.resource
      } else {
        // 如果获取失败，返回默认图标
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAYAAADimHc4AAAABmJLR0QA/wD/AP+gvaeTAAACqklEQVR4nO2cP3LTQBSH34vTZphxByRXgDJHcJEjUFBwh0y4BDkAPU0oGNoMh3DJkIoZKMPgFBSMsxSxsP6stCtZ9m/G+b7G8ntP+1b6tCtXNgMAAAAAAAAAAAAAgH3Hs6rO58dmdmkWZmZ25CGsEsGsOF59ejlmwSys6zwSK+q8iEXyXo5V8qtzI7Hi2K08v3LezItYJO/lWOn6Hq6jGSuNeRfMrg/NLxYfXn1L3dq0gPP5sbnPLYRpdWLxCVYE1PJrAbULsGAeiUUF1OR6i7T2/OqzLD0ityJgkNRwe/83vPhz9fpH1+096Eo+jOiXZmHaTIRm6f/JdNDIx07oGWvp6bWy8pfqk5fbLz9twacHh/YuUZUhwGyWbpy+AG+taw+35zeRlkoVT/+AsUM95rNmUZUcAUfpxokRGhOrnuSR2PpwiLTYSsy4iRkPQnzPbh37SWLELAGVPS6z8YCnOl7Qa6vYqOfAVVX6mveLpkqegFiDHe7l8VND5IITqyoWy+45wrYXIUNAXpPmvplz2jb38lg+c1VFt8yhPbvptQJ2/wLeZC/f4AXct59ZWloL/QTs/AWcP43upOQFnEXyvfH0/fdhagdyenayy3Zb59Mz77zHPVcAjA0CxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQEzOn/bdbX8ae8vvVEFaQPAvo0zlEeJm16mapICfTC7M7NcoM3pc3C6X9jZVlBTw883zrz6ZvHTzKwu2GGdue83CzT7eL+3084nfqCcDAAAAAAAAAAAAAAB6/gHztkDOhvHqxwAAAABJRU5ErkJggg=='
      }
    } catch (error) {
      console.error('获取图标失败:', error)
      // 如果发生错误，返回默认图标
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAYAAADimHc4AAAABmJLR0QA/wD/AP+gvaeTAAACqklEQVR4nO2cP3LTQBSH34vTZphxByRXgDJHcJEjUFBwh0y4BDkAPU0oGNoMh3DJkIoZKMPgFBSMsxSxsP6stCtZ9m/G+b7G8ntP+1b6tCtXNgMAAAAAAAAAAAAAgH3Hs6rO58dmdmkWZmZ25CGsEsGsOF59ejlmwSys6zwSK+q8iEXyXo5V8qtzI7Hi2K08v3LezItYJO/lWOn6Hq6jGSuNeRfMrg/NLxYfXn1L3dq0gPP5sbnPLYRpdWLxCVYE1PJrAbULsGAeiUUF1OR6i7T2/OqzLD0ityJgkNRwe/83vPhz9fpH1+096Eo+jOiXZmHaTIRm6f/JdNDIx07oGWvp6bWy8pfqk5fbLz9twacHh/YuUZUhwGyWbpy+AG+taw+35zeRlkoVT/+AsUM95rNmUZUcAUfpxokRGhOrnuSR2PpwiLTYSsy4iRkPQnzPbh37SWLELAGVPS6z8YCnOl7Qa6vYqOfAVVX6mveLpkqegFiDHe7l8VND5IITqyoWy+45wrYXIUNAXpPmvplz2jb38lg+c1VFt8yhPbvptQJ2/wLeZC/f4AXct59ZWloL/QTs/AWcP43upOQFnEXyvfH0/fdhagdyenayy3Zb59Mz77zHPVcAjA0CxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQAwCxCBADALEIEAMAsQgQEzOn/bdbX8ae8vvVEFaQPAvo0zlEeJm16mapICfTC7M7NcoM3pc3C6X9jZVlBTw883zrz6ZvHTzKwu2GGdue83CzT7eL+3084nfqCcDAAAAAAAAAAAAAAB6/gHztkDOhvHqxwAAAABJRU5ErkJggg=='
    }
  }
}

export const getters = {
  // Get an icon from the cache by ID
  getIcon: state => id => {
    return state.iconCache[id] || null
  },

  // Check if an icon exists in the cache
  hasIcon: state => id => {
    return !!state.iconCache[id]
  }
}
