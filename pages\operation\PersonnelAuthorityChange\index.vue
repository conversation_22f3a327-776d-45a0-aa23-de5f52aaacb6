<template>
  <div class="data-change-record">
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <!-- 添加序号列 -->
      <el-table-column
        label="序号"
        width="100"
        fixed="left">
        <template slot-scope="scope">
          {{ (page.pageIndex - 1) * page.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="userName"
        label="用户名"
        width="120" />
      <el-table-column
        prop="userNo"
        label="工号"
        width="100" />
      <el-table-column
        label="原始数据"
        prop="oldData"
        show-overflow-tooltip
      />
      <el-table-column
        label="新数据"
        prop="newData"
        show-overflow-tooltip
      />
      <el-table-column
        label="操作"
        width="180"
        fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isConfirm == 0"
            type="text"
            size="small"
            @click="handleConfirm(scope.row)">
            确认
          </el-button>
          <span
            v-else 
            class="confirmed-text"
          >已确认</span>
        </template>
      </el-table-column>
    </el-table>
  
    <div class="pagination-container">
      <el-pagination
        :current-page="page.pageIndex"
        :page-size="page.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { personnelAuthorityChangeFindAll, changeUserRole } from '@/api/system'
import { post } from '@/lib/Util'

export default {
  layout: 'menuLayout',
  name: 'operation-dataChangeRecord',
  data() {
    return {
      tableData: [],
      page: {
        pageIndex: 1,
        pageSize: 10,
        total: 346
      }
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      try {
        const params = {
          pageSize: this.page.pageSize,
          pageIndex: this.page.pageIndex
        }
        const res = await post(personnelAuthorityChangeFindAll, params)
        this.tableData = res.data.content || []
        this.page.total = res.data.totalElements || 0
      } catch (error) {
        console.error('获取数据失败:', error)
      }
    },
    handleCurrentChange(currentPage) {
      this.page.pageIndex = currentPage
      this.loadData()
    },
    handleSizeChange(size) {
      this.page.pageSize = size
      this.page.pageIndex = 1 // 切换每页条数时重置到第一页
      this.loadData()
    },
    async handleConfirm(row) {
      try {
        await this.$confirm('确认执行此操作?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await post(changeUserRole, { id: row.id })
        this.$message.success('确认成功')
        this.loadData() // 刷新数据
      } catch (error) {
        if (error !== 'cancel') {
          // 排除取消操作的情况
          this.$message.error('确认失败：' + (error.message || '未知错误'))
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.data-change-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: right;
    align-items: center;

    .total-text {
      margin-right: 20px;
    }

    .page-text {
      margin-left: 20px;
    }
  }
}

:deep(.el-table) {
  th {
    background-color: #f5f7fa;
  }
}

.confirmed-text {
  color: #909399;
}
</style>
