// 编辑 新增详情用mixins，不适应页面特有操作的需要自定义相同名称函数覆盖

import { post } from '@/lib/Util'

export default {
  data: () => {
    return {
      title: '',
      editType: null,
      loading: false
    }
  },
  methods: {
    add() {
      this.title = '新增'
      this.editType = 'add'
      this.clearForm()
    },
    /**
     * 开启编辑
     * @param data 编辑元数据
     */
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, data)
    },
    clearForm() {
      for (let k in this.formData) {
        if (Array.isArray(this.formData[k])) {
          this.formData[k] = []
        } else {
          this.$nextTick(() => {
            this.formData[k] = undefined
          })
        }
      }
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].clearValidate == 'function'
      ) {
        this.$refs['form'].clearValidate()
      }
      this.afterClear()
    },
    afterClear() {},
    onOpen() {},
    close() {
      this.visible = false
      this.$nextTick(() => {
        this.$emit('success')
      })
      this.clearForm()
    },
    submitBefore() {
      // 提交前操作
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            this.submitBefore()
            post(this.url.edit, this.formData).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            this.submitBefore()
            post(this.url.add, this.formData).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    }
  }
}
