<template>
  <div class="page-container">
    <div 
      v-if="list.length > 0" 
      class="menu-grid">
      <nuxt-link 
        v-for="(item, index) in list"
        :key="index"
        :to="item.port === '9730' ? item.url : ''"
        class="menu-item"
        @click.native="handleItemClick(item)"
      >
        <IconPng
          :icon-name="item.icon"
          class="icon-svg" />
        <div class="item-name">{{ item.customName }}</div>
      </nuxt-link>
    </div>
    <div 
      v-else 
      class="empty-state">
      <div class="empty-icon">
        <img 
          src="@/assets/desktop/applist/computer.png" 
          alt="empty-menu" >
      </div>
      <h3 class="empty-title">暂无可用菜单</h3>
      <div class="empty-description">当前未配置任何菜单项，请在菜单配置中添加</div>
      <div class="empty-description">（温馨提示: 请确保所勾选菜单在左侧列表中存在，左侧列表中没有则无法查看所勾选菜单）</div>
    </div>
  </div>
</template>

<script>
import { getHomePageResource } from '@/api/system'
import { post } from '@/lib/Util'
import IconPng from '@/components/IconPng'

export default {
  components: { IconPng },
  data() {
    return {
      loading: false,
      list: [],
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId')
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      const res = await post(getHomePageResource, {
        userNo: localStorage.getItem('userId')
      })
      this.list = res.data
    },
    handleItemClick(item) {
      if (item.port !== '9730') {
        window.open(
          'http://' +
            item.ip +
            ':' +
            item.port +
            item.url +
            '?org=redirect&showHeader=1&token=' +
            this.token +
            '&userId=' +
            this.userId,
          '_blank'
        )
      }
    }
  }
}
</script>

<style scoped lang="less">
.icon-svg {
  background: linear-gradient(145deg, #4299e1, #63b3ed);
  padding: 12px;
  border-radius: 12px;
  height: 56px;
  width: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  img {
    // 如果icon是img标签
    height: 32px;
    width: 32px;
  }

  &:hover {
    transform: scale(1.1);
    background: linear-gradient(145deg, #3182ce, #4299e1);
  }
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  padding: 30px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f5f7fa);
  border-radius: 16px;
  box-shadow: 8px 8px 16px rgba(174, 174, 192, 0.15),
    -8px -8px 16px rgba(255, 255, 255, 0.7);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  text-decoration: none;
  color: #2c3e50;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      45deg,
      rgba(66, 153, 225, 0.1),
      rgba(99, 179, 237, 0.1)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 12px 12px 20px rgba(174, 174, 192, 0.2),
      -12px -12px 20px rgba(255, 255, 255, 0.8);

    &::before {
      opacity: 1;
    }
  }
}

.item-name {
  font-size: 17px;
  font-weight: 600;
  text-align: center;
  color: #3d4852;
  margin-top: 8px;
  letter-spacing: 0.5px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24px;

  .empty-image {
    width: 120px;
    height: 120px;
    opacity: 0.6;
  }
}

.empty-title {
  font-size: 24px;
  color: #4a5568;
  margin-bottom: 12px;
  font-weight: 600;
}

.empty-description {
  font-size: 16px;
  color: #718096;
  max-width: 1000px;
  line-height: 1.6;
}
</style>
