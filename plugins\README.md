
## 统一认证前端对接说明

- `/plugins` 文件夹下添加两个插件：

    -  `interceptors.js`  axios拦截器，用于拦截请求，在请求头放入token信息，以及统一错误处理等

    -  `route.js`  路由拦截器，用于记录token信息、权限控制、判断登录状态，做登录重定向等操作

- `nuxt.config.js` 添加配置
    
        module.exports = {
            plugins: [
               { src: '@/plugins/interceptors', ssr: false },
               { src: '~/plugins/route', ssr: false },
            ]
        }

>说明： 目前登录重定向地址默认为 http://172.25.63.126:9701  
> 如有需要请修改interceptors.js、route.js中的对应地址