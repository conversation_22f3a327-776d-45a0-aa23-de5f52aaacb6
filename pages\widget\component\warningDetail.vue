<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'报警历史记录'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="94%"
      @close="clearForm"
      @open="tabActive = 'first';onOpen"
      v-on="$listeners"
    >
      <div
        class="page-content"
        style="position: relative">
        <div
          style="position: absolute; right: 0; top: 0;z-index: 999">
          <el-form
            ref="form"
            :model="searchForm"
            inline
          >
            <el-form-item
              label="时间"
              prop="dateTime"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                align="right"
                @input="handleDateChange" />
            </el-form-item>
            <el-form-item
              :label-width="'0'"
            >
              <el-button
                type="primary"
                @click="query(true)">查询
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-tabs v-model="tabActive">
          <el-tab-pane
            label="处理统计"
            name="first">
            <br>
            <el-row :gutter="40">
              <el-col :span="12">
                {{ popHistory.handleList.length ? '报警规则：' + popHistory.handleList[0].ruleName : '' }}
                <br><br>
                <el-table
                  :data="popHistory.handleList"
                  :max-height="'400px'"
                  size="small"
                  stripe
                  border
                  style="width: 100%"
                >
                  <el-table-column
                    label="处理意见"
                    prop="alertAdvice"
                  />
                  <el-table-column
                    label="次数"
                    prop="count"
                  />
                </el-table>
              </el-col>
              <el-col :span="12">
                <div
                  v-if="tabActive === 'first' && visible"
                  style="height: 400px">
                  <pie-chart :chart-data="handleObj.chart" />
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane
            label="报警历史记录"
            name="second">
            <br>
            <div
              class="page-operate"
              style="align-items: flex-end">
              <div style="margin-bottom: 10px; text-align: right">
                <el-button
                  type="primary"
                  @click="handleHistoryExport"
                >导出
                </el-button>
              </div>
            </div>
            <el-table
              :data="popHistory.list"
              :max-height="'400px'"
              size="small"
              stripe
              border
              style="width: 100%"
            >
              <el-table-column
                label="所属模块"
                prop="moduleName"
                width="100"
              >
                <!--          <template-->
                <!--            v-slot="{row}"-->
                <!--          >-->
                <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
                <!--          </template>-->
              </el-table-column>
              <el-table-column
                label="报警描述"
                prop="alertContent"
                min-width="100"
              />
              <el-table-column
                label="设备名"
                show-overflow-tooltip
                prop="deviceName"
                width="180"
              />
              <el-table-column
                label="区域名"
                prop="areaName"
                width="100"
              />
              <el-table-column
                label="报警类型"
                prop="alertTypeName"
                width="100"
              />
              <el-table-column
                label="报警等级"
                prop="alertLevel"
                width="90"
              >
                <template v-slot="{row}">
                  <el-tag
                    v-if="row.alertLevel == 1"
                    type="danger">一级
                  </el-tag>
                  <el-tag
                    v-if="row.alertLevel == 2"
                    type="warning">二级
                  </el-tag>
                  <el-tag
                    v-if="row.alertLevel == 3">三级
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                label="状态"
                prop="status"
                width="80"
              >
                <template v-slot="{row}">
                  <el-tag
                    v-if="row.status == 0"
                    type="danger">
                    未处理
                  </el-tag>
                  <el-tag
                    v-if="row.status == 2"
                    type="success">
                    已处理
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                label="处理人"
                prop="dealUser"
                width="140"
              />
              <el-table-column
                label="报警分析"
                prop="alertAnalysis"
                width="180"
              />
              <el-table-column
                label="处理意见"
                prop="alertAdvice"
                width="180"
              />
              <el-table-column
                label="报警时间"
                prop="createDateTime"
                width="150"
              />
            </el-table>
            <div style="text-align: right; margin-top: 10px">
              <el-pagination
                :current-page="popHistory.pageIndex"
                :page-size="popHistory.pageSize"
                :page-sizes="[10, 20, 30, 40, 100]"
                :total="popHistory.total"
                background
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handlePopSizeChange"
                @current-change="handlePopCurrentChange"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane
            label="班组统计"
            name="third">
            <div style="height: 500px">
              <bars-chart
                v-if="tabActive == 'third'"
                :key="0"
                :chart-data="dataOption.series"
                :x-data="dataOption.xAxis.data"
                :bar-width="100"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import listMixins from '@/mixins/ListMixins'
import {
  exportHistoryByWarningRuleID,
  findHistoryByWarningRuleID,
  findSummaryByWarningRuleID,
  findTrendByWaringRuleIDAndTime
} from '@/api/system'
import NercarEcharts from '@/components/NercarEcharts.vue'
import { post } from '@/lib/Util'
import PieChart from '@/components/chart/pie-chart'
import moment from 'moment'
import BarsChart from '@/components/chart/bars-chart.vue'

export default {
  components: { BarsChart, PieChart },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  // 班组统计
  data: () => {
    return {
      dataOption: {
        xAxis: [
          {
            type: 'category',
            data: []
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            data: [],
            name: '报警次数'
          }
        ]
      },
      visible: false,
      searchForm: {},
      handleObj: {
        chart: []
      },
      tabActive: 'first',
      popHistory: {
        id: null,
        warningRuleID: null,
        list: [],
        handleList: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date()).format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '昨日',
            onClick(picker) {
              const end = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 23:59:59')
              )
              const start = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('isoWeek')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {},
  async created() {
    /*this.searchForm.dateTime = [
      this.$moment()
        .subtract(7, 'days')
        .format('YYYY-MM-DD'),
      this.$moment().format('YYYY-MM-DD')
    ]*/
  },
  destroyed() {},
  mounted() {},
  // 班组统计
  methods: {
    handleDateChange() {
      this.$forceUpdate()
    },
    getData(id) {
      post(findTrendByWaringRuleIDAndTime, {
        warningRuleID: id,
        startTime: this.searchForm.dateTime[0],
        endTime: this.searchForm.dateTime[1],
        pageIndex: this.popHistory.pageIndex,
        pageSize: this.popHistory.pageSize
      }).then(res => {
        let xData = []
        let yData = []
        for (let i = 0; i < res.data.team.length; i++) {
          xData.push(res.data.team[i].name)
          yData.push(res.data.team[i].count)
        }
        this.dataOption.xAxis.data = xData
        this.dataOption.series[0].data = yData
      })
    },
    // 处理记录
    showHandleHistory(id) {
      this.popHistory.handleList = []
      this.popHistory.warningRuleID = id
      post(
        findSummaryByWarningRuleID,
        Object.assign(
          {
            warningRuleID: id,
            startTime: this.searchForm.dateTime[0],
            endTime: this.searchForm.dateTime[1]
          },
          {
            pageIndex: this.popHistory.pageIndex,
            pageSize: this.popHistory.pageSize
          }
        )
      ).then(res => {
        this.popHistory.handleList = res ? res.data : []
        this.handleObj.chart = this.popHistory.handleList.map(item => {
          return {
            value: item.count,
            name: item.alertAdvice
          }
        })
      })
    },
    query(reset) {
      this.showHistory(this.popHistory.id, reset)
      this.showHandleHistory(this.popHistory.warningRuleID)
      this.getData(this.popHistory.id)
    },

    // 历史报警记录
    showHistory(id, reset = false) {
      this.popHistory.id = id
      this.popHistory.list = []
      reset && (this.popHistory.pageIndex = 1)
      post(
        findHistoryByWarningRuleID,
        Object.assign(
          {
            warningRuleID: id,
            startTime: this.searchForm.dateTime[0],
            endTime: this.searchForm.dateTime[1]
          },
          {
            pageIndex: this.popHistory.pageIndex,
            pageSize: this.popHistory.pageSize
          }
        )
      ).then(res => {
        this.popHistory.list = res ? res.data.content : []
        this.popHistory.pageSize = res.data.pageable.pageSize
        this.popHistory.totalPages = res.data.totalPages
        this.popHistory.total = res.data.totalElements
      })
    },
    handlePopSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.popHistory.pageSize = val
      this.showHistory(this.popHistory.id)
    },
    handlePopCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.popHistory.pageIndex = val
      this.showHistory(this.popHistory.id)
    },
    handleHistoryExport() {
      this.loading = true
      post(
        exportHistoryByWarningRuleID,
        Object.assign({ warningRuleID: this.id }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警历史记录.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;

  &:hover {
    background: #eee;
    color: #fff;
  }
}
</style>
