<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '任务'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择模块',
              trigger: 'change'
            }
          ]"
          label="模块名称："
          prop="serviceName"
        >
          <el-select
            v-model="formData.serviceName"
            :disabled="editType === 'fill'"
            size="small"
            filterable
            placeholder="选择模块"
            @change="clearModel"
          >
            <el-option
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入任务名称',
              trigger: 'change'
            }
          ]"
          filterable
          label="任务名称："
          prop="taskName"
        >
          <el-input
            v-model="formData.taskName"
            :disabled="editType === 'fill'"
            :rows="2"
            type="textarea"
            placeholder="请输入应用情况"
          />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择任务类型',
              trigger: 'change'
            }
          ]"
          label="任务类型："
          prop="taskType"
        >
          <el-radio-group
            v-model="formData.taskType"
            :disabled="editType === 'fill'"
            size="medium"
          >
            <el-radio
              v-for="(item, index) in taskTypeList"
              :key="index"
              :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="北科负责人："
          prop="serviceUser"
        >
          <user-select
            :org="['X11110000', 'X11100000', 'X11120000', 'X11130000', 'X11140000', 'X11150000', 'X11160000', 'X11170000', 'X11180000', 'X11190000']"
            :disabled="editType === 'fill'"
            v-model="formData.serviceUser"/>
        </el-form-item>
        <el-form-item
          label="板材负责人："
          prop="devUser"
        >
          <user-select
            :disabled="editType === 'fill'"
            v-model="formData.devUser"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择时间',
              trigger: 'change'
            }
          ]"
          label="计划完成时间："
          prop="planCompleteDate"
        >
          <el-date-picker
            v-model="formData.planCompleteDate"
            :placeholder="'选择时间'"
            :append-to-body="true"
            :disabled="editType === 'fill'"
            :format="'yyyy-MM-dd'"
            :value-format="'yyyy-MM-dd'"
            :clearable="false"
            style="width: 140px"
          />
        </el-form-item>
        <el-form-item
          label="计划延期时间："
          prop="planCompleteDate"
        >
          <el-date-picker
            v-model="formData.planDelayCompleteDate"
            :picker-options="pickerOptionsC"
            :placeholder="'选择时间'"
            :append-to-body="true"
            :disabled="editType === 'fill'"
            :format="'yyyy-MM-dd'"
            :value-format="'yyyy-MM-dd'"
            :clearable="false"
            style="width: 140px"
          />
        </el-form-item>
        <el-form-item
          label="实际完成时间："
          prop="actualCompleteDate"
        >
          <el-date-picker
            v-model="formData.actualCompleteDate"
            :picker-options="pickerOptionsA"
            :placeholder="'选择时间'"
            :append-to-body="true"
            :disabled="editType === 'fill'"
            :format="'yyyy-MM-dd'"
            :value-format="'yyyy-MM-dd'"
            :clearable="false"
            style="width: 140px"
            @change="formData.status = 2; formData.currentProgress = 100; "
          />
        </el-form-item>
        <el-form-item
          label="状态："
          prop="status"
        >
          <el-radio-group
            v-model="formData.status"
            :disabled="editType === 'fill'"
            size="medium"
            @change="changeStatus"
          >
            <el-radio
              v-for="(item, index) in showStatusList"
              :key="index"
              :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择当前进度',
              trigger: 'change'
            }
          ]"
          label="当前进度："
          prop="currentProgress"
        >
          <el-slider
            v-model="formData.currentProgress"
            show-input/>
        </el-form-item>
        <div
          v-for="(item, index) in formData.reportList"
          :key="index"
          class="model-item"
        >
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请选择填报时间',
                trigger: 'change'
              }
            ]"
            :prop="'reportList.' + index + '.weekDate'"
            label="填报时间："
          >
            <el-button
              style="float: right"
              size="small"
              @click="deleteModel(index)"
            >删除
            </el-button>
            <el-date-picker
              v-model="item.weekDate"
              :placeholder="'选择时间'"
              :append-to-body="true"
              :format="'yyyy-MM-dd'"
              :value-format="'yyyy-MM-dd'"
              :clearable="false"
              style="width: 140px"
            />
          </el-form-item>
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请输入本周进展',
                trigger: 'change'
              }
            ]"
            :prop="'reportList.' + index + '.weekProgress'"
            label="本周进展："
          >
            <el-input
              v-model="item.weekProgress"
              :rows="4"
              type="textarea"
              placeholder="请输入本周进展"
            />
          </el-form-item>
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请输入下周计划',
                trigger: 'change'
              }
            ]"
            :prop="'reportList.' + index + '.nextWeekPlan'"
            label="下周计划："
          >
            <el-input
              v-model="item.nextWeekPlan"
              :rows="4"
              type="textarea"
              placeholder="请输入下周计划"
            />
          </el-form-item>
          <el-form-item
            :prop="'reportList.' + index + '.unfinishedReason'"
            label="未完成原因："
          >
            <el-input
              v-model="item.unfinishedReason"
              :rows="4"
              type="textarea"
              placeholder="请输入未完成原因"
            />
          </el-form-item>
        </div>
        <div class="text-center">
          <el-button @click="addModel">添加进度填报</el-button>
        </div>
      </el-form>

      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  taskTrackingDelete,
  taskTrackingFind,
  taskTrackingSave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'
import UserSelect from '@/components/userSelect'

export default {
  components: { UserSelect, ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    taskTypeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        list: taskTrackingFind, //分页接口地址
        edit: taskTrackingSave,
        add: taskTrackingSave,
        delete: taskTrackingDelete //删除接口地址
      },
      formData: {
        modelNo: null,
        reportList: []
      },
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {
    showStatusList: function() {
      return this.statusList.filter(item => !item.hide)
    },

    pickerOptionsC: function() {
      return {
        disabledDate: time => {
          console.log(time, time.getTime())
          if (!this.formData.planCompleteDate) return true
          return time.getTime() <= this.$moment(this.formData.planCompleteDate)
        }
      }
    },
    pickerOptionsA: function() {
      return {
        disabledDate: time => {
          console.log(time, time.getTime())
          return time.getTime() >= this.$moment()
        }
      }
    }
  },
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    addModel() {
      this.formData.reportList.push({
        weekSubmitUser: JSON.parse(localStorage.getItem('userDetail')).userName
      })
    },
    deleteModel(index) {
      this.formData.reportList.splice(index, 1)
    },
    clearModel() {
      this.formData.reportList = []
    },
    weekChange() {
      this.$nextTick(() => {
        this.formData.weekDate = this.$moment(this.formData.weekDate)
          .startOf('isoWeek')
          .add(4, 'days')
          .format('yyyy-MM-DD')
      })
    },
    submitBefore() {
      // 提交前操作
      this.formData.type = 2
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {
        status: 1,
        reportList: []
      }
    },
    changeStatus(e) {
      console.log(e)
      if (e === 2) {
        this.formData.currentProgress = 100
      }
    },
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, data)
      // 处理填报
      if (!this.formData.weekDate) return (this.formData.reportList = [])
      this.formData.reportList = this.formData.weekDate
        .split('|||')
        .map((item, index) => {
          return {
            unfinishedReason: this.formData.unfinishedReason
              ? this.formData.unfinishedReason.split('|||')[index]
              : '',
            weekSubmitUser: this.formData.weekSubmitUser
              ? this.formData.weekSubmitUser.split('|||')[index]
              : '',
            nextWeekPlan: this.formData.nextWeekPlan
              ? this.formData.nextWeekPlan.split('|||')[index]
              : '',
            weekProgress: this.formData.weekProgress
              ? this.formData.weekProgress.split('|||')[index]
              : '',
            weekDate: this.formData.weekDate
              ? this.formData.weekDate.split('|||')[index]
              : ''
          }
        })
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          const params = Object.assign(this.formData, {
            unfinishedReason: this.formData.reportList
              .map(item => item.unfinishedReason)
              .join('|||'),
            weekSubmitUser: this.formData.reportList
              .map(item => item.weekSubmitUser)
              .join('|||'),
            nextWeekPlan: this.formData.reportList
              .map(item => item.nextWeekPlan)
              .join('|||'),
            weekProgress: this.formData.reportList
              .map(item => item.weekProgress)
              .join('|||'),
            weekDate: this.formData.reportList
              .map(item => item.weekDate)
              .join('|||')
          })
          if (this.editType === 'edit' || this.editType === 'fill') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            this.submitBefore()
            post(this.url.edit, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            this.submitBefore()
            post(this.url.add, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style
  scoped
  lang="less"
>
.model-item {
  border: 1px solid #dedddd;
  padding: 20px 20px 20px 0;
  margin-bottom: 10px;
}
</style>
