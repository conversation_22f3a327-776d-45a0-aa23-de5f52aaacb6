<template>
  <div v-if="visible">
    <el-dialog
      v-el-drag-dialog
      :title="title + '项目节点'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入节点名称',
              trigger: 'change'
            }
          ]"
          label="菜单名称："
          prop="name"
        >
          <el-input
            v-model="formData.name"
            placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择模块',
              trigger: 'change'
            }
          ]"
          label="模块："
          prop="module"
        >
          <el-select
            v-model="formData.module"
            size="small"
            placeholder="选择模块"
            @change="formData.parentId = null;getMenuList"
          >
            <el-option
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="上级菜单："
          prop="module"
        >
          <el-select
            v-model="formData.parentId"
            size="small"
            clearable
            filterable
            placeholder="选择上级菜单"
          >
            <el-option
              v-for="(item, index) in menuList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入页面开发',
              trigger: 'change'
            }
          ]"
          label="页面开发："
          prop="pageDev"
        >
          <el-slider
            v-model="formData.pageDev"
            show-input/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入后端开发',
              trigger: 'change'
            }
          ]"
          label="后端开发："
          prop="backendDev"
        >
          <el-slider
            v-model="formData.backendDev"
            show-input/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入数据接入',
              trigger: 'change'
            }
          ]"
          label="数据接入："
          prop="dataAccess"
        >
          <el-slider
            v-model="formData.dataAccess"
            show-input/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入功能上线',
              trigger: 'change'
            }
          ]"
          label="功能上线："
          prop="functionOnline"
        >
          <el-slider
            v-model="formData.functionOnline"
            show-input/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入投入运行',
              trigger: 'change'
            }
          ]"
          label="投入运行："
          prop="putIntoOperation"
        >
          <el-slider
            v-model="formData.putIntoOperation"
            show-input/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入交付验收',
              trigger: 'change'
            }
          ]"
          label="交付验收："
          prop="deliveryAcceptance"
        >
          <el-slider
            v-model="formData.deliveryAcceptance"
            show-input/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { progressFind, progressSave, uploadFile } from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    module: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: progressSave,
        add: progressSave,
        file: uploadFile,
        list: progressFind
      },
      formData: {
        module: 1
      },
      dialogImageUrl: null,
      dialogVisible: false,
      moduleList: ENUM.moduleList,
      menuList: [],
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {
    module: function() {
      this.formData.module = this.module
    }
  },
  created() {
    // console.log('')
  },
  methods: {
    submitBefore() {
      if (!this.formData.parentId) this.formData.parentId = '0'
    },
    getMenuList() {
      post(
        this.url.list,
        {
          module: this.formData.module,
          parentId: '0',
          pageIndex: 1,
          pageSize: 2000
        },
        {}
      ).then(res => {
        this.menuList = [{ id: '0', name: '无上级' }].concat(res.data.content)
      })
    },
    clearForm() {
      this.formData = {}
    }
  }
}
</script>
<style scoped>
</style>
