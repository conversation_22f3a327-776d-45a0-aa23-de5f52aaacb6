<template>
  <div
    class="content"
  >

    <div
      class="page-operate"
      style="align-items: flex-start">
      <div class="search-wrapper">
        <el-form
          ref="form"
          :model="searchForm"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label="规则名称"
            prop="ruleName"
          >
            <el-input
              v-model="searchForm.ruleName"
              clearable
              size="small"
              placeholder="请输入规则名称"
              style="width: 130px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="模块"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.moduleCode"
              size="small"
              placeholder="选择模块"
              style="width: 130px"
              @change="handleSearch(true)"
            >
              <el-option
                v-for="(item, index) in moduleList"
                :key="index"
                :label="item.Name"
                :value="item.ID"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="产线"
            prop="productionLineName"
          >
            <el-select
              v-model="searchForm.productionLineName"
              size="small"
              placeholder="选择产线"
              style="width: 130px"
              filterable
              @change="handleSearch(true)"
            >
              <el-option
                v-for="(item, index) in productLineList"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="设备名称"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.deviceName"
              size="small"
              placeholder="选择设备"
              style="width: 130px"
              filterable
              @change="handleSearch(true)"
            >
              <el-option
                v-for="(item, index) in deviceList"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="区域名称"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.areaName"
              size="small"
              placeholder="选择区域"
              style="width: 130px"
              filterable
              @change="handleSearch(true)"
            >
              <el-option
                v-for="(item, index) in areaList"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报警类型"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.warningType"
              size="small"
              clearable
              style="width: 130px"
              placeholder="报警类型"
            >
              <el-option
                v-for="(item, index) in warningTypeList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报警级别"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.alertLevel"
              size="small"
              clearable
              style="width: 130px"
              placeholder="报警级别"
            >
              <el-option
                v-for="(item, index) in alertLevelList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div
        class="text-right"
        style="white-space: nowrap">
        <el-button
          icon="ios-search"
          type="primary"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button
          icon="ios-search"
          type="primary"
          @click="handleExport"
        >导出
        </el-button>
      </div>
    </div>
    <div class="widget-list">
      <el-table
        :data="tableData"
        size="small"
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="模块名称"
          prop="moduleName"
          min-width="80"
        />
        <el-table-column
          label="规则名称"
          prop="ruleName"
          min-width="100"
        />
        <el-table-column
          label="规则描述"
          prop="ruleDesc"
          min-width="100"
        />
        <el-table-column
          label="指标数值"
          prop="ruleValue"
          min-width="100"
        />
        <el-table-column
          label="区域名称"
          prop="areaName"
          min-width="100"
        />
        <el-table-column
          label="产线名称"
          prop="productionLineName"
          min-width="100"
        />
        <el-table-column
          label="设备名称"
          prop="deviceName"
          min-width="100"
        />
        <el-table-column
          label="点位名称"
          prop="pointName"
          min-width="100"
        />
        <el-table-column
          label="报警等级"
          prop="alertLevel"
          width="140"
        />
        <el-table-column
          label="推送角色"
          prop="pushRoleName"
          width="140"
        />
        <el-table-column
          label="责任人"
          prop="liablePersonName"
          width="140"
        />
        <el-table-column
          label="创建时间"
          prop="createDateTime"
          width="140"
        />
      </el-table>
    </div>
    <el-row
      align="middle"
      class="table-pagination"
      justify="center"
      type="flex"
    >
      <el-pagination
        :current-page="page.pageIndex"
        :page-size="page.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  findAlertInfo,
  findAreaInfoList,
  findDeviceInfoList,
  findModuleInfoList,
  findProductionLineInfoList
} from '@/api/system'
import { exportWarningRule, findWarningRule } from '~/api/system'

export default {
  name: 'WidgetWarningRule',
  layout: 'staticPage',
  mixins: [listMixins],
  data() {
    return {
      searchForm: {
        userNo: localStorage.getItem('userId')
      },
      url: {
        list: findWarningRule
      },
      tableData: [],
      moduleList: [],
      areaList: [],
      productLineList: [],
      deviceList: [],
      warningTypeList: [
        // 0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        {
          value: 5,
          name: '生产过程监测'
        },
        {
          value: 6,
          name: '趋势校验报警'
        }
      ],
      isConfirm: [
        {
          value: 0,
          name: '未确认'
        },
        {
          value: 1,
          name: '确认'
        }
      ],
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        this.moduleList = res.data || []
      })
      post(findProductionLineInfoList, {}).then(res => {
        this.productLineList = res.data.map(item => {
          return item[0]
        })
      })
      post(findDeviceInfoList, {}).then(res => {
        this.deviceList = res.data.map(item => {
          return item[0]
        })
      })
      post(findAreaInfoList, {}).then(res => {
        this.areaList = res.data.map(item => {
          return item[0]
        })
      })
    },
    handleExport() {
      this.loading = true
      post(
        exportWarningRule,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '预警规则.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    handleWarning(data) {
      // window.parent.postMessage(
      //   {
      //     type: 'link',
      //     url: data.warningEventBody.alertUrl,
      //     title: 'data.warningEventBody.title'
      //   },
      //   '*'
      // )
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-operate {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
    .operate-icon {
      margin-left: 8px;
    }
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
