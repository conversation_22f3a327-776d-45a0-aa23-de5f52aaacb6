<template>
  <div class="page-content">
    <el-row
      :gutter="20"
      class="row-bg full-height"
      justify="start"
      type="flex">
      <el-col
        :span="7"
        class="full-height"
      >
        <div class="tree-left shadow-light full-height">
          <div class="tree-tit">
            当前选择：{{ orgName || '' }}
          </div>
          <div class="tree-box">
            <el-tree
              :data="data"
              :load="loadNode"
              :props="defaultProps"
              highlight-current
              lazy
              node-key="id"
              @node-click="handleNodeClick"
            />
          </div>
        </div>
      </el-col>
      <el-col
        :span="17"
        class="full-height overflow-auto"
      >
        <div class="page-operate">
          <div class="search-wrapper">
            <el-form
              ref="searchForm"
              :label-width="'80px'"
              :model="searchForm"
              size="small"
              inline
              @keyup.enter.native="handleSearch(true)"
            >
              <el-form-item
                label=""
                prop="userNo"
              >
                <user-select
                  v-model="searchForm.handleUser"
                  size="mini"/>
              </el-form-item>
              <el-form-item
                label="开始时间"
                prop="startTime"
              >
                <el-date-picker
                  v-model="searchForm.dateRange"
                  :value-format="'yyyy-MM-dd'"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @input="$forceUpdate()"/>
              </el-form-item>
              <el-form-item
                v-if="orgName"
                label="当前机构"
                prop="userName"
              >
                {{ orgName || '' }}
                <el-button
                  size="small"
                  @click="orgName = null; searchForm.orgCode = null; handleSearch(true)">
                  取消选择
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div>
            <el-button
              icon="ios-search"
              size="small"
              type="primary"
              @click="handleSearch"
            >搜索
            </el-button>
            <el-button
              size="small"
              @click="handleReset"
            >重置
            </el-button>
          </div>
        </div>
        <div class="page-card shadow-light">
          <el-table
            v-loading="loading"
            :data="tableData"
            :size="size"
            class="custom-table"
            border
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              type="index"
              width="50"/>
            <el-table-column
              label="工号"
              prop="userNo"
              width="80"
            />
            <el-table-column
              label="姓名"
              prop="userName"
              width="80"
            />
            <el-table-column
              label="组织机构"
              prop="orgName"
            />
            <el-table-column
              label="页面数量"
              prop="num"
            />
            <el-table-column
              label="访问次数"
              prop="accessNum"
            />
            <el-table-column
              label="操作"
              prop="percent"
              width="80"
            >
              <template v-slot="{ row }">
                <el-button
                  slot="reference"
                  type="text"
                  @click="handleView(row)"
                >责任页面
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <list
      ref="modalView"
      :service-list="serviceList"
      :login-time-start="searchForm.loginTimeStart"
      :login-time-end="searchForm.loginTimeEnd"/>
  </div>

</template>

<script>
import listMixins from '@/mixins/ListMixins'
import {
  findBasicDataConfigByType,
  findEachPageAccessList,
  findUserPageAccessList,
  getMainHandleUser,
  orgListByCode
} from '@/api/system'
import SelectOrg from '@/components/SelectOrg'
import { post } from '@/lib/Util'
import List from '@/pages/visit/ownerLog/component/list'
import UserSelect from '@/components/userSelect'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'visit-ownerLog',
  components: { UserSelect, List, SelectOrg },
  layout: 'menuLayout',
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {},
      serviceList: [],
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: getMainHandleUser //分页接口地址
      },
      editUserId: null,
      orgName: null,
      maxNum: 20,
      data: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'orgAllName',
        isLeaf: 'leaf'
      }
    }
  },
  created() {
    this.findBasicDataConfigByType()
  },
  methods: {
    //
    afterHandleSearch() {
      this.tableData = this.tableData.map(item => {
        item.percent = Number(((item.num / this.maxNum) * 100).toFixed(1))
        item.percent = item.percent > 100 ? 100 : item.percent
        return item
      })
    },
    beforeHandleSearch() {
      if (!this.searchForm.dateRange) {
        this.searchForm.dateRange = [
          this.$moment().format('yyyy-MM-DD'),
          this.$moment().format('yyyy-MM-DD')
        ]
      }
      this.searchForm.loginTimeStart = this.searchForm.dateRange[0]
      this.searchForm.loginTimeEnd = this.searchForm.dateRange[1]
      this.searchForm.userNos =
        this.searchForm.handleUser && this.searchForm.handleUser.length
          ? this.searchForm.handleUser.split(',').map(item => {
              return item.split('|')[0]
            })
          : null
    },
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data : []
      this.loading = false
      this.afterHandleSearch()
    },
    getStatus(percent) {
      if (percent >= 80) {
        return 'success'
      } else if (percent >= 50) {
        return 'warning'
      } else {
        return 'exception'
      }
    },
    handleView: function(row) {
      this.$refs.modalView.userNo = row.userNo
      this.$refs.modalView.visible = true
      this.$refs.modalView.handleSearch(true)
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.name === name) || {}
    },
    async loadData(orgCode) {
      const data = await post(orgListByCode, { orgCode: orgCode })
      data.data.forEach(item => {
        item.leaf = !item.hasChildren
      })
      return Promise.resolve(data.success ? data.data : [])
    },
    async loadNode(node, resolve) {
      const parentCode = node.data.orgCode || 'X'
      let data = await this.loadData(parentCode)
      if (parentCode === 'X') {
        data = data.filter(item => ENUM.orgTop.indexOf(item.orgCode) !== -1)
      }
      resolve(data)
    },
    async handleNodeClick(data) {
      this.searchForm.orgCode = data.orgCode
      this.orgName = data.orgAllName
      this.handleSearch(true)
    },
    async findBasicDataConfigByType() {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
      return Promise.resolve(true)
    }
  }
}
</script>

<style
  lang="less"
  scoped>
.tree-left {
  overflow: auto;
  padding: 24px;
  background: #fff;
  border: 1px solid #eee;

  .tree-box {
    height: calc(100% - 55px);
    overflow: auto;
  }

  .tree-tit {
    margin-bottom: 20px;
    font-size: 18px;
    line-height: 1.5;
  }
}
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.progress-box {
  display: flex;
  font-size: 18px;
  font-weight: bold;
  .progress-num {
    flex: 1;
    margin-left: 5px;
    text-align: right;
  }
  &.exception {
    color: #ff2855;
  }
  &.warning {
    color: #ffb243;
  }
  &.success {
    color: #19be6b;
  }
}
</style>
