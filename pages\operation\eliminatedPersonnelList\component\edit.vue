<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '申请'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1000px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="0"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择用户',
              trigger: 'change'
            }
          ]"
          prop="userNo"
        >
          <p style="font-size: 20px; margin-bottom: 10px">工号姓名：</p>
          <user-select v-model="formData.userNo"/>
        </el-form-item>
        <el-form-item
          label=""
          prop="remark"
        >
          <p style="font-size: 20px; margin-bottom: 10px">备注：</p>
          <el-input
            v-model="formData.extraRemark"
            type="textarea"
            placeholder="请输入备注" />

        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { eliminatedPersonnelListSave } from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import UserSelect from '@/components/userSelect'

export default {
  components: { UserSelect, ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: eliminatedPersonnelListSave,
        add: eliminatedPersonnelListSave
      },
      formData: {},
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultSelected: [],
      defaultExpanded: [],
      data1: [],
      defaultProps1: {
        children: 'children',
        label: 'name'
      },
      defaultSelected1: [],
      defaultExpanded1: []
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = data
      this.formData.userNo = data.userNo + '|' + data.userName
      console.log('editFormData', data.id)
    },
    clearForm() {
      this.formData = {}
      this.defaultSelected1 = []
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          if (this.formData.id) {
            post(this.url.add, {
              id: this.formData.id,
              userNo: this.formData.userNo.split('|')[0],
              userName: this.formData.userNo.split('|')[1],
              status: 1,
              orgCode: this.formData.userNo.split('|')[3]
                ? this.formData.userNo.split('|')[3]
                : this.formData.orgCode,
              extraRemark: this.formData.extraRemark
            }).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('修改成功！')
                this.close()
              }
            })
          } else {
            console.log('formData', this.formData)
            post(this.url.add, {
              userNo: this.formData.userNo.split('|')[0],
              userName: this.formData.userNo.split('|')[1],
              orgCode: this.formData.userNo.split('|')[3],
              extraRemark: this.formData.extraRemark
            }).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('新增成功！')
                this.close()
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
