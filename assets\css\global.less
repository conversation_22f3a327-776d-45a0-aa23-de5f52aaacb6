
/*菜单彩色图标*/
.icon-svg {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

/*常用样式*/
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}
.one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


/*全局滚动条样式 chrome内核*/
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/

*::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
}

/*定义滚动条的轨道颜色、内阴影及圆角*/
*::-webkit-scrollbar-track{
    -webkit-box-shadow: inset 0 0 4px rgba(0,0,0,.15);
    border-radius: 3px;
}


/*定义滑块颜色、内阴影及圆角*/
*::-webkit-scrollbar-thumb{
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.15);
    background-color: #C1CBDB;
    &:hover {
        background: #a9b4c5;
    }
}

/*定义两端按钮的样式*/
*::-webkit-scrollbar-button {
    display: none;
}

/*定义右下角汇合处的样式*/
::-webkit-scrollbar-corner {
    display: none;
}
.el-menu-item.is-active {
    background: #0F2A59;
}

/*light*/
.home.light .win .app-item .app-tit {
}
.home.light .dock .dock-box {
}
.home.light .dock .dock-box ul li {
}
.home.light .desk-copyright {
    color: #333;
}


.home.dark .dock .dock-box .miniList{
   border-color: rgba(255, 255, 255, 0.5)
}
/*dark*/
.home.dark .win .app-item .app-tit {
    color: #fff;
    text-shadow: 0 1px 20px black;
}
.home.dark .dock .dock-box {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.5);
}
.home.dark .dock .dock-box ul li {
    color: #cecece;
}

.home.dark .dock .dock-box ul:nth-child(1){
   border-color: rgba(255, 255, 255, 0.5)
}


.dock-pop {
    padding: 2px;
}

.search-wrapper {
    /deep/ .el-form-item--mini.el-form-item {
        margin-bottom: 12px !important;
    }
    /deep/ .el-form-item--small.el-form-item {
        margin-bottom: 12px !important;
    }
    /deep/ .el-date-editor .el-range-separator {
        box-sizing: content-box !important;
    }
}
.page-operate .el-button--small{
    margin-bottom: 12px !important;
}

// 主要页面通用样式
.page-content {
    font-size: 18px;
    height: 100%;
    .page-card {
        background: #fff;
        padding: 24px;
        position: relative;
    }
    .page-operate {
        display: flex;
        justify-content: space-between;
        .operate-icon {
            margin-left: 8px;
        }
    }
    .page-title {
        font-size: 20px;
        font-weight: bold;
        line-height: 28px;
        color: #3A3F63;
        margin-bottom: 10px;
    }
    .table-title {
        font-size: 16px;
        font-weight: bold;
        color: #606266;
        line-height: 33px;
        margin-bottom: 12px;
    }
}


.shadow-base {
    box-shadow:
            0px 2px 4px -2px rgba(0, 0, 0, 0.12),
            0px 4px 8px rgba(0, 0, 0, 0.08),
            0px 4px 16px 4px rgba(0, 0, 0, 0.04);
    border-radius: 6px
}

.shadow-light {
    box-shadow:
            0px 0px 4px rgba(0, 0, 0, 0.08),
            0px 2px 6px rgba(0, 0, 0, 0.06),
            0px 4px 8px 2px rgba(0, 0, 0, 0.04);
    border-radius: 6px
}
.full-height {
    height: 100%;
}
.overflow-auto {
    overflow: auto;
}

.custom-table {
    &.el-table--small td, &.el-table--small th {
        padding: 3px 0;
        height: 48px;
    }
}

.el-button--text {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.el-tooltip__popper {
    font-size: 16px;
    max-width: 500px;
}

/*水印样式，控制行与行之间的交错显示  为0则不交错*/
#tp-watermark {
    div:nth-child(2n){
        padding-left: 200px;
    }
}


.sys-notice {
    .el-notification__title {
        font-size: 12px;
        font-weight: 400;
        margin-bottom: 10px;
    }
    .el-notification__content h3 {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        line-height: 1.5;
    }
    .el-notification__content p {
        line-height: 1.5;
        font-size: 14px;
    }
}
