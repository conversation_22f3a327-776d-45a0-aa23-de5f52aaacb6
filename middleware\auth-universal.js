import axios from 'axios'
import { resourceListNoPage } from '~/api/system'
import { getMenuData } from '~/lib/Menu'

export default async function(context) {
  console.log(context)
  const { query } = context.route
  // 判断资源来源是否为后端重定向
  // 获取url参数，如果有token信息，直接塞入localstorage
  if (query.org && query.org === 'redirect') {
    !!query.token && context.store.commit('menu/seToken', query.token)
    !!query.userId && context.store.commit('menu/setUserId', query.userId)
  }
  console.log(query)
  if (
    // windows 对象上的系统初始化标志位
    !context.app.SYSTEM_MENU_LOADED
  ) {
    console.log('重新请求数据')
    const { data } = await axios.post(
      'http://172.25.63.67:9800/' + resourceListNoPage,
      {
        userNo: context.store.state.menu.userId,
        serviceName: 'res'
      }
    )
    console.log(context.store.state.menu.userId)
    if (data.success) {
      const menu = data.data.filter(item => item.type === 'menu')
      const button = data.data
        .filter(item => item.type === 'button')
        .map(item => item.url)
      // 缓存菜单、按钮权限数据
      context.store.commit('menu/setAllMenus', menu)
      context.store.commit(
        'menu/setUserMenuList',
        getMenuData(menu, '')[0]['children']
      )
      // console.log(getMenuData(menu, '')[0]['children'])
      context.store.commit('menu/setPageButtonPower', button)
      context.app.SYSTEM_MENU_LOADED = 1 // 系统菜单已加载
    }
    if (
      context.store.state.menu.allMenus &&
      !context.store.state.menu.allMenus.find(
        item => item.url === context.route.path
      ) &&
      context.route.path !== '/'
    ) {
      context.redirect('/')
    }
  }
}
