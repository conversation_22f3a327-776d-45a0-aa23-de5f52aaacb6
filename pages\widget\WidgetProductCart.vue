<template>
  <div
    class="content"
  >
    <el-row :gutter="20">
      <el-col :span="10">
        <div class="info-list">
          <div>当前班组 <span>白班/甲班</span></div>
          <div>轧制数量 <span>45块</span></div>
          <div>轧制重量 <span>604t</span></div>
          <div>当前状态 <span>正常</span></div>
          <div>生产时长 <span>08:40:15</span></div>
          <div>故障时间 <span>00:00:00</span></div>
        </div>
      </el-col>
      <el-col :span="14">
        <div
          id="pro-chart"
          style="height: 220px;width: 100%;"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'WidgetProductChart',
  layout: 'staticPage',
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    setTimeout(() => {
      this.init()
    }, 0)
  },
  methods: {
    init() {
      //
      this.chart = this.$echarts.init(
        document.getElementById('pro-chart'),
        'light'
      )
      this.chart.setOption({
        color: ['#67F9D8', '#FFE434', '#56A3F1', '#FF917C'],
        splitArea: {
          areaStyle: {
            color: ['#77EADF', '#26C3BE', '#64AFE9', '#428BD4'],
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: 10
          }
        },
        center: ['50%', '50%'],
        radius: 360,
        radar: {
          // shape: 'circle',
          indicator: [
            {
              name: '厚度',
              max: 6500
            },
            {
              name: '凸度',
              max: 16000
            },
            {
              name: '平度',
              max: 30000
            },
            {
              name: '卷温',
              max: 38000
            },
            {
              name: '终温',
              max: 52000
            },
            {
              name: '宽度',
              max: 25000
            }
          ]
        },
        series: [
          {
            name: 'Budget vs spending',
            type: 'radar',
            data: [
              {
                value: [4200, 3000, 20000, 35000, 50000, 18000],
                name: 'Allocated Budget'
              }
            ]
          }
        ]
      })
    }
  }
}
</script>

<style scoped lang="less">
.content {
  height: 100%;
  background: #fff;
  padding: 12px;
}
.info-list {
  font-size: 14px;
  line-height: 2;
  color: #999;
  span {
    color: #333;
    font-weight: bolder;
    margin-left: 5px;
  }
}
</style>
