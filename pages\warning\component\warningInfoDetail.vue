<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'历史记录'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="94%"
      @close="clearForm"
      @open="tabActive = 'first';onOpen"
      v-on="$listeners"
    >
      <div 
        class="page-content"
        style="position: relative">
        <el-table
          :data="popHistory.list"
          :max-height="'400px'"
          size="small"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column
            label="所属模块"
            prop="moduleName"
            width="100"
          >
            <!--          <template-->
            <!--            v-slot="{row}"-->
            <!--          >-->
            <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
            <!--          </template>-->
          </el-table-column>
          <el-table-column
            label="报警描述"
            prop="alertContent"
            min-width="100"
          />
          <el-table-column
            label="设备名"
            show-overflow-tooltip
            prop="deviceName"
            width="180"
          />
          <el-table-column
            label="区域名"
            prop="areaName"
            width="100"
          />
          <el-table-column
            label="报警类型"
            prop="alertTypeName"
            width="100"
          />
          <el-table-column
            label="报警等级"
            prop="alertLevel"
            width="90"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.alertLevel == 1"
                type="danger">一级</el-tag>
              <el-tag
                v-if="row.alertLevel == 2"
                type="warning">二级</el-tag>
              <el-tag
                v-if="row.alertLevel == 3">三级</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            prop="status"
            width="80"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.status == 0"
                type="danger">
                未处理
              </el-tag>
              <el-tag
                v-if="row.status == 2"
                type="success">
                已处理
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            label="未处理时长"
            prop="diff"
            min-width="135"
            show-overflow-tooltip
          >
            <template
              v-slot="{row}"
            >
              <timer
                v-if="row.status == 0"
                :time="row.diff" />
            </template>
          </el-table-column>
          <el-table-column
            label="处理人"
            prop="dealUser"
            width="140"
          />
          <el-table-column
            label="报警分析"
            prop="alertAnalysis"
            width="180"
          />
          <el-table-column
            label="处理意见"
            prop="alertAdvice"
            width="180"
          />
          <el-table-column
            label="报警时间"
            prop="createDateTime"
            width="150"
          />
        </el-table>
        <div style="text-align: right; margin-top: 10px">
          <el-pagination
            :current-page="popHistory.pageIndex"
            :page-size="popHistory.pageSize"
            :page-sizes="[10, 20, 30, 40, 100]"
            :total="popHistory.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePopSizeChange"
            @current-change="handlePopCurrentChange"
          />
        </div>
      </div>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import listMixins from '@/mixins/ListMixins'
import {
  exportHistoryByWarningRuleID,
  findHistoryByRoleAndTime,
  findHistoryByWarningRuleID,
  findSummaryByWarningRuleID
} from '@/api/system'
import { post } from '@/lib/Util'
import PieChart from '@/components/chart/pie-chart'
import moment from 'moment'
import Timer from '@/pages/system/coordination/component/timer'

export default {
  components: { Timer, PieChart },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      visible: false,
      searchForm: {},
      handleObj: {
        chart: []
      },
      tabActive: 'first',
      popHistory: {
        id: null,
        warningRuleID: null,
        list: [],
        handleList: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date()).format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '昨日',
            onClick(picker) {
              const end = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 23:59:59')
              )
              const start = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('isoWeek')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {},
  async created() {},
  destroyed() {},
  methods: {
    query(reset) {
      this.showHistory(this.popHistory.id, reset)
    },
    // 历史报警记录
    showHistory(id, reset = false) {
      this.popHistory.id = id
      this.popHistory.list = []
      reset && (this.popHistory.pageIndex = 1)
      post(
        findHistoryByRoleAndTime,
        Object.assign(
          {
            roleID: id,
            status: 0,
            startTime: this.searchForm.dateTime[0],
            endTime: this.searchForm.dateTime[1]
          },
          {
            pageIndex: this.popHistory.pageIndex,
            pageSize: this.popHistory.pageSize
          }
        )
      ).then(res => {
        this.popHistory.list = res ? res.data.content : []
        this.updateDiff()
        this.popHistory.pageSize = res.data.pageable.pageSize
        this.popHistory.totalPages = res.data.totalPages
        this.popHistory.total = res.data.totalElements
      })
    },
    updateDiff() {
      this.popHistory.list.forEach(item => {
        item.diff = this.$moment().diff(
          this.$moment(item.createDateTime),
          'seconds'
        )
      })
    },
    handlePopSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.popHistory.pageSize = val
      this.showHistory(this.popHistory.id)
    },
    handlePopCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.popHistory.pageIndex = val
      this.showHistory(this.popHistory.id)
    },
    handleHistoryExport() {
      this.loading = true
      post(
        exportHistoryByWarningRuleID,
        Object.assign({ warningRuleID: this.id }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警历史记录.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  &:hover {
    background: #eee;
    color: #fff;
  }
}
</style>
