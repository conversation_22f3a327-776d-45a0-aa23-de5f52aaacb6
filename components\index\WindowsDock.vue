<template>
  <div
    :class="{'active': activeDesktop.dpCheck === -1}"
    class="dock"
    @mousedown.stop>
    <div class="dock-box">
      <ul>
        <li
          v-for="(item, key) in dockList"
          :key="key"
          class="app-icon"
          @click="openPage(item)">
          <el-tooltip
            :content="item.name"
            class="item"
            effect="dark"
            placement="top-start">
            <icon-png
              :icon-name="item.deskIcon"
            />
          </el-tooltip>
        </li>
        <li
          @click="openAppDialog">
          <el-tooltip
            :content="'所有应用'"
            class="item"
            effect="dark"
            placement="top-start">
            <i class="el-icon-menu"/>
          </el-tooltip>
        </li>
      </ul>
      <!--        最小化-->
      <ul
        v-if="miniAppList.length"
        class="miniList">
        <li
          v-for="(item, index) in miniAppList.filter((item, miniIndex) => miniIndex < showNum)"
          :key="item.id"
          class="app-icon"
          @click="openPage(item.origin)"
        >
          <el-popover
            :ref="'dock-pop' + index"
            placement="top"
            width="100"
            trigger="hover"
            popper-class="dock-pop">
            <div class="dock-pop-tit">{{ item.name }}</div>
            <ul class="dock-contextmenu">
              <li @click="closeAllWin(index)">
                <i class="el-icon-close"/>关闭所有窗口
              </li>
              <li @click="closeOtherWin(index)">
                <i class="el-icon-remove-outline"/>关闭其他窗口
              </li>
              <li @click="closeWin(index)">
                <i class="el-icon-delete"/>关闭当前窗口
              </li>
            </ul>
            <icon-png
              slot="reference"
              :icon-name="item.deskIcon"
            />
          </el-popover>
        </li>
        <li
          v-if="miniAppList.filter((item, miniIndex) => miniIndex >= showNum).length"
          class="hidden-btn"
          @click="openAppDialog">
          <el-popover
            placement="top"
            width="200"
            trigger="hover">
            <ul class="mini-app-pop">
              <li
                v-for="(item, index) in miniAppList.filter((item, miniIndex) => miniIndex >= showNum)"
                :key="index"
                :title="item.name"
                @click="openPage(item.origin)">
                <icon-png
                  :icon-name="item.deskIcon"
                  class="icon"
                />
                {{ item.name }}
              </li>
            </ul>
            <i
              slot="reference"
              class="el-icon-more"/>
          </el-popover>
        </li>
      </ul>
    </div>
    <el-dialog
      :top="'2%'"
      :visible.sync="appVisible"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      :width="'1200px'"
      :title="'应用中心'"
      append-to-body
      class="hmi-dialog"
      @mousedown.native.stop>
      <el-tabs
        v-model="mode"
        type="card">
        <el-tab-pane
          label="应用"
          name="menu">
          <div class="crumb-wrapper">
            <el-icon
              title="根目录1"
              class="icon-crumb el-icon-s-home"
              @click.native="openHome"/>
            <el-icon
              title="上一级"
              class="icon-crumb el-icon-back"
              @click.native="back"/>
            <el-breadcrumb
              class="crumb"
              separator-class="el-icon-arrow-right">
              <el-breadcrumb-item
                @click.native="back">
                <a>首页</a>
              </el-breadcrumb-item>
              <el-breadcrumb-item
                v-for="(item, index) in menuPath"
                :key="index"
                @click.native="backPath(index)"
              >
                <a>{{ breadName(index) }}</a>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="search-wrapper">
            <el-input
              v-model="searchKey"
              placeholder="输入菜单名搜索"
              class="width-full"
              @input="search()"
            />
          </div>
          <div>

            <el-row :gutter="20">
              <el-col :span="6">
                <div
                  class="tree-wrapper">
                  <el-tree
                    ref="tree"
                    :data="menuStore"
                    :expand-on-click-node="false"
                    :props="defaultProps"
                    draggable
                    highlight-current
                    node-key="id"
                    style="background-color: #f1f1f1;"
                  >
                    <span
                      slot-scope="{ node, data }"
                      class="custom-tree-node"
                      @click.prevent.stop="handleNodeClick(node)"
                      @dblclick.prevent.stop="openPage(data)">
                      <icon-png
                        v-if="data.type !== 'button'"
                        :icon-name="data.deskIcon" />
                      <i
                        v-else
                        class="el-icon-thumb"/>
                      {{ data.name }}
                    </span>
                  </el-tree>
                </div>
              </el-col>
              <el-col :span="18">
                <div class="win">
                  <template v-for="(item, index) in showMenu">
                    <template v-if="item.isShow">
                      <div
                        v-if="item.children && item.children.length"
                        :key="item.id || index"
                        :ref="'item' + item.id"
                        :title="item.name"
                        :class="{'righting': index === rightMenuIndex}"
                        class="app-item"
                        @contextmenu.prevent="openMenu($event, item.id, index)"
                        @dblclick="openDir(index)"
                      >
                        <div
                          class="app-inner app-dir">
                          <div class="app-dir-inner">
                            <template
                              v-for="(child, cIndex) in item.children">
                              <span
                                v-if="child.isShow"
                                :key="child.id"
                                class="inner-app-item">
                                <icon-png
                                  :key="cIndex"
                                  :icon-name="child.deskIcon"
                                  class="c-svg"
                                />
                              </span>
                            </template>
                           
                          </div>
                        </div>
                        <div class="app-tit">{{ item.name }}</div>
                      </div>
                      <div
                        v-else
                        :key="item.id || index"
                        :ref="'item' + item.id"
                        :title="item.name"
                        :class="{'righting': index === rightMenuIndex}"
                        class="app-item"
                        @contextmenu.prevent="openMenu($event, item.id, index)"
                        @dblclick.prevent.stop="openPage(item)"
                      >
                        <div
                          class="app-inner">
                          <icon-png
                            :icon-name="item.deskIcon"
                            style="user-select: none"
                            class="c-svg"
                          />
                        </div>
                        <div class="app-tit">{{ item.name }}</div>
                      </div>
                    </template>
                  </template>
                </div>
              </el-col>
            </el-row>

          </div>
          <br>
          *提示：应用可右键添加至桌面快捷方式
        </el-tab-pane>
        <!--小部件-->
        <el-tab-pane
          label="桌面小部件"
          name="plugin">

          <div class="crumb-wrapper">
            <el-icon
              title="根目录1"
              class="icon-crumb el-icon-s-home"
              @click.native="openHome"/>
            <el-icon
              title="上一级"
              class="icon-crumb el-icon-back"
              @click.native="back"/>
            <el-breadcrumb
              class="crumb"
              separator-class="el-icon-arrow-right">
              <el-breadcrumb-item
                @click.native="openHome">
                <a>首页</a>
              </el-breadcrumb-item>
              <el-breadcrumb-item
                v-for="(item, index) in menuPath"
                :key="index"
                @click.native="backPath(index)"
              >
                <a>{{ breadNameWidget(index) }}</a>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="search-wrapper">
            <el-input
              v-model="searchKey"
              placeholder="输入菜单名搜索"
              class="width-full"
              @input="search()"
            />
          </div>
          <div class="win">

            <template
              v-for="(item, index) in showWidget"
            >
              <template v-if="item.isShow">
                <div
                  v-if="item.children && item.children.length"
                  :key="item.id || index"
                  :ref="'item' + item.id"
                  :class="{'righting': index === rightMenuIndex}"
                  class="app-item"
                  @contextmenu.prevent="openMenu($event, item.id, index)"
                  @dblclick="openDir(index)"
                >
                  <div
                    class="app-inner app-dir">
                    <div class="app-dir-inner">
                      <template
                        v-for="(child, cIndex) in item.children">
                        <span
                          v-if="child.isShow"
                          :key="child.id"
                          class="inner-app-item">
                          <icon-png
                            :key="cIndex"
                            :icon-name="child.deskIcon"
                            class="c-svg"
                          />
                        </span>
                      </template>
                    </div>
                  </div>
                  <div class="app-tit">{{ item.name }}</div>
                </div>
                <div
                  v-else
                  :key="item.id || index"
                  :ref="'item' + item.id"
                  :class="{'righting': index === rightMenuIndex}"
                  class="app-item"
                  draggable="true"
                  @drag.prevent="drag($event, index)"
                  @dragend.prevent="dragend($event, index)"
                  @contextmenu.prevent="openMenu($event, item.id, index)"
                  @dblclick.prevent.stop="openPage(item)"
                >
                  <div
                    class="app-inner">
                    <icon-png
                      :icon-name="item.deskIcon"
                      style="user-select: none"
                      class="c-svg"
                    />
                  </div>
                  <div class="app-tit">{{ item.name }}</div>
                </div>
              </template>
            </template>
          </div>
        </el-tab-pane>
      </el-tabs>
      <ul
        v-show="rightMenuVisible"
        ref="rightMenu"
        :style="{ left: rightMenuLeft + 'px', top: rightMenuTop + 'px' }"
        class="contextmenu"
      >
        <template v-if="mode === 'menu'">
          <li @click="openPage(showMenu[rightMenuIndex])">打开</li>
          <li @click="openPage(showMenu[rightMenuIndex], '_blank')">新窗口打开</li>
          <li @click="addShortcuts">添加快捷方式</li>
        </template>
        <template v-else>
          <li
            v-if="!hasChildren"
            @click="addWidget">添加小部件至桌面</li>
        </template>
      </ul>
    </el-dialog>
  </div>
</template>

<script>
import { initWidget, post } from '@/lib/Util'
import { findRootResource } from '@/api/desktop'
import lodash from 'lodash'
import IconPng from '@/components/IconPng'
import { mapState } from 'vuex'
import { generateTree } from '@/lib/Menu'
export default {
  name: 'WindowsDock',
  components: { IconPng },
  data() {
    return {
      showNum: 8,
      mode: 'menu',
      appVisible: false,
      searchKey: '',
      totalData: [],
      searchData: [],
      menuPath: [],
      dockList: [],
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuIndex: null,
      rightMenuDock: false,
      searchTimer: null,
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  computed: {
    ...mapState('menu', ['widgetList', 'menuStore', 'allMenus']),
    ...mapState('desktop', [
      'miniAppList',
      'desktopData',
      'desktopList',
      'activeIndex'
    ]),
    showWidget: function() {
      if (this.mode !== 'plugin') return []
      let widget = this.searchKey
        ? this.searchData.filter(item => item.type === 'plugin')
        : this.widgetList
      this.menuPath.forEach(item => {
        if (!widget[item]) return
        widget = widget[item].children
      })
      return widget
    },
    showMenu: function() {
      if (this.mode !== 'menu') return []
      let menu = this.searchKey
        ? this.searchData.filter(item => item.type === 'menu')
        : this.menuStore
      this.menuPath.forEach(item => {
        if (!menu[item]) return
        menu = menu[item].children
      })
      return menu
    },
    breadName() {
      return function(index) {
        if (this.mode !== 'menu') return ''
        let menu = this.searchKey
            ? this.searchData.filter(item => item.type === 'menu')
            : this.menuStore,
          name = null
        for (let i = 0; i <= index; i++) {
          // console.log(this.showMenu, this.menuPath, i)
          name = menu[this.menuPath[i]].name
          menu = menu[this.menuPath[i]].children
        }
        return name
      }
    },
    breadNameWidget() {
      return function(index) {
        if (this.mode !== 'plugin') return ''
        let menu = this.widgetList,
          name = null
        for (let i = 0; i <= index; i++) {
          name = menu[this.menuPath[i]].name
          menu = menu[this.menuPath[i]].children
        }
        return name
      }
    },
    hasChildren() {
      return (
        this.rightMenuIndex !== null &&
        this.showWidget[this.rightMenuIndex].children &&
        this.showWidget[this.rightMenuIndex].children.length
      )
    },
    activeDesktop: function() {
      return this.activeIndex ? this.desktopList[this.activeIndex] : {}
    }
  },
  watch: {
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    mode(value) {
      this.menuPath = []
      this.searchKey = null
    }
  },
  created() {
    this.getDockList()
  },
  methods: {
    openPage(item, target = '_self') {
      // console.log(item)
      this.$bus.$emit('open-iframe', item, target)
    },
    getDockList() {
      post(findRootResource, {
        userNo: localStorage.getItem('userId'), //传入
        type: 'menu',
        serviceName: ''
      }).then(res => {
        for (let i = 0; i < res.data.length; i++) {
          if (
            res.data[i].serviceName === 'res' ||
            res.data[i].serviceName === 'idm'
          ) {
            this.dockList.push(res.data[i])
          }
        }
      })
    },
    openAppDialog() {
      this.appVisible = true
    },
    openDir(index) {
      this.menuPath.push(index)
    },
    // 返回主页
    openHome() {
      this.menuPath = []
    },
    // 返回上一级
    back() {
      this.menuPath.pop()
    },
    // 返回当前打开路径
    backPath(index) {
      this.menuPath.splice(index + 1, this.menuPath.length)
    },
    // 右键菜单
    openMenu(e, id, index) {
      this.rightMenuIndex = index
      if (id) {
        // this.chooseCaseFolder(item, path);
      }
      const x = e.clientX
      const y = e.clientY
      this.rightMenuTop = y
      this.rightMenuLeft = x
      this.rightMenuVisible = true
    },
    closeMenu() {
      this.rightMenuVisible = false
      this.rightMenuIndex = null
    },
    addShortcuts() {
      const data = lodash.cloneDeep(this.showMenu[this.rightMenuIndex])
      delete data.children
      data.shortcutName = data.name + '-快捷方式' // 快捷方式名称
      this.$bus.$emit('add-shortcuts', data)
    },
    addWidget() {
      this.appVisible = false
      this.$bus.$emit(
        'add-widget',
        lodash.cloneDeep(initWidget(this.showWidget[this.rightMenuIndex]))
      )
    },
    drag: function(e, index) {
      this.appVisible = false
      this.$bus.$emit('widget-dragstart', initWidget(this.showWidget[index]))
    },
    dragend: function(e, index) {
      this.$bus.$emit('widget-dragend')
    },
    closeWin(index) {
      this.$refs['dock-pop' + index][0].doClose()
      this.$nextTick(() => {
        this.miniAppList.splice(index, 1)
      })
    },
    closeOtherWin(index) {
      this.$refs['dock-pop' + index][0].doClose()
      this.$store.commit(
        'desktop/miniAppList',
        this.miniAppList.filter((item, i) => i === index)
      )
    },
    closeAllWin(index) {
      this.$refs['dock-pop' + index][0].doClose()
      this.$store.commit('desktop/miniAppList', [])
    },
    // 搜索
    search() {
      if (!this.searchKey) {
        return this.openHome()
      }

      this.searchTimer && clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        // 过滤包含搜索名称的菜单
        const filterData = this.allMenus.filter(item => {
          return (
            item.name &&
            item.type !== 'button' &&
            item.name.indexOf(this.searchKey) !== -1
          )
        })
        if (!filterData.length) return (this.data = [])
        // 递归把所有菜单的父级找出来
        this.handleMenu(filterData, filterData)
        this.searchData = generateTree(filterData)
        this.menuPath = []
      }, 200)
    },
    // 递归添加父级级单
    handleMenu(menuData, addMenu) {
      const addList = []
      // console.log(menuData, addMenu)
      addMenu.forEach(item => {
        if (!item.parentId) return
        if (!menuData.find(i => i.id === item.parentId)) {
          const match = this.allMenus.find(i => i.id === item.parentId)
          match && menuData.push(match)
          match && addList.push(match)
        }
      })
      if (addList.length) {
        this.handleMenu(menuData, addList)
      }
    },
    // 树单机
    handleNodeClick(data) {
      // console.log(data)
    }
  }
}
</script>

<style scoped lang="less">
.dock {
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  height: 70px;
  //max-width: 1200px;
  //left: 0;
  //right: 0;
  margin: 0 auto;
  z-index: 999;
  user-select: none;
  transition: transform 0.8s;
  &.active {
    transform: translate(-50%, 100px);
  }
  .dock-box {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 16px;
    height: 100%;
    display: flex;
    padding: 8px 10px;

    ul {
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 0 15px;

      .app-icon:hover {
        //width: 100px;
        //height: 100px;
        margin-bottom: 15px;
        transform: scale(1.2);
      }

      li {
        position: relative;
        width: 60px;
        height: 60px;
        font-size: 60px;
        color: rgba(0, 0, 0, 0.2);
        text-align: center;
        transition: all 0.2s ease-in-out 0.1s;
        margin: 0 10px;
        cursor: pointer;

        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
    .miniList {
      border-left: 1px solid rgba(0, 0, 0, 0.2);
      li {
        &:after {
          content: '';
          position: absolute;
          width: 3px;
          height: 3px;
          background: #74c7ea;
          box-shadow: 0 0 1px 1px rgba(47, 133, 232, 0.86);
          bottom: -2px;
          left: 50%;
          margin-left: -1px;
          border-radius: 2px;
        }
      }
      .hidden-btn:after {
        display: none;
      }
    }
  }
}
.win {
  height: 100%;
  background: #f1f1f1;
  min-height: 400px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  padding: 20px;

  .app-item {
    user-select: none;
    position: relative;
    width: 100px;
    margin-bottom: 5px;
    transition: transform 0.5s;
    border: 1px dashed transparent;
    align-self: start;

    &.righting {
      border: 1px solid #409eff;
      background: rgba(64, 158, 255, 0.19);
    }

    &.dragging {
      opacity: 0.5;
      border: 0.5px dashed #99999990;
    }

    &.drop-top:before {
      content: '';
      position: absolute;
      width: 100%;
      top: -2px;
      left: 0;
      border-bottom: 4px solid rgba(156, 196, 241, 0.65);
    }

    &.drop-bottom:after {
      content: '';
      position: absolute;
      width: 100%;
      bottom: -2px;
      left: 0;
      border-bottom: 4px solid rgba(156, 196, 241, 0.65);
    }

    &.drop-merge .app-inner {
      transform: scale(1.5);
    }

    .app-tit {
      color: #666;
      margin: 3px 5px 0;
      font-size: 14px;
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .app-inner {
      position: relative;
      margin: 10px auto;
      width: 70px;
      transition: transform 0.5s;

      .c-svg {
        font-size: 70px;
      }
    }

    .app-dir {
      position: relative;
      z-index: 9;
      height: 70px;

      .c-svg {
        font-size: 29px;
        transition: all 0.2s linear;
      }

      &.open {
        z-index: 99;

        .app-dir-inner {
          width: 180px;
          height: auto;
          box-shadow: 0 0 5px rgba(153, 153, 153, 0.5);

          .inner-app-item {
            margin-bottom: 8px;
          }

          .c-svg {
            font-size: 70px;
          }

          &:before {
            display: none;
          }
        }
      }

      .app-dir-inner {
        display: flex;
        flex-wrap: wrap;
        background: #fff;
        border-radius: 10px;
        padding: 5px;
        height: 70px;
        width: 100%;
        overflow: hidden;
        transition: all 0.2s linear;
        margin-bottom: 5px;

        .inner-app-item {
          width: 50%;
          text-align: center;
          margin: 1px 0;
        }

        .app-dir-tit {
          text-align: center;
          padding: 5px;
          color: #666;
          font-size: 14px;
          width: 100%;
          border-top: 1px solid #ccc;
        }

        &:before {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }
      }
    }
  }
}
.crumb-wrapper {
  display: flex;
  padding: 10px;
  background: #f1f1f1;
  margin-bottom: 10px;
  font-size: 10px;
  align-items: center;
  .icon-crumb {
    font-size: 30px;
    margin-right: 10px;
    cursor: pointer;
  }
  .crumb {
    user-select: none;
    flex: 1;
    padding: 15px;
    background: #fff;
  }
}
.search-wrapper {
  margin-bottom: 10px;
}
.contextmenu {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  background: #fff;
  font-size: 14px;
  color: #666;
  min-width: 250px;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  user-select: none;
  li {
    padding: 7px 16px;
    cursor: pointer;
    border: 1px solid #e9e9e9;
    border-bottom: none;
    &:hover {
      background: #f4f4f5;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9;
  }
}
/deep/ .dock-pop {
  padding: 0;
}
.dock-pop {
  padding: 0;
  .dock-pop-tit {
    text-align: center;
    padding: 5px;
    font-weight: bolder;
    line-height: 20px;
  }
  .dock-contextmenu {
    background: #fff;
    font-size: 12px;
    color: #666;
    box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
    li {
      padding: 5px 16px;
      cursor: pointer;
      border-top: 1px solid #e9e9e9;
      border-bottom: none;
      text-align: center;
      &:hover {
        background: #f4f4f5;
      }
      i {
        margin-right: 5px;
      }
    }
    li:last-child {
      border-top: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;
    }
  }
}
.mini-app-pop {
  background: #fff;
  font-size: 20px;
  color: #666;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    padding: 8px 10px;
    cursor: pointer;
    border-top: 1px solid #e9e9e9;
    border-bottom: none;
    text-align: left;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    .icon {
      font-size: 30px;
      vertical-align: middle;
    }
    &:hover {
      background: #f4f4f5;
    }
    i {
      margin-right: 5px;
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9;
  }
}
.tree-wrapper {
  background-color: #f1f1f1;
  height: 400px;
  overflow: auto;
}
</style>
