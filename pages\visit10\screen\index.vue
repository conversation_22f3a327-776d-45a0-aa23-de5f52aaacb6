<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="search-wrapper">
        <el-form
          ref="searchForm"
          :label-width="'80px'"
          :model="searchForm"
          size="small"
          inline
        >
          <el-form-item
            label="时间范围"
            prop="startTime"
          >
            <el-date-picker
              v-model="searchForm.dateRange"
              :value-format="'yyyy-MM-dd'"
              :clearable="false"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @input="loadData();loadJudge()"/>
          </el-form-item>
          <el-form-item
            label-width="0"
            label=""
            prop="endTime"
          >
            <el-radio
              v-for="item in timeList"
              :key="item.value"
              v-model="searchForm.dateType"
              :label="item.value">{{ item.label }}</el-radio>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="page-content-wrapper">
      <el-row
        :gutter="20"
        class="full-height">
        <el-col
          :span="16"
          class="full-height">
          <div class="full-height page-left">
            <div class="page-item">
              <el-col
                :span="12"
                class="full-height">
                <div class="page-card full-height shadow-light">
                  <div class="page-title">
                    <p>
                      模块访问量
                    </p>
                  </div>
                  <div class="page-inner">
                    <pie-chart :chart-data="moduleObj.chart"/>
                  </div>
                </div>
              </el-col>
              <el-col
                :span="12"
                class="full-height">
                <div class="page-card full-height shadow-light">
                  <div class="page-title">
                    <div>
                      <div class="float-right">
                        <el-select
                          v-model="pageObj.service"
                          placeholder="应用选择"
                          clearable
                          style="width: 140px">
                          <el-option
                            v-for="item in serviceList"
                            :key="item.name"
                            :label="item.cname"
                            :value="item.name"/>
                        </el-select>
                      </div>
                      页面访问量排名
                    </div>
                  </div>
                  <div class="page-inner">
                    <bars-chart
                      :show-legend="false"
                      :chart-data="pageObj.chart"
                      :x-data="pageObj.xData"
                      :transverse="true"
                      :data-zoom="true"/>
                  </div>
                </div>
              </el-col>
            </div>
            <div class="divider-hold"/>
            <div class="page-item">
              <el-col
                :span="24"
                class="full-height">
                <div class="page-card full-height shadow-light">
                  <div class="page-title">
                    <div 
                      class="float-right" 
                      style="white-space: nowrap">
                      <el-checkbox
                        v-model="departmentObj.leaderType" 
                        :true-label="1" 
                        :false-label="0">查看领导</el-checkbox>
                      &nbsp;
                      <select-org
                        v-if="departmentObj.leaderType !== 1"
                        v-model="departmentObj.orgCode"
                        :multiple="false"
                        style="display: inline-block"
                        @input="loadOrgData"/>
                    </div>
                    {{ departmentObj.leaderType === 1 ? '领导访问量' : '部门访问量排名' }}
                  </div>
                  <div 
                    v-loading="departmentObj.loading" 
                    class="page-inner">
                    <bars-chart
                      :show-legend="false"
                      :chart-data="departmentObj.chart"
                      :show-label="true"
                      :x-data="departmentObj.xData"
                      :data-zoom="true"
                      :data-zoom-size="15"/>
                  </div>
                </div>
              </el-col>
              <el-col
                v-if="false"
                :span="12"
                class="full-height">
                <div class="page-card full-height shadow-light">
                  <div class="page-title">
                    <div class="float-right">
                      <!--                      <el-select-->
                      <!--                        v-model="leaderObj.service"-->
                      <!--                        placeholder="应用选择"-->
                      <!--                        style="width: 140px">-->
                      <!--                        <el-option-->
                      <!--                          v-for="item in serviceList"-->
                      <!--                          :key="item.name"-->
                      <!--                          :label="item.cname"-->
                      <!--                          :value="item.name"/>-->
                      <!--                      </el-select>-->
                    </div>
                    中层领导访问量</div>
                  <div class="page-inner">
                    <bars-chart
                      :show-legend="false"
                      :chart-data="leaderObj.chart"
                      :x-data="leaderObj.xData"/>
                  </div>
                </div>
              </el-col>
            </div>
          </div>
        </el-col>
        <el-col
          :span="8"
          class="full-height">
          <div class="page-card shadow-light full-height">
            <div class="page-title">
              <div class="float-right">
                <!--                <el-select-->
                <!--                  v-model="leaderObj.service"-->
                <!--                  placeholder="应用选择"-->
                <!--                  style="width: 140px">-->
                <!--                  <el-option-->
                <!--                    v-for="item in serviceList"-->
                <!--                    :key="item.name"-->
                <!--                    :label="item.cname"-->
                <!--                    :value="item.name"/>-->
                <!--                </el-select>-->
              </div>
              页面owner访问量排名</div>
            <div class="page-inner">
              <bars-chart
                :show-legend="false"
                :chart-data="ownerObj.chart"
                :x-data="ownerObj.xData"
                :transverse="true"/>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { findBasicDataConfigByType, pageLogSecret } from '@/api/system'
import PieChart from '@/pages/visit/screen/component/pie-chart'
import { post } from '@/lib/Util'
import BarsChart from '@/pages/visit/screen/component/bars-chart'
import SelectOrg from '@/components/SelectOrg'

export default {
  name: 'visit10-screen',
  components: { SelectOrg, BarsChart, PieChart },
  layout: 'menuLayout',
  data: () => {
    return {
      searchForm: {
        dateType: ''
      },
      serviceList: [],
      timeList: [
        {
          label: '当日',
          value: 'day'
        },
        {
          label: '本周',
          value: 'week'
        },
        {
          label: '本月',
          value: 'month'
        }
      ],
      url: {
        list: pageLogSecret.appLogMultiCondition //分页接口地址
      },
      moduleObj: {
        type: 'day',
        chart: []
      },
      pageObj: {
        type: 'day',
        service: '',
        chart: [],
        xData: [],
        totalData: []
      },
      departmentObj: {
        type: 'day',
        service: '',
        leaderType: null,
        loading: false,
        chart: [],
        xData: []
      },
      leaderObj: {
        type: 'day',
        service: '',
        chart: [
          {
            data: [323, 3231, 454, 2123, 545],
            name: '<10w'
          }
        ],
        xData: ['页面1', '页面1', '页面1', '页面1', '页面1']
      },
      ownerObj: {
        type: 'day',
        service: '',
        chart: [],
        xData: []
      }
    }
  },
  watch: {
    'searchForm.dateType': function() {
      switch (this.searchForm.dateType) {
        case 'day':
          this.searchForm.dateRange = [
            this.$moment().format('yyyy-MM-DD'),
            this.$moment().format('yyyy-MM-DD')
          ]
          break
        case 'week':
          this.searchForm.dateRange = [
            this.$moment()
              .startOf('week')
              .format('yyyy-MM-DD'),
            this.$moment()
              .endOf('week')
              .format('yyyy-MM-DD')
          ]
          break
        case 'month':
          this.searchForm.dateRange = [
            this.$moment()
              .startOf('month')
              .format('yyyy-MM-DD'),
            this.$moment()
              .endOf('month')
              .format('yyyy-MM-DD')
          ]
          break
        default:
          break
      }
      this.loadData()
      this.loadJudge()
    },
    'departmentObj.leaderType': function(newValue) {
      this.loadJudge()
    },
    'pageObj.service': function() {
      this.formatData()
    }
  },
  mounted() {
    this.searchForm.dateRange = [
      this.$moment().format('yyyy-MM-DD'),
      this.$moment().format('yyyy-MM-DD')
    ]
    this.departmentObj.orgCode = JSON.parse(
      localStorage.getItem('userDetail')
    ).orgCode
    this.loadData()
  },
  methods: {
    async loadData() {
      if (!this.serviceList.length) {
        await this.findBasicDataConfigByType()
      }
      post(pageLogSecret.getPageLogLookBoard, {
        loginTimeStart: this.searchForm.dateRange[0],
        loginTimeEnd: this.searchForm.dateRange[1]
      }).then(res => {
        // 模块
        this.moduleObj.chart = res.data.module.map(item => {
          return {
            value: item.num,
            name: this.getServiceName(item.code).cname
          }
        })
        // 页面
        res.data.page.forEach(item => {
          item.service = this.getServiceName(item.code).cname
        })
        this.pageObj.totalData = res.data.page.reverse()
        this.formatData()
        // owner
        const owner = res.data.owner.reverse()
        this.ownerObj.xData = owner.map(item => item.userName)
        this.ownerObj.chart = [
          {
            data: owner.map(item => item.accessNum),
            name: '访问次数'
          }
        ]
      })
    },
    formatData() {
      const page = this.pageObj.totalData.filter(
        item => !this.pageObj.service || item.code === this.pageObj.service
      )
      this.pageObj.xData = page.map(item => item.name)
      this.pageObj.chart = [
        {
          data: page.map(item => {
            return {
              value: item.num,
              text:
                (item.service !== item.name ? item.service + '-' : '') +
                (item.parentName && item.parentName !== item.service
                  ? item.parentName + '-'
                  : '') +
                item.name
            }
          }),
          name: '访问次数'
        }
      ]
    },
    loadJudge() {
      if (this.departmentObj.leaderType === 1) {
        this.loadLeaderData()
      } else {
        this.loadOrgData()
      }
    },
    loadOrgData() {
      // 部门数据获取
      post(pageLogSecret.getPageLogLookBoardByOrg, {
        loginTimeStart: this.searchForm.dateRange[0],
        loginTimeEnd: this.searchForm.dateRange[1],
        orgCode: this.departmentObj.orgCode
      }).then(res => {
        //
        this.departmentObj.xData = res.data.org.map(item => item.name)
        this.departmentObj.chart = [
          {
            data: res.data.org.map(item => item.num),
            name: '访问次数'
          }
        ]
      })
    },
    loadLeaderData() {
      // 部门数据获取
      post(pageLogSecret.getLeaderAccess, {
        loginTimeStart: this.searchForm.dateRange[0],
        loginTimeEnd: this.searchForm.dateRange[1],
        leaderType: this.departmentObj.leaderType,
        pageSize: 100
      }).then(res => {
        //
        this.departmentObj.xData = res.data.content.map(item => item.userName)
        this.departmentObj.chart = [
          {
            data: res.data.content.map(item => item.accessNum),
            name: '访问次数'
          }
        ]
      })
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.name === name) || {}
    },
    //
    async findBasicDataConfigByType() {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
      return Promise.resolve(true)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.float-right {
  float: right;
}
.page-content {
  display: flex;
  flex-direction: column;
  .page-content-wrapper {
    margin: -10px;
    padding: 10px;
    flex: 1;
    overflow: auto;
  }
  .page-left {
    display: flex;
    flex-direction: column;
    .page-item {
      flex: 1;
      margin: 0 -10px;
    }
  }
  .divider-hold {
    height: 25px;
  }
  .page-card {
    display: flex;
    flex-direction: column;
    .page-inner {
      flex: 1;
    }
    .operate {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
