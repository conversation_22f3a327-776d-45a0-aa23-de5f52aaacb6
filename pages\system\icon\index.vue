<template>
  <div class="page-content">
    <div class="page-operate">
      <div>
        <el-button
          icon="el-icon-circle-plus-outline"
          type="success"
          @click="handleAdd"
        >新增
        </el-button>
      </div>
    </div>
    <div
      class="page-card shadow-light">
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        class="custom-table"
        border
        style="width: 100%; background-color: #eee"
      >
        <el-table-column
          label="序号"
          type="index"
          width="180"
        />
        <el-table-column
          label="图标"
          prop="isDef"
        >
          <template
            v-slot="{row}"
          >
            <img
              :class="row.iconType === 2 ? 'gray': ''"
              :src="row.resource"
              alt=""
              style="width: 30px; display: block"
            >
          </template>
        </el-table-column>
        <el-table-column
          label="图标类型"
          prop="isDef"
        >
          <template
            v-slot="{row}"
          >
            {{ row.iconType === 1 ? '桌面图标' : '菜单图标' }}
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="270"
        >
          <template
            v-slot="{row}"
          >
            <span>
              <el-button
                slot="reference"
                type="text"
                @click="handleDelete(row)"
              >删除
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Edit
      ref="modalForm"
      :type-list="typeList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import { deleteImg, listImg } from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'

export default {
  layout: 'menuLayout',
  name: 'Icon',
  components: {
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: listImg, //分页接口地址
        delete: deleteImg //删除接口地址
      },
      roleStatusList: ENUM.roleStatus,
      editRole: null,
      typeList: [
        {
          value: 1,
          label: '桌面图标'
        },
        {
          value: 2,
          label: '菜单图标'
        }
      ]
    }
  },
  methods: {
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data : []
      this.loading = false
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.gray {
  background-color: #999;
}
</style>
