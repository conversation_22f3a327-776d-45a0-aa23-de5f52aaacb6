<template>
  <div>

    <div class="page-content">
      <div class="page-card shadow-light">
        <div style="position: relative">
          <el-tabs
            v-model="activeName"
            style="user-select: none"
            @tab-click="handleClick">
            <el-tab-pane
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :name="item.value.toString()"/>
            <template>
              <div
                class="operate-wrapper">
                <div class="page-operate">
                  <div class="search-wrapper">
                    {{ getDict(activeName, 'moduleList').label }}模块综合得分：<span style="color: crimson; font-size: 20px">{{ score }}</span>
                  </div>
                  <div>
                    <el-button
                      icon="el-icon-circle-plus-outline"
                      type="success"
                      @click="handleAdd"
                    >新增
                    </el-button>
                  </div>
                </div>
                <el-table
                  v-loading="loading"
                  :data="tablePageData"
                  :size="size"
                  :header-cell-class-name="'custom-header'"
                  :span-method="arraySpanMethod"
                  border
                  style="width: 100%"
                  height="70vh"
                >
                  <el-table-column
                    label="一级菜单栏"
                    prop="name"
                    fixed="left"
                    width="150"
                    show-overflow-tooltip
                  >
                    <template v-slot="{ row }">
                      <span v-if="row.parentId === '0'">{{ row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    fixed="left"
                    label="子菜单栏"
                    prop="name"
                    width="180"
                    show-overflow-tooltip
                  >
                    <template v-slot="{ row }">
                      <span v-if="row.parentId !== '0'">{{ row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'综合\n评分'"
                    prop="comprehensiveScore"
                    width="50"
                  >
                    <template v-slot="{ row }">
                      <span v-if="row.parentId !== '0'">{{ row.comprehensiveScore }}</span>
                      <el-progress
                        v-else
                        :text-inside="true"
                        :stroke-width="15"
                        :percentage="row.comprehensiveScore"
                        status="success"/>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'功能\n开发'"
                    prop="functionDev"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="color1 node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.functionDev"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'内部\n测试'"
                    prop="internalTest"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="color2 node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.internalTest"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'外部\n试用'"
                    prop="externalTrial"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="color3 node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.externalTrial"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'功能\n培训'"
                    prop="functionTrain"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="color4 node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.functionTrain"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'功能\n上线'"
                    prop="functionOnline"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="color5 node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.functionOnline"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="' 功能计划\r上线日期'"
                    prop="functionOnline"
                    width="85"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'">
                        {{ getOnlineDate(row) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :class-name="'node-cell'"
                    :width="42 * weekNum + 1"
                  >
                    <template
                      slot-scope="{ column, $index }"
                      slot="header">
                      <div
                        v-for="(val, month) in cycle"
                        :key="month + guid()"
                        class="header-wrapper">
                        <div
                          :class="{'deadline': month === '2023-03'}"
                          class="header-month">{{ month }}</div>
                        <div class="header-weeks">
                          <span
                            v-for="(weeks, week) in cycle[month]"
                            :key="week"
                            :class="{'deadline': week === '13'}"
                            class="header-week"
                            @click="viewSummary(month, week)">{{ week }}</span>
                        </div>
                      </div>
                    </template>
                    <template v-slot="{ row, column, $index }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="node-wrapper">
                        <template v-for="(val, month) in cycle">
                          <span
                            :key="month">
                            <div
                              v-for="(weeks, week) in cycle[month]"
                              :key="month + week"
                              :class="{'deadline': week === '13'}"
                              class="color-block"
                              @click.stop="editGantt(row, month, week)">
                              <template v-for="(day, index) in weeks">
                                <span
                                  v-if="!getDayClass(row, day)"
                                  :key="index"
                                  class="color-week-block"/>
                                <el-tooltip 
                                  v-else
                                  :key="index"
                                  :content="getDayPop(row, day)"
                                  :open-delay="200"
                                  class="item" 
                                  effect="dark"
                                  placement="top">
                                  <span
                                    :key="index"
                                    :class="getDayClass(row, day)"
                                    class="color-week-block"/>
                                </el-tooltip>
                              </template>
                              <div
                                v-if="currentRow && row.id === currentRow.id && month === currentEdit[0] && week === currentEdit[1]"
                                :class="{'top': $index > 4 && dataList.length - $index < 5}"
                                class="color-select shadow-light">
                                <span class="pop-arrow"/>
                                <el-popover
                                  v-for="(work, wIndex) in workList"
                                  :key="wIndex"
                                  :ref="'pop' + work.value"
                                  placement="right"
                                  width="300"
                                  trigger="click">
                                  <el-form 
                                    ref="form"
                                    :model="formPop"
                                    label-width="0"
                                    class="search-wrapper">
                                    <el-form-item label="">
                                      <el-date-picker
                                        v-model="formPop.dateRange"
                                        :value-format="'yyyy-MM-dd'"
                                        type="daterange"
                                        format="yyyy-MM-dd"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        style="width: 100%"/>
                                    </el-form-item>
                                    <el-form-item>
                                      <el-button 
                                        type="primary"
                                        @click="setGantt()">确定</el-button>
                                      <el-button @click="setGantt(true)">清除节点</el-button>
                                    </el-form-item>
                                  </el-form>
                                  <div slot="reference">
                                    <div
                                      class="select-item"
                                      @click="selectGanttNode(row, work.value)">
                                      <span
                                        :class="'color' + work.value"
                                        class="color-node"/>
                                      {{ work.label }}</div>
                                  </div>
                                </el-popover>
                              </div>
                            </div>
                          </span>

                        </template>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    width="100"
                    fixed="right"
                  >
                    <template
                      v-slot="{row}"
                    >
                      <span>
                        <el-button
                          type="text"
                          @click="handleEdit(row)"
                        >编辑
                        </el-button>
                      </span>
                      <span v-if="!row.children || !row.children.length">
                        <el-divider direction="vertical" />
                        <el-button
                          slot="reference"
                          type="text"
                          @click="handleDelete(row)"
                        >删除
                        </el-button>
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
                <el-row
                  align="middle"
                  class="table-pagination"
                  justify="end"
                  type="flex"
                  style="margin-top: 15px"
                >
                  <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    :total="totalCount"
                    :page-sizes="[15, 30, 50, 100]"
                    layout="total, sizes, jumper, prev, pager, next"
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"/>
                </el-row>
              </div>
            </template>
          </el-tabs>
        </div>
      </div>
    </div>
    <Edit
      ref="modalForm"
      :module-list="moduleList"
      :module="activeName"
      @success="handleSearch()"
    />
    <Handle
      ref="modalHandle"
      :current-edit="currentEdit"
      :current-row="currentRow"
      @success="handleSearch()" />
    <Detail
      ref="modalDetailForm"
      :module-list="moduleList"
      :detail="showDetail"
    />
  </div>
</template>

<script>
import {
  dictionaryDtlFindByDictCode,
  onlineDelete,
  onlineFind,
  onlineSave,
  progressDelete,
  progressFind,
  progressSave,
  weeklySummaryFind
} from '@/api/system'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import * as _ from 'lodash'
import listMixins from '@/mixins/ListMixins'
import Edit from '@/pages/system/progress331/component/edit'
import Handle from '@/pages/system/progress331/component/handle'
import Detail from '@/pages/system/summary/component/detail'

export default {
  name: 'Progress',
  components: { Handle, Edit, Detail },
  layout: 'menuLayout',
  mixins: [listMixins],
  data() {
    return {
      formData: {
        work: 1
      },
      formPop: {},
      activeModule: '',
      activeName: '1',
      dataList: [],
      url: {
        edit: onlineSave,
        list: onlineFind, //分页接口地址
        delete: onlineDelete, //删除接口地址
        save: onlineSave,
        summaryList: weeklySummaryFind,
        getDict: dictionaryDtlFindByDictCode
      },
      page: {
        pageIndex: 1,
        pageSize: 2000,
        total: 0,
        totalPages: 0
      },
      moduleList: [],
      workList: ENUM.onlineList,
      loading: true,
      selectVisible: false,
      currentRow: null,
      currentEdit: [],
      weeks: [1, 2, 3, 4],
      cycle: {
        '2023-01': {},
        '2023-02': {},
        '2023-03': {},
        '2023-04': {},
        '2023-05': {},
        '2023-06': {}
      },
      showDetail: {},
      // 默认显示第几页
      currentPage: 1,
      pageSize: 15,
      searchName: null
    }
  },
  computed: {
    // 总条数，根据接口获取数据长度(注意：这里不能为空)
    totalCount: function() {
      return this.dataList.length || 0
    },
    tablePageData: function() {
      return this.dataList.slice(
        (this.currentPage - 1) * this.pageSize,
        this.currentPage * this.pageSize
      )
    },
    score: function() {
      if (!this.dataList.length) return '0.00'
      return (
        _.sumBy(
          this.dataList.filter(item => item.parentId !== '0'),
          'comprehensiveScore'
        ) / this.dataList.filter(item => item.parentId !== '0').length
      ).toFixed(2)
    },
    weekNum: function() {
      let num = 0
      for (const key in this.cycle) {
        // console.log(Object.getOwnPropertyNames(this.cycle[key]))
        num += Object.getOwnPropertyNames(this.cycle[key]).length - 1
      }
      return num
    }
  },
  watch: {},
  created() {
    this.formatData()
    this.reset()
  },
  mounted() {
    this.bindClick()
  },
  destroyed() {
    this.unbindClick()
  },
  methods: {
    // 生成时间数据
    formatData() {
      for (const monthName in this.cycle) {
        const _year = monthName.substring(0, 4)
        const dayOfWeek = this.$moment(monthName).weekday()
        let num = this.$moment(monthName).daysInMonth()
        if (dayOfWeek <= 5) {
          num -= 5 - dayOfWeek
        } else {
          num -= 8 - dayOfWeek + 4
        }
        // 月含周数
        const weekNum = parseInt(num / 7) + (num % 7 > 0 ? 1 : 0)
        // 计算月第一周数
        let firstWeek = 0
        if (dayOfWeek <= 5) {
          firstWeek = this.$moment(monthName)
            .add(5 - dayOfWeek, 'days')
            .week()
        } else {
          firstWeek = this.$moment(monthName)
            .add(8 - dayOfWeek + 4, 'days')
            .week()
        }
        // 添加周数
        for (let i = 0; i < weekNum; i++) {
          this.cycle[monthName][firstWeek + i] = []
          for (let j = 0; j < 7; j++) {
            const _weekday = this.$moment(
              `${_year}-${firstWeek + i}`,
              'gggg-ww'
            )
              .weekday(j)
              .format('YYYY-MM-DD')
            this.cycle[monthName][firstWeek + i].push(_weekday)
          }
        }
      }
      // console.log(this.cycle)
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.parentId === '0') {
        if (columnIndex === 2) {
          return [1, 6]
        } else if ([3, 4, 5, 6, 7].includes(columnIndex)) {
          return [0, 0]
        }
      }
    },
    bindClick() {
      document.addEventListener('click', this.cancelPop)
    },
    unbindClick() {
      document.removeEventListener('click', this.cancelPop)
    },
    cancelPop() {
      this.currentRow = null
    },
    async handleSearch(reset = false) {
      if (!this.moduleList.length) {
        const { data: module } = await post(this.url.getDict, {
          dictCode: 'module'
        })
        this.moduleList = module.map(item => {
          return {
            value: item.code,
            label: item.value
          }
        })
        this.activeName = this.moduleList.length ? this.moduleList[0].value : ''
      }
      if (!this.url || !this.url.list) {
        this.$message.warning('请设置url.list属性!')
        return
      }
      if (!this.activeName) {
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.currentPage = 1
        this.pageSize = 15
      }
      // 搜索
      this.loading = true
      this.searchForm.module = this.activeName
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data.content : []
      this.page.pageSize = data.pageable.pageSize
      this.page.totalPages = data.totalPages
      this.page.total = data.totalElements
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    afterHandleSearch() {
      const arr = this.tableData.filter(res => res.parentId == '0')
      for (let i = arr.length - 1; i >= 0; i--) {
        arr[i].children = this.tableData.filter(
          res => res.parentId == arr[i].id
        )
        arr.splice(
          i + 1,
          0,
          ...this.tableData.filter(res => res.parentId === arr[i].id)
        )
      }
      arr.forEach(item => {
        item.id = item.id.toString()
        item.module = item.module.toString()
        item.parentId = item.parentId.toString()
        item.children = item.children || []
        item.ganttData = Object.assign(
          {},
          item.ganttData ? JSON.parse(item.ganttData) : {}
        )
      })
      this.dataList = arr
    },
    reset() {},
    handleClick(tab, event) {
      this.handleSearch(true)
    },
    handleAdd: function() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.visible = true
      this.$refs.modalForm.formData.module = this.activeName
      this.$refs.modalForm.getMenuList()
    },
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
      this.$refs.modalForm.getMenuList()
      this.$refs.modalForm.onOpen()
    },
    handleHandle: function(row, month, week) {
      this.$refs.modalHandle.edit(row)
      this.$refs.modalHandle.visible = true
      this.$refs.modalHandle.currentRow = _.cloneDeep(row)
      this.$refs.modalHandle.currentEdit = [month, week]
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          this.currentRow.ganttData[this.currentEdit[0]][
            this.currentEdit[1]
          ] = this.formData.work
          const params = Object.assign(this.currentRow, {
            ganttData: JSON.stringify(this.currentRow.ganttData)
          })
          post(this.url.save, params).then(res => {
            if (res.success) {
              this.$message.success('修改成功！')
              this.selectVisible = false
              this.loading = false
              this.handleSearch()
            } else {
              this.$message.warning('修改失败！')
              this.loading = false
            }
          })
        })
      }
    },
    async viewSummary(month, week) {
      const _year = month.substring(0, 4)
      const _month = Number(month.substring(5, 7))
      const _week = week
      console.log(
        this.$moment(`${month}-01`).week(),
        this.$moment(`${_year}-${_week}`, 'gggg-ww')
          .weekday(1)
          .format('YYYY-MM-DD'),
        this.$moment(`${_year}-${_week}`, 'gggg-ww')
          .weekday(7)
          .format('YYYY-MM-DD')
      )
      const { data } = await post(
        this.url.summaryList,
        Object.assign(
          {
            startDate: this.$moment(`${_year}-${_week}`, 'gggg-ww')
              .weekday(1)
              .format('YYYY-MM-DD'),
            endDate: this.$moment(`${_year}-${_week}`, 'gggg-ww')
              .weekday(7)
              .format('YYYY-MM-DD'),
            module: this.module
          },
          this.searchForm,
          {
            pageIndex: 1,
            pageSize: 99
          }
        )
      )
      if (data.content.length) {
        this.showDetail = data.content[0]
        this.$refs.modalDetailForm.visible = true
      } else {
        this.$message.warning('暂无周总结！')
      }
    },
    selectGanttNode(row, module) {
      this.formPop = {}
      if (this.activeModule) {
        this.$refs['pop' + this.activeModule].forEach(item => item.doClose())
      }
      this.activeModule = module
      if (
        this.currentRow.ganttData[this.activeModule] &&
        this.currentRow.ganttData[this.activeModule].endDate
      ) {
        //
      }
    },
    setGantt(isClear = false) {
      if (isClear) {
        this.currentRow.ganttData[this.activeModule] = {}
      } else {
        this.currentRow.ganttData[this.activeModule] = {
          startDate: this.formPop.dateRange[0],
          endDate: this.formPop.dateRange[1]
        }
      }
      delete this.currentRow.ganttData[undefined]
      const params = Object.assign(this.currentRow, {
        ganttData: JSON.stringify(this.currentRow.ganttData)
      })
      // return console.log(this.formPop.dateRange)
      // return console.log(this.currentRow.ganttData)
      post(this.url.edit, params).then(res => {
        if (res.success) {
          this.$message.success('修改成功！')
          this.selectVisible = false
          this.currentRow = null
          this.handleSearch()
          this.formPop = {}
        } else {
          this.$message.warning('修改失败！')
          this.currentRow = null
        }
      })
    },
    editGantt(row, month, week) {
      console.log(month, week)
      // this.handleHandle()
      this.currentRow = _.cloneDeep(row)
      this.currentEdit = [month, week]
    },
    getDayClass(row, day) {
      if (!row.ganttData) return ''
      const data = row.ganttData
      // console.log(data)
      for (const key in data) {
        if (data[key] && data[key].startDate) {
          if (this.isDuringDate(day, data[key].startDate, data[key].endDate)) {
            if (!this.workList.find(item => item.value == key)) return ''
            return 'color' + key
          }
        }
      }
      return ''
    },
    getDayPop(row, day) {
      if (!row.ganttData) return ''
      const data = row.ganttData
      // console.log(data)
      for (const key in data) {
        if (data[key] && data[key].startDate) {
          if (this.isDuringDate(day, data[key].startDate, data[key].endDate)) {
            if (!this.workList.find(item => item.value == key)) return ''
            return (
              this.workList.find(item => item.value == key).label +
              '（' +
              data[key].startDate +
              ' — ' +
              data[key].endDate +
              ')'
            )
          }
        }
      }
      return ''
    },
    isDuringDate(checkDateStr, beginDateStr, endDateStr) {
      if (checkDateStr >= beginDateStr && checkDateStr <= endDateStr) {
        return true
      }
      return false
    },
    getOnlineDate: function(row) {
      if (!row.ganttData) return ''
      const data = row.ganttData
      if (data['5'] && data['5'].endDate) {
        return data['5'].endDate
      }
      return ''
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
    // 分页
    // 每页显示的条数
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    // 显示第几页
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped lang="less">
.operate-wrapper {
  margin: 10px;
}
.tab-select {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 9;
}
.operate-wrapper {
  .node {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 5px;
  }
  /deep/ .color1,
  /deep/ .orange {
    background-color: #f4731e !important;
    border-right-color: #f4731e !important;
  }
  /deep/ .color2,
  /deep/ .green {
    background-color: #91c650 !important;
    border-right-color: #91c650 !important;
  }
  /deep/ .color3,
  /deep/ .blue {
    background-color: #1239aa !important;
    border-right-color: #1239aa !important;
  }
  /deep/ .color4,
  /deep/ .yellow {
    background-color: #9a60b4 !important;
    border-right-color: #9a60b4 !important;
  }
  /deep/ .color5,
  /deep/ .red {
    background-color: #de291d !important;
    border-right-color: #de291d !important;
  }
  /deep/ .color6,
  /deep/ .lightBlue {
    background-color: #159fec !important;
    border-right-color: #159fec !important;
  }
  /deep/ .el-progress-bar__outer {
    background-color: #ccc;
  }
  /deep/ .custom-header {
    text-align: center;
    background-color: #666;
  }
  /deep/ .color-block {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    border-right: 1px solid #e6e9f4;
    width: 42px;
    height: 36px;
    cursor: pointer;
  }
  /deep/ .deadline {
    position: relative;
    &:after {
      content: '';
      position: absolute;
      right: -1px;
      top: -1px;
      bottom: -1px;
      width: 3px;
      background: #ff0000;
      z-index: 1;
    }
  }
  /deep/ .color-block .color-week-block {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: 6px;
    height: 36px;
    cursor: pointer;
  }
  /deep/ .node-wrapper {
    white-space: nowrap;
    font-size: 0;
  }
  /deep/ .node-cell .cell {
    padding: 0;
    overflow: visible;
  }
  /deep/ .node-cell {
    padding: 0 !important;
  }
  /deep/ .el-dropdown {
    font-size: 0;
  }
  /deep/ .el-table thead th.el-table__cell {
    background: #f5f7fa !important;
    border-right-color: #e6e9f4 !important;
  }
  /deep/ .el-table--small .el-table__cell {
    padding: 2px 0;
  }
}
.header-wrapper {
  display: inline-block;
  text-align: center;
  .header-month {
    text-align: center;
    border-bottom: 1px solid #e6e9f4;
    border-right: 1px solid #e6e9f4;
  }
  .header-weeks {
    font-size: 0;
  }
  .header-week {
    font-size: 12px;
    display: inline-block;
    vertical-align: middle;
    border-right: 1px solid #e6e9f4;
    width: 42px;
    height: 36px;
    line-height: 36px;
    cursor: pointer;
  }
  .deadline {
    position: relative;
    &:after {
      content: '';
      position: absolute;
      right: -1px;
      top: -1px;
      bottom: -1px;
      width: 3px;
      background: #ff0000;
      z-index: 1;
    }
  }
}
.color-select {
  width: 100px;
  position: absolute;
  top: 95%;
  left: 0;
  font-size: 12px;
  background: #fff;
  line-height: 25px;
  z-index: 999;
  text-align: center;
  &.top {
    top: unset;
    bottom: 95%;
    .pop-arrow {
      top: unset;
      bottom: -12px;
      border-top-width: 6px;
      border-bottom-color: transparent;
      border-top-color: #ebeef5;
    }
  }
  .pop-arrow {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
    top: -6px;
    left: 13px;
    margin-right: 3px;
    border-top-width: 0;
    border-bottom-color: #ebeef5;
  }
  .select-item:hover {
    background: #d2d0d0;
  }
  .color-node {
    display: inline-block;
    vertical-align: middle;
    height: 15px;
    width: 20px;
  }
}
</style>
