<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '会议纪要'"
      :width="'600px'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        label-width="160px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          :rules="[{ required: true, message: '选择组织部门' }]"
          label="组织部门"
          prop="hostOrgName"
        >
          <select-org
            v-model="formData.hostOrgName"
            :multiple="false"
            @on-change="handleOrgCodeChange"/>
        </el-form-item>
        <el-form-item
          label="主持人"
          prop="host"
        >
          <user-select v-model="formData.host"/>
        </el-form-item>
        <el-form-item
          label="主题"
          prop="title"
        >
          <el-input
            v-model="formData.title"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入主题"
          />
        </el-form-item>
        <el-form-item
          label="会议内容"
          prop="content"
        >
          <textarea
            v-model="formData.content"
            :style="{width: '100%',height: '80px'}"
            clearable
            placeholder="请输入会议内容"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
        >
          <textarea
            v-model="formData.remark"
            :style="{width: '100%',height: '80px'}"
            clearable
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item
          label="上传文件"
          prop="roleCode"
        >
          <div
            v-if="formData.attachList && formData.attachList.length"
            class="video-list">
            <template v-for="item in formData.attachList">
              <p
                :key="item.id"
                class="video-item">
                <span>

                  <a :href="url.download + item.id">
                    <i
                      :title="item.name"
                      class="play-icon el-icon-reading"/>
                  </a>
                  <em @click="showVideo(item.id)">{{ item.name }}</em>
                </span>
                <i
                  title="删除"
                  class="delete-icon el-icon-delete"
                  @click="deleteVideo(item.id)"/>
              </p>
            </template>
          </div>
          <el-upload
            ref="upload"
            :auto-upload="false"
            :http-request="httpRequest"
            class="upload-demo"
            action="#">
            <el-button
              slot="trigger"
              size="small"
              type="primary">选取文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="saveFunction"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  downloadFileById,
  meetingRecordSave,
  meetingRecordEdit,
  meetingRecordUpload,
  findAttachesList
} from '@/api/system'
import { post } from '@/lib/Util'
import { imgIp } from '@/config'
import UserSelect from '@/pages/system/meetingMinutes/component/userSelect'
import SelectOrg from '@/pages/system/meetingMinutes/component/SelectOrg'

export default {
  components: { UserSelect, SelectOrg },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    relatedId: {
      type: String,
      default: ''
    },
    typeList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      serviceNo: 'meeting',
      url: {
        upload: meetingRecordUpload,
        download: imgIp + downloadFileById,
        edit: meetingRecordEdit,
        add: meetingRecordSave,
        attachList: findAttachesList
      },
      formData: {},
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('角色编辑页面')
  },
  methods: {
    //保存方法，调用上传附件和提交表单
    saveFunction() {
      const files = this.$refs.upload.uploadFiles
      if (!files.length || files.length > 0) {
        // 定义允许的文件扩展名
        const allowedExtensions = ['.pdf', '.docx', '.doc']
        if (
          !files.every(item =>
            allowedExtensions.some(ext => item.name.toLowerCase().endsWith(ext))
          )
        ) {
          return this.$message.warning('只能上传PDF、DOC、DOCX文件')
        }
        this.uploadFile()
      }
      setTimeout(() => {
        //500 毫秒后执行的代码
        this.handelConfirm()
      }, 500)
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            const params = _.cloneDeep(this.formData)
            params.orgID = params.orgID ? params.orgID.join(',') : ''
            params.moduleCode = params.moduleCode
              ? params.moduleCode.join(',')
              : ''
            post(this.url.edit, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            const params = _.cloneDeep(this.formData)
            params.orgID = params.orgID ? params.orgID.join(',') : ''
            params.moduleCode = params.moduleCode
              ? params.moduleCode.join(',')
              : ''
            post(this.url.add, params).then(res => {
              this.loading = false
              if (res.success) {
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    },
    uploadFile() {
      const files = this.$refs.upload.uploadFiles
      if (!files.length) {
        return this.$message.warning('请选择文件')
      }
      this.loading = true
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      formData.append('relatedId', this.relatedId)
      formData.append('serviceNo', this.serviceNo)
      post(this.url.upload, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.success) {
          this.formData.attachList = res.data
          this.$refs.upload.clearFiles()
        }
        this.loading = false
      })
    },
    handleOrgCodeChange(code) {
      this.formData.hostOrgID = code
    },
    showVideo(id) {
      this.$emit('show', id)
    },
    deleteVideo(id) {
      this.$confirm('你确定要执行删除文件操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          post(deleteFileByIds, { ids: [id] }).then(res => {
            this.formData.videoIds = this.formData.videoIds.filter(
              item => item.id !== id
            )
          })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          setTimeout(() => {
            //500 毫秒后执行的代码
            this.loadFileListFromServer()
          }, 500)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //刷新文件列表
    loadFileListFromServer() {
      const params = {
        relatedId: this.formData.id
      }
      post(this.url.attachList, params).then(res => {
        if (res.success) {
          this.formData.attachList = res.data
        } else {
          this.$message.warning('刷新列表失败！')
        }
      })
    },
    httpRequest(params) {}
  }
}
</script>
<style scoped lang="less">
.video-list {
  border: 1px solid #eee;
  margin-bottom: 5px;
}
.video-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #eee;
  &:last-child {
    border: none;
  }
  .play-icon {
    font-size: 26px;
    cursor: pointer;
    vertical-align: middle;
  }
  .delete-icon {
    font-size: 18px;
    cursor: pointer;
  }
  em {
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
