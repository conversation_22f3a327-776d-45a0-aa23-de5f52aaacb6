<template>
  <div
    class="content"
  >
    <div
      class="page-operate"
      style="align-items: flex-start">
      <div class="search-wrapper">
        <el-form
          ref="form"
          :model="searchForm"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label="报警厂区"
            prop="ruleName"
          >
            <el-input
              v-model="searchForm.waringPlant"
              clearable
              size="small"
              placeholder="报警厂区"
              style="width: 130px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="报警区域"
            prop="ruleName"
          >
            <el-input
              v-model="searchForm.areaName"
              clearable
              size="small"
              placeholder="报警区域"
              style="width: 130px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="设备名称"
            prop="deviceName"
          >
            <el-input
              v-model="searchForm.deviceName"
              clearable
              size="small"
              placeholder="设备名称"
              style="width: 130px"
              type="text"
            />
          </el-form-item><el-form-item
            label="模块"
            prop="serviceName"
          >
            <el-select
              v-model="searchForm.serviceName"
              size="small"
              clearable
              style="width: 130px"
              placeholder="选择模块"
            >
              <el-option
                v-for="(item, index) in moduleList"
                :key="index"
                :label="item.Name"
                :value="item.ID"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报警类型"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.warningType"
              size="small"
              clearable
              style="width: 130px"
              placeholder="报警类型"
            >
              <el-option
                v-for="(item, index) in warningTypeList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报警级别"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.alertLevel"
              size="small"
              clearable
              style="width: 130px"
              placeholder="报警级别"
            >
              <el-option
                v-for="(item, index) in alertLevelList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="状态"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.status"
              size="small"
              clearable
              style="width: 130px"
              placeholder="状态"
            >
              <el-option
                v-for="(item, index) in statusList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="时间"
            prop="dateTime"
          >
            <el-date-picker
              v-model="searchForm.dateTime"
              :picker-options="pickerOptions"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"/>
          </el-form-item>
        </el-form>
      </div>
      <div
        class="text-right"
        style="white-space: nowrap">
        <el-button
          icon="ios-search"
          type="primary"
          @click="handleSearch"
        >查询
        </el-button>
      </div>
    </div>
    <div class="widget-list">
      <el-table
        :data="tableData"
        :row-class-name="getRowClass"
        size="small"
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="所属模块"
          prop="serviceNameCN"
          width="100"
        />
        <el-table-column
          label="报警内容"
          show-overflow-tooltip
          prop="warningEventBody.title"
          min-width="100"
        />
        <el-table-column
          label="报警内容"
          show-overflow-tooltip
          prop="warningEventBody.msg"
          min-width="100"
        />
        <el-table-column
          label="区域"
          prop="areaName"
          width="100"
        />
        <el-table-column
          label="设备名称"
          prop="deviceName"
          width="100"
        />
        <el-table-column
          label="点位名称"
          prop="pointName"
          width="100"
        />
        <el-table-column
          label="预警类型"
          prop="warningType"
          width="120"
        >
          <template v-slot="{row}">
            {{ getDict(row.warningType, 'warningTypeList').name }}
          </template>
        </el-table-column>
        <el-table-column
          label="报警等级"
          prop="alertLevel"
          width="100"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.alertLevel == 1"
              type="danger">一级</el-tag>
            <el-tag
              v-if="row.alertLevel == 2"
              type="warning">二级</el-tag>
            <el-tag
              v-if="row.alertLevel == 3">三级</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          width="80"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.status == 0"
              type="danger">
              未处理
            </el-tag>
            <el-tag
              v-if="row.status == 1"
              type="success">
              已处理
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="日期"
          prop="createDateTime"
          width="140"
        />
        <el-table-column
          :width="80"
          label="操作"
          prop="date"
        >
          <template
            v-slot="{row}"
          >

            <el-button
              type="text"
              @click="popHistory.pageIndex = 1;showHistory(row.warningRuleID)">历史记录</el-button>
            <span v-if="row.status == 0 && row.warningEventBody.alertUrl">
              <el-button
                size="small"
                type="text"
                @click="handleWarning(row)"
              >去处理
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-row
      align="middle"
      class="table-pagination"
      justify="center"
      type="flex"
    >
      <el-pagination
        :current-page="page.pageIndex"
        :page-size="page.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>

    <el-dialog
      v-el-drag-dialog
      :top="'3%'"
      :title="'处理统计'"
      :width="'80%'"
      :visible.sync="popVisible1"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >
      <el-table
        :data="popHistory.list"
        size="small"
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="处理意见"
          prop="alertAdvice"
        />
        <el-table-column
          label="次数"
          prop="count"
        />
      </el-table>
    </el-dialog>

    <warning-detail
      ref="modalDetailForm"
      :id="popHistory.id"/>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  findAlertInfo,
  findHistoryByWaringEventID,
  findModuleInfoList
} from '@/api/system'
import WarningDetail from '@/pages/widget/component/warningDetail'

export default {
  name: 'WidgetEarlyWarning',
  components: { WarningDetail },
  layout: 'staticPage',
  mixins: [listMixins],
  data() {
    return {
      searchForm: {
        userNo: localStorage.getItem('userId')
      },
      url: {
        list: findAlertInfo
      },
      tableData: [],
      moduleList: [],
      popVisible1: false,
      popHistory: {
        id: null,
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ],
      warningTypeList: [
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        {
          value: 5,
          name: '生产过程监测'
        },
        {
          value: 6,
          name: '趋势校验报警'
        }
      ],
      statusList: [
        {
          value: 0,
          name: '未处理'
        },
        {
          value: 1,
          name: '已挂起'
        },
        {
          value: 2,
          name: '已处理'
        },
        {
          value: 3,
          name: '持续中'
        },
        {
          value: 9,
          name: '审核中'
        }
      ]
    }
  },
  computed: {},
  watch: {
    'searchForm.dateTime': function() {
      if (this.searchForm.dateTime && this.searchForm.dateTime.length) {
        this.searchForm.startTime = this.searchForm.dateTime[0]
        this.searchForm.endTime = this.searchForm.dateTime[1]
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleWarning(data) {
      window.parent.postMessage(
        {
          type: 'link',
          url: data.warningEventBody.alertUrl,
          title: data.warningEventBody.title
        },
        '*'
      )
    },
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        console.log(res)
        this.moduleList = res.data || []
      })
    },

    getRowClass({ row }) {
      if (
        this.$moment().format('YYYY-MM-DD') ===
        this.$moment(row.createDateTime).format('YYYY-MM-DD')
      ) {
        return 'select-row'
      }
    },

    // 历史报警记录
    showHistory(id) {
      this.popHistory.id = id
      this.$refs.modalDetailForm.visible = true
      this.$refs.modalDetailForm.showHistory(id, true)
      this.$refs.modalDetailForm.showHandleHistory(id)
    },
    handlePopSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.popHistory.pageSize = val
      this.showHistory(this.popHistory.id)
    },
    handlePopCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.popHistory.pageIndex = val
      this.showHistory(this.popHistory.id)
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.content .page-operate {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  padding-left: 10px;
  padding-right: 10px;
  .operate-icon {
    margin-left: 8px;
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
/deep/ .select-row {
  background: #ffeff0 !important;
}
/deep/
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped.select-row
  td.el-table__cell {
  background: #ffeff0 !important;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
