export const ENUM = {
  roleStatus: [
    {
      value: 1,
      label: '启用',
      type: 'success'
    },
    {
      value: 0,
      label: '禁用',
      type: 'warning'
    }
  ],
  // 板材所有顶级组织机构 (用于机构数过滤)
  // X32000000,中厚板卷厂,X
  // X38000000,宽厚板厂,X
  // X50000000,板材事业部,X
  // X66000000,中板厂,X
  // X84000000,金石材料厂,X
  // X73000000,一炼钢,X
  orgTop: [
    'X32000000',
    'X38000000',
    'X50000000',
    'X66000000',
    'X84000000',
    'X73000000'
  ],
  moduleList: [
    {
      value: 1,
      label: '数据节点'
    },
    {
      value: 2,
      label: '工厂服务引警'
    },
    {
      value: 3,
      label: '物料跟踪'
    },
    {
      value: 4,
      label: '智慧生产'
    },
    {
      value: 5,
      label: '智慧质量'
    },
    {
      value: 6,
      label: '智慧能源'
    },
    {
      value: 7,
      label: '智慧成本'
    },
    {
      value: 8,
      label: '智慧运维'
    },
    {
      value: 9,
      label: '协同运管'
    },
    {
      value: 10,
      label: '数字工厂'
    },
    {
      value: 11,
      label: '一炼钢设备监测'
    },
    {
      value: 12,
      label: '宽厚板设备监测'
    },
    {
      value: 13,
      label: '宽厚板震动监测'
    }
  ],

  workList: [
    {
      value: 1,
      label: '页面开发'
    },
    {
      value: 2,
      label: '后端开发'
    },
    {
      value: 3,
      label: '数据接入'
    },
    {
      value: 4,
      label: '功能上线'
    },
    {
      value: 5,
      label: '投入运行'
    },
    {
      value: 6,
      label: '交付验收'
    }
  ],

  onlineList: [
    {
      value: 1,
      label: '功能开发'
    },
    {
      value: 2,
      label: '内部测试'
    },
    {
      value: 3,
      label: '外部试用'
    },
    {
      value: 4,
      label: '功能培训'
    },
    {
      value: 5,
      label: '功能上线'
    }
  ]
}

export const DesktopConfig = {
  desktopWallpaper: [
    {
      name: '深蓝',
      theme: 'dark',
      img: require('../assets/desktop/desktop-bg/desktopbg_dark.jpg')
    },
    {
      name: '粒子',
      theme: 'dark',
      img: require('../assets/desktop/desktop-bg/desktopbg_lizi.png')
    },
    {
      name: '蓝紫',
      theme: 'dark',
      img: require('../assets/desktop/desktop-bg/bgcnew.jpg')
    },
    {
      name: '光线',
      theme: 'dark',
      img: require('../assets/desktop/desktop-bg/desktopbg_guangxian.jpg')
    },
    {
      name: '浅色',
      theme: 'light',
      img: require('../assets/desktop/desktop-bg/desktopbg_light.jpg')
    },
    {
      name: '流沙',
      theme: 'light',
      img: require('../assets/desktop/desktop-bg/desktopbg_liusha.jpg')
    }
  ]
}
