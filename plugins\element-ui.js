import Vue from 'vue'
import Element from 'element-ui'
import locale from 'element-ui/lib/locale/lang/zh-CN'
// import locale from 'element-ui/lib/locale/lang/en'
import 'font-awesome/css/font-awesome.css'

Element.Button.props.round = { type: Boolean, default: true }
Element.Dialog.props.closeOnClickModal = { type: Boolean, default: false }
Element.Dialog.props.top = { type: String, default: '5%' }
Element.Select.props.clearable = { type: Boolean, default: true }
export default () => {
  Vue.use(Element, {
    locale,
    size: 'small',
    round: true
  })
}
