<template>
  <div
    id="select-area"
    :style="[
      { width: size.width + 'px' },
      { height: size.height + 'px' },
      { top: Point.y + 'px' },
      { left: Point.x + 'px' },
    ]"
    class="select-area"
  />
</template>

<script>
export default {
  name: 'SelectArea',
  props: {
    startPoint: {
      type: Object,
      required: true,
      default: () => {}
    },
    endPoint: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  computed: {
    Point() {
      let x =
        this.endPoint.x === 0
          ? this.startPoint.x
          : Math.min(this.startPoint.x, this.endPoint.x)
      let y =
        this.endPoint.y === 0
          ? this.startPoint.y
          : Math.min(this.startPoint.y, this.endPoint.y)
      return {
        x,
        y
      }
    },
    size() {
      let width =
        this.endPoint.x === 0
          ? 0
          : Math.abs(this.startPoint.x - this.endPoint.x)
      let height =
        this.endPoint.y === 0
          ? 0
          : Math.abs(this.startPoint.y - this.endPoint.y)
      return {
        width,
        height
      }
    }
  }
}
</script>

<style scoped>
.select-area {
  position: fixed;
  background-color: rgba(255, 192, 203, 0.1);
  border: 1px solid #3eaff6;
  z-index: 9;
}
</style>
