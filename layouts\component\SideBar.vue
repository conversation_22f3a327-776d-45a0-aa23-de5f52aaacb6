<template>
  <client-only>
    <div class="side-wrapper">
      <div class="menu-box">
        <el-menu
          :collapse="menuCollapse"
          :default-active="activeMenu"
          :unique-opened="true"
          class="el-menu-demo"
          background-color="#232253"
          text-color="#f1f1f1"
          active-text-color="#fff"
          @open="handleOpenNav"
          @select="handleChangeNav"
          @close="handleCloseNav">
          <menu-item
            v-for="item in treeData"
            :key="item.id"
            :menu="item"
          />
        </el-menu>
      </div>
      <div
        :title="menuCollapse ? '展开' : '收起'"
        class="collapse-btn"
        @click="handleCollapse">
        <i :class="menuCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"/>
      </div>
    </div>
  </client-only>
</template>

<script>
import menuItem from '@/layouts/component/MenuItem'
import { mapState } from 'vuex'
import { filterUrl, logout } from '@/lib/Menu'
import { findAllRscByUserNo } from '@/api/system'
import { post } from '@/lib/Util'

export default {
  components: {
    menuItem
  },
  data: () => {
    return {
      dialogTitle: '',
      isCollapse: true,
      treeData: [],
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId')
    }
  },
  computed: {
    ...mapState('menu', [
      'menuStore',
      'menuCollapse',
      'currentPageName',
      'isIframeMode',
      'iframeUrl'
    ]),
    ...mapState('desktop', ['miniAppList']),
    activeMenu() {
      const route = this.$route
      const { fullPath } = route
      return filterUrl(fullPath)
    },
    userMenuList() {
      return this.menuStore
        ? this.menuStore.filter(item => item.serviceName === 'res')[0].children
        : []
    }
  },
  created() {
    this.fetchInitialData()

    // 新增：监听权限更新事件
    this.$root.$on('menu-permission-updated', () => {
      console.log('菜单权限已更新，重新获取菜单数据')
      this.fetchInitialData()
    })
    console.log('%c this.$root', 'color: red;', this.$root)
  },
  beforeDestroy() {
    // 在组件销毁前移除事件监听，防止内存泄漏
    this.$root.$off('menu-permission-updated')
  },
  methods: {
    // 修改初始化数据获取方法
    async fetchInitialData() {
      try {
        // 获取当前路径状态
        const isCenter = window.location.href.includes('/center')
        const isVisitPath = new RegExp('^/visit').test(this.currentPageName)
        const isUserSystemPath = this.currentPageName === '/system/user'

        // 根据条件决定数据源
        if (this.currentPageName === '/center' || isCenter) {
          // 中心页面需要从接口获取完整数据
          const params = {
            userNo: localStorage.getItem('userId'),
            customName: '',
            status: ''
          }
          const res = await post(findAllRscByUserNo, params)
          const formatData = this.formatTreeData(res.data)

          this.treeData = formatData
        } else if (isUserSystemPath || isVisitPath) {
          // 用户系统或访问路径使用过滤后的菜单列表
          this.treeData = this.filterUserMenuList()
        } else {
          // 其他情况也使用过滤后的菜单列表
          this.treeData = this.filterUserMenuList()
        }
      } catch (error) {
        console.error('菜单数据获取失败：', error)
      }
    },
    formatTreeData(data) {
      // 常驻菜单的resourceID列表
      const permanentMenuIds = [
        '9c274f7b-d55f-45f7-9ce9-8b6b9d7c34a1', // 首页
        '7ff9e369-a5be-40c5-b7c5-4ab9bd3e1702', // 菜单配置
        'a52f52a9-d63e-4a75-bf38-428cf0abf945', // 报警规则配置
        '96fbe14b-24e2-4dc3-9fae-029fbddbaaff', // 消息推送
        'ad4ca590-414d-4605-ab37-02a4cc34933a' // 消息历史记录
      ]

      // 构建树形结构
      const map = {}
      const roots = []
      const firstLevelNodes = {} // 存储一级节点

      // 初始化映射表 - 使用resourceID作为键
      data.forEach(node => {
        if (node.isShow === 1 && node.type === 'menu') {
          map[node.resourceID] = { ...node, children: [] }
        }
      })

      // 找出所有一级节点
      data.forEach(node => {
        if (node.isShow === 1 && node.type === 'menu') {
          // 常驻菜单单独处理
          if (permanentMenuIds.includes(node.resourceID)) {
            return
          }

          const parentId = node.parentId
          // 如果没有父节点或父节点不存在于数据中，则为一级节点
          if (!parentId || !map[parentId]) {
            firstLevelNodes[node.resourceID] = map[node.resourceID]
          }
        }
      })

      // 将一级节点添加到roots
      Object.values(firstLevelNodes).forEach(node => {
        if (!roots.some(r => r.resourceID === node.resourceID)) {
          roots.push(node)
        }
      })

      // 将常驻菜单添加到roots
      data.forEach(node => {
        if (
          node.isShow === 1 &&
          node.type === 'menu' &&
          permanentMenuIds.includes(node.resourceID)
        ) {
          if (!roots.some(r => r.resourceID === node.resourceID)) {
            roots.push(map[node.resourceID])
          }
        }
      })

      // 查找所有被选中的叶子节点并添加到对应的一级节点下
      data.forEach(node => {
        if (
          node.isShow === 1 &&
          node.type === 'menu' &&
          node.isSelected === '1'
        ) {
          // 跳过一级节点和常驻菜单
          if (
            firstLevelNodes[node.resourceID] ||
            permanentMenuIds.includes(node.resourceID)
          ) {
            return
          }

          // 查找该节点的顶层父节点（一级节点）
          let currentParentId = node.parentId
          let topParentId = null

          // 向上查找，直到找到一级节点
          while (currentParentId) {
            const parentNode = map[currentParentId]
            if (!parentNode) break

            // 如果父节点是一级节点
            if (firstLevelNodes[currentParentId]) {
              topParentId = currentParentId
              break
            }
            currentParentId = parentNode.parentId
          }

          // 如果找到了一级父节点，将此叶子节点直接添加到该一级节点下
          if (topParentId && map[topParentId]) {
            // 避免重复添加
            if (
              !map[topParentId].children.some(
                child => child.resourceID === node.resourceID
              )
            ) {
              map[topParentId].children.push(map[node.resourceID])
            }
          }
        }
      })

      // 排序 - 首页置顶，其余按sort排序
      roots.sort((a, b) => {
        // 首页总是排在最前面
        if (a.name === '首页') return -1
        if (b.name === '首页') return 1

        // 特定菜单处理：消息推送、消息历史记录、菜单配置、报警规则配置等排在最后
        const specialMenus = [
          '消息推送',
          '消息历史记录',
          '菜单配置',
          '报警规则配置'
        ]
        const aIsSpecial = specialMenus.includes(a.name)
        const bIsSpecial = specialMenus.includes(b.name)

        if (aIsSpecial && bIsSpecial) {
          // 特殊菜单之间按sort值排序
          return b.sort - a.sort
        }

        if (aIsSpecial) return 1 // 特殊菜单放最后
        if (bIsSpecial) return -1

        // 其他菜单按sort正序排列
        return a.sort - b.sort
      })

      // 为子节点排序
      const sortChildren = nodes => {
        for (const node of nodes) {
          if (node.children && node.children.length > 0) {
            node.children.sort((a, b) => a.sort - b.sort)
          }
        }
      }

      sortChildren(roots)

      return roots
    },
    handleOpenNav(key, keyPath) {
      // console.log(key, keyPath)
    },
    handleCloseNav(key, keyPath) {
      // console.log(key, keyPath)
    },
    handleChangeNav(key, keyPath) {
      // 获取对应的菜单项数据
      const menuItem = this.findMenuItemByKey(key)
      console.log(
        '%c 菜单项数据',
        'color: blue;',
        JSON.stringify(menuItem, null, 2)
      )

      if (menuItem.port === '9730') {
        // 当是本系统内部菜单时，关闭iframe模式，使用路由跳转
        this.$store.commit('menu/setIframeMode', false)
        this.$store.commit('menu/setIframeUrl', '')
        this.$router.push(key)
      } else {
        // 当是外部系统菜单时，开启iframe模式，设置iframe的url
        const iframeUrl =
          'http://' +
          menuItem.ip +
          ':' +
          menuItem.port +
          menuItem.url +
          '?org=redirect&showHeader=1&token=' +
          this.token +
          '&userId=' +
          this.userId
        this.$store.commit('menu/setIframeMode', true)
        this.$store.commit('menu/setIframeUrl', iframeUrl)
        // 可以选择是否还需要调用redirect方法
        // this.redirect(menuItem, key)
      }
    },
    redirect(menuItem, key) {
      console.log(
        '%c 地址',
        'color: red;',
        'http://' + menuItem.ip + ':' + menuItem.port + key
      )

      window.open(
        'http://' +
          menuItem.ip +
          ':' +
          menuItem.port +
          key +
          '?org=redirect&showHeader=1&token=' +
          this.token +
          '&userId=' +
          this.userId,
        '_blank'
      )
    },
    handleCollapse() {
      this.$store.commit('menu/menuCollapse', !this.menuCollapse)
    },
    // 根据 key 查找菜单项
    findMenuItemByKey(key) {
      // 递归查找函数
      const findItem = (items, targetKey) => {
        for (const item of items) {
          // 如果当前项的 url 与 key 匹配
          if (item.url === targetKey) {
            return item
          }
          // 如果有子项，递归查找
          if (item.children && item.children.length > 0) {
            const found = findItem(item.children, targetKey)
            if (found) return found
          }
        }
        return null
      }

      return findItem(this.treeData, key)
    },
    // 新增：过滤特定ID的菜单项
    filterUserMenuList() {
      const filterIds = [
        '9c274f7b-d55f-45f7-9ce9-8b6b9d7c34a1', // 首页
        '7ff9e369-a5be-40c5-b7c5-4ab9bd3e1702', // 菜单配置
        'a52f52a9-d63e-4a75-bf38-428cf0abf945' // 报警规则配置
      ]

      if (!this.userMenuList || !this.userMenuList.length) {
        return []
      }

      return this.userMenuList.filter(item => !filterIds.includes(item.id))
    }
  }
}
</script>

<style scoped lang="less">
.home {
  width: 220px;
  margin: 0 auto 10px;
  text-align: center;
  background-color: #93a9e1;
  height: 50px;
  line-height: 50px;
  border-radius: 4px;
  color: #ffffff;
  letter-spacing: 3px;
  cursor: pointer;
  .el-icon-s-home {
    font-size: 20px;
  }
}
.el-menu {
  border-right: none;
  //padding-left: 10px;
  &:not(.el-menu--collapse) {
    width: 260px;
  }
  .el-menu-item {
    font-size: 16px;
    letter-spacing: 2px;
  }
}
/deep/ .el-menu-demo {
  > div > .el-menu-item {
    padding-left: 30px !important;
  }
  > div > .el-submenu > .el-submenu__title {
    padding-left: 30px !important;
  }
  > div > .el-submenu > .el-menu > div > .el-submenu > .el-submenu__title {
    padding-left: 40px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-menu-item,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-submenu__title {
    padding-left: 48px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-menu-item {
    padding-left: 60px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-submenu__title {
    padding-left: 60px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-menu
    > div
    > .el-menu-item,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-submenu__title {
    padding-left: 94px !important;
  }
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu
    > .el-menu
    > div
    img {
    display: none;
  }

  > div > .el-submenu > .el-menu > div > .el-submenu.is-opened > .el-menu,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu.is-opened
    .el-menu
    .el-submenu__title,
  > div
    > .el-submenu
    > .el-menu
    > div
    > .el-submenu.is-opened
    .el-menu
    .el-menu-item {
    background-color: #171636 !important;
  }
  &.el-menu--collapse {
    > div > .el-menu-item {
      padding-left: 20px !important;
    }
    > div > .el-submenu > .el-submenu__title {
      padding-left: 20px !important;
    }
  }
}

/deep/.el-menu .el-menu-item {
  transition: border ease-in-out 0.3s;
  background: transparent !important;
  &:hover:before {
    content: '';
    position: absolute;
    top: 8px;
    bottom: 8px;
    left: 16px;
    right: 16px;
    border-radius: 8px;
    background-color: #38386c;
    z-index: 1;
  }
  span {
    position: relative;
    z-index: 999;
  }
}

/deep/ .el-menu .el-menu-item.is-active {
  color: #f2f2f2;
  &:before {
    content: '';
    position: absolute;
    top: 8px;
    bottom: 8px;
    left: 16px;
    right: 16px;
    border-radius: 8px;
    background-color: #4458fe;
    z-index: 1;
  }
}
/deep/ .el-menu-item.is-active {
  background-color: transparent !important;
}
/deep/ .el-submenu__title {
}
/deep/ .el-menu--collapse .el-menu-item.is-active:before {
  display: none;
}
/deep/
  .el-menu--collapse
  .el-submenu
  .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}
/deep/ .el-menu--collapse .el-menu-item span,
/deep/ .el-menu--collapse .el-submenu .el-submenu__title span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}
/deep/ .el-menu--popup .el-submenu__title {
  background-color: transparent !important;
}
/deep/ .is-opened > .el-submenu__title span {
  position: relative;
}
/deep/ .is-opened > .el-submenu__title span:before {
  content: '';
  position: absolute;
  border-radius: 50%;
  background-color: #4458fe;
  width: 8px;
  height: 8px;
  left: -50px;
  top: 50%;
  margin-top: -4px;
}
.side-wrapper {
  height: 100%;
  position: relative;
  background: #232253;
  .collapse-btn {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    font-size: 22px;
    padding-left: 20px;
    background: #232253;
    color: #fff;
    cursor: pointer;
  }
  .menu-box {
    height: calc(100% - 56px);
    overflow: auto;
  }
}
.menu-box {
  /*滚动条样式 chrome内核*/
  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
  &::-webkit-scrollbar {
    width: 6px; /*对垂直流动条有效*/
    height: 6px; /*对水平流动条有效*/
  }
  /*定义滚动条的轨道颜色、内阴影及圆角*/
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
  }
  /*定义滑块颜色、内阴影及圆角*/
  &::-webkit-scrollbar-thumb {
    border-radius: 7px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.15);
    background: #c1cbdb;
  }
  /*定义两端按钮的样式*/
  &::-webkit-scrollbar-button {
    display: none;
  }
  /*定义右下角汇合处的样式*/
  &::-webkit-scrollbar-corner {
    display: none;
  }
}
</style>
