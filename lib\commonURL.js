/*
 * @Author: prefix +  <PERSON><PERSON><PERSON><PERSON>
 * @Date: prefix +  2022-07-13 16: prefix + 13: prefix + 20
 * @LastEditTime: 2022-08-15 17:48:34
 * @LastEditors: GUoZhipeng
 * @Description: prefix +
 * @LastEditTime: 2022-08-18 10:40:44
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @Description: prefix +
 * @FilePath: \iet-idm-web\lib\commonURL.js
 * 可以输入预定的版权声明、个性签名、空行等
 */
let prefix = 'idm/'
let prefixRes = 'res/'
export default {
  // 数据字典：根据条件查询
  findByCondition: prefix + 'mat/findByCondition.idm',
  // 数据监控：获得tree结构
  findGroups: prefix + 'group/findGroups.idm',
  //主题配置 ：修改分组顺序
  updateOrderNo: prefix + 'group/updateOrderNo',
  // 数据监控：查询明细
  findGroupId: prefix + 'dataDict/findGroupId.idm',
  // 数据监控：查看位置曲线
  querySpaceCurveByTime: prefix + 'api/curve/querySpaceCurveByTime.idm',
  // 数据监控：查看时间曲线
  queryTimeCurve: prefix + 'api/curve/queryTimeCurve.idm',
  // 数据监控：搜索变量groupId
  findDataDictAllGroupId: prefix + 'dataDict/findDataDictAllGroupId.idm',
  // 数据监控：搜索变量
  findDataDictAll: prefix + 'dataDict/findDataDictAll.idm',
  // 数据补录：特征值正在补录
  findBeingExecuted:
    prefix + 'dataSupplement/eigenvalues/findBeingExecuted.idm',
  // 数据补录：特征值查询
  findAll: prefix + 'dataSupplement/DataSupplementHistory/findAll.idm',
  // 数据补录：变量正在补录
  variable: prefix + 'dataSupplement/variable/findBeingExecuted.idm',
  // 数据补录：输入框查询钢板号
  findByDischargeTime: prefix + 'subModelDt/findByDischargeTime.idm',
  // 数据补录：变量补录执行事件
  saveDataSupplement: prefix + 'dataSupplement/saveDataSupplement.idm',
  // 数据补录：选择分组
  findByGroupId: prefix + 'ruleEngine/findByGroupId.idm',
  // 数据导出：导入数据
  excelGeneration: prefix + 'exportdata/exportdata',
  // 数据字典：文件上传
  uploadFile: prefix + 'api/system/uploadFile.idm',
  // 数据字典：树组件删除
  deleteById: prefix + 'group/deleteById.idm',
  // 数据字典：查询明细
  findGroupIdEn: prefix + 'dataDict/findGroupIdEn.idm',
  // 数据字典：启用
  DataDictfindByConditions: prefix + 'dataDict/findByCondition.idm',
  // 数据字典：查询位置信息
  findPositionCurveByVarName: prefix + 'subject/findPositionCurveByVarName.idm',
  // 数据字典：查询变量数据
  queryTimeCurveVariablesByName:
    prefix + 'api/transpose/queryTimeCurveVariablesByName.idm',
  // 数据字典：查询单点数据
  findSingleVariableValue: prefix + 'api/findSingleVariableValue.idm',
  // 数据字典：删除数据
  delDataDict: prefix + 'dataDict/delDataDict.idm',
  // 数据字典：查询重复变量名
  checkVarNameIsRepeat: prefix + 'dataDict/checkVarNameIsRepeat.idm',
  // 数据字典：提交添加内容
  saveDataDict: prefix + 'dataDict/saveDataDict.idm',
  // 数据字典：位置曲线配置
  savePositionCurveToVarName: prefix + 'subject/savePositionCurveToVarName.idm',
  // 规则引擎管理：添加/编辑弹窗
  saveGroup: prefix + 'group/saveGroup.idm',
  // 规则引擎管理：查询规则引擎日志
  findByRuleId: prefix + 'ruleLogs/findByRuleId.idm',
  // 规则引擎管理：搜索规则引擎
  ruleEngineFindByCondition: prefix + 'ruleEngine/findByCondition.idm',
  // 规则引擎管理：删除规则引擎
  delById: prefix + 'ruleEngine/delById.idm',
  // 特征值计算：查询所有规则引擎日志
  ruleLogs: prefix + 'ruleLogs/findAll.idm',
  // 特征值计算：按照物料号查询规则引擎日志
  findByCoilId: prefix + 'ruleLogs/findByCoilId.idm',
  // 特征值计算：查询规则引擎日志
  ruleLogsFindByCondition: prefix + 'ruleLogs/findByCondition.idm',
  //主题信息：根据主题名称查询主题
  ThemefindByCondition: prefix + 'theme/findByCondition.idm',
  // 主题配置：查询明细
  themeFindGroupId: prefix + 'theme/findGroupId.idm',
  // 主题配置：查询全部
  findByContent: prefix + 'theme/findByContent.idm',
  // 主题配置：查看详情
  findTreeBySubId: prefix + 'theme/findTreeBySubId.idm',
  // 主题配置：删除
  delSubjectProp: prefix + 'theme/delSubjectProp.idm',
  // 主题配置：属性配置点击事件
  findDictByPropId: prefix + 'propConf/findDictByPropId.idm',
  // 主题配置：查寻所有的属性集合
  findProp: prefix + 'propConf/findProp.idm',
  // 主题配置：添加属性集合弹窗
  saveProp: prefix + 'propConf/saveProp.idm',
  // 主题配置：删除
  delProp: prefix + 'propConf/delProp.idm',
  // 主题配置：用户
  findOneUserByUserNo: prefixRes + 'user/findOneUserByUserNo',
  // 主题配置：查寻所有的属性集合
  findBySubjectId: prefix + 'propConf/findBySubjectId.idm',
  // 主题配置：移除变量
  saveDictProp: prefix + 'propConf/saveDictProp.idm',
  // 主题配置：提交本次编辑
  saveSubjectProp: prefix + 'theme/saveSubjectProp.idm',
  // 主题查询：获取数据
  findByFormula: prefix + 'ruleEngine/findByFormula.idm',
  // 主题查询：主题切换下拉框并获取所有变量
  findSubjectData: prefix + 'api/findSubjectData.idm',
  // 主题查询：测试规则
  ruleAnalyzerCheck: prefix + 'ruleEngine/ruleAnalyzerCheck.idm',
  // Loading页面：index
  findRuleEngineById: prefix + 'ruleEngine/findRuleEngineById.idm',
  // 统计管理：查询变量名
  dataDictFindByCondition: prefix + 'dataDict/findByCondition.idm',
  // 获取用户角色下所有菜单
  findAllResourceNoPage: prefixRes + '/resource/findAllResourceNoPage',
  // 时间主题配置：查寻所有的属性集合
  findBySubjectTimeId: prefix + 'api/findSubjectDataByTime.idm',
  // 查询时间主题集合
  findByConditionUrl: prefix + 'theme/findByCondition.idm',
  // 根据别名查变量
  findDictionaryByName: prefix + 'dataDict/findDictionaryByName.idm',

  findByGroupIdEn: prefix + 'dataDict/findByGroupIdEn.idm',
  // KPI管理：删除kpi配置
  deleteRecal: prefix + '/api/statistics/kpiConfig/delete.idm',
  // KPI管理： 查询kpi配置
  findAllByPageRecal: prefix + '/api/statistics/kpiConfig/findAllByPage.idm',
  // KPI管理： 添加kpi配置
  saveRecal: prefix + '/api/statistics/kpiConfig/save.idm',
  //删除redis缓存
  deleteRedisData: prefix + '/api/deleteRedisData.idm',
  //通过物料号查询相关信息
  findMaterialPedigreeList: prefix + '/mat/findMaterialPedigreeList.idm',
  // //搜索规则引擎
  // findByConditions: prefix + 'ruleEngine/findByCondition.idm'
  findAllNodePath: prefix + '/dataDict/findAllBySubjectNodePath.idm',
  //物料画像首页生产工序
  ProductionProcess: prefix + 'api/findProcess.idm',
  qualityPolice: 'aDash/queryservice/queryMaMonitorLog',
  findPRCode: prefix + '/api/findPRCode.idm',
  queryPropDataByTopic: prefix + '/api/queryPropDataByTopic.idm',

  findHeatBySetDate: 'mesAPI/HeatTreatmentYield/findHeatBySetDate',
  findSteelOutputBySetDateTotal:
    'mesAPI/steelOutput/findSteelOutputBySetDateTotal',
  findSteelOutputBySetDate: 'mesAPI/steelOutput/findSteelOutputBySetDate',
  actualSuccessRate: 'mesAPI/Target/actualSuccessRate/find',
  // 钢板成本
  rolling: 'ifc/middleground/c2/rolling/findByMatId',
  finishing: 'ifc/middleground/c2/finishing/findByMatId',
  heatTreatment: 'ifc/middleground/c2/heatTreatment/findByMatId',
  rollingCategory: 'ifc/middleground/c2/rollingCategory/findByMatId',
  // 钢板能源
  plateUsingPOST: 'plate/findByMatId',
  plateUsingPOST2: 'fs/comprehensive/findByMatId',
  // 钢水成本
  MoltZL: 'ifc/middleground/b1/zl/findByMatId',
  MoltLF: 'ifc/middleground/b1/lf/findByMatId',
  MoltRH: 'ifc/middleground/b1/rh/findByMatId',
  Molttotal: 'ifc/middleground/b1/total/findByMatId',
  //钢坯成本
  BillLZ: 'ifc/middleground/b1/lz/findByMatId',
  Billtotal: 'ifc/CaContinuousCastingPro/queryAllSlab'
}
// this.commonUrl.findMaterialPedigreeList
