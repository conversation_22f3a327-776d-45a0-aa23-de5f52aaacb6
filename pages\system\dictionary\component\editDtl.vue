<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '字典值'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入编码',
              trigger: 'change'
            }
          ]"
          label="编码"
          prop="code"
        >
          <el-input
            v-model="formData.code"
            placeholder="请输入编码" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入值',
              trigger: 'change'
            }
          ]"
          label="值"
          prop="value"
        >
          <el-input
            v-model="formData.value"
            placeholder="请输入值">
            <el-button
              slot="append"
              icon="el-icon-user"
              @click="changeUser()"/>
          </el-input>

        </el-form-item>
        <el-form-item
          v-if="code === 'modelNo'"
          :rules="[
            {
              required: true,
              message: '请选择',
              trigger: 'change'
            }
          ]"
          label="所属模块"
          prop="value1"
        >
          <el-select
            v-model="formData.value1"
            size="small"
            clearable
            filterable
            placeholder="选择模块"
          >
            <el-option
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
    <DistributeUser
      v-if="visibleDistribute"
      :role="editRole"
      :visible.sync="visibleDistribute"
      @getInputStr="getInputStrFn"
    />
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  dictionaryDtlFindByDictCode,
  dictionaryDtlSave,
  matterSave,
  saveFeedback,
  uploadFile,
  weeklySummarySave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'
import DistributeUser from './distributeUser.vue'

export default {
  components: { DistributeUser, ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: ''
    },
    code: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visibleDistribute: false,
      loading: false,
      visible: false,
      url: {
        edit: dictionaryDtlSave,
        add: dictionaryDtlSave,
        file: uploadFile,
        getDict: dictionaryDtlFindByDictCode
      },
      moduleList: [],
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
    post(this.url.getDict, {
      dictCode: 'serviceByEval'
    }).then(res => {
      this.moduleList = res.data.map(item => {
        return {
          value: item.code,
          label: item.value
        }
      })
    })
  },
  methods: {
    getInputStrFn(params) {
      this.formData.value = params
    },
    // 选择角色
    changeUser(data) {
      this.editRole = data
      this.visibleDistribute = !this.visibleDistribute
    },
    weekChange() {
      this.$nextTick(() => {
        this.formData.weekDate = this.$moment(this.formData.weekDate)
          .startOf('isoWeek')
          .add(4, 'days')
          .format('yyyy-MM-DD')
      })
    },
    httpRequest(params) {},
    submitBefore() {
      this.formData.dict = {
        id: this.id
      }
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
