<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'应用访问统计表'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="94%"
      @close="clearForm"
      @open="tabActive = 'first';onOpen"
      v-on="$listeners"
    >
      <div
        class="page-content"
        style="position: relative">
        <div
          style="position: absolute; right: 0; top: 0;z-index: 999">
          <el-form
            ref="form"
            :model="searchForm"
            inline
          >
            <el-form-item
              label="日期："
              prop="serviceNo"
            >
              <el-select
                v-model="selectedDate"
                size="small"
                multiple
                collapse-tags
                clearable
                filterable
                placeholder="选择状态"
                style="width: 184px"
              >
                <el-option
                  v-for="date in datePick"
                  :key="date"
                  :label="date"
                  :value="date"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :label-width="'0'"
            >
              <el-button
                type="primary"
                @click="getData">查询
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-tabs v-model="tabActive">
          <el-tab-pane
            label="人均访问趋势排名"
            name="first">
            <el-row :gutter="24">
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'first' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部处室人均访问排名趋势'"
                    :x-data="dataOption1.dataX"
                    :chart-data="dataOption1.data"
                  />
                </div>
              </el-col>
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'first' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部分厂人均访问排名趋势'"
                    :x-data="dataOption2.dataX"
                    :chart-data="dataOption2.data"
                  />
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane
            label="C层人均访问趋势排名"
            name="second">
            <el-row :gutter="24">
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'second' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部处室C层人均访问排名趋势'"
                    :x-data="dataOption3.dataX"
                    :chart-data="dataOption3.data"
                  />
                </div>
              </el-col>
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'second' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部分厂C层人均访问排名趋势'"
                    :x-data="dataOption4.dataX"
                    :chart-data="dataOption4.data"
                  />
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane
            label="E层人均访问趋势排名"
            name="third">
            <el-row :gutter="24">
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'third' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部处室E层人均访问排名趋势'"
                    :x-data="dataOption5.dataX"
                    :chart-data="dataOption5.data"
                  />
                </div>
              </el-col>
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'third' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部分厂E层人均访问排名趋势'"
                    :x-data="dataOption6.dataX"
                    :chart-data="dataOption6.data"
                  />
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane
            label="问题反馈次数趋势"
            name="fourth">
            <el-row :gutter="24">
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'fourth' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部处室问题反馈次数趋势'"
                    :x-data="dataOption7.dataX"
                    :chart-data="dataOption7.data"
                  />
                </div>
              </el-col>
              <el-col
                v-loading="loading"
                :span="12">
                <div
                  v-if="tabActive === 'fourth' && visible"
                  style="height: 400px">
                  <line-chart
                    :chart-title="'事业部分厂问题反馈次数趋势'"
                    :x-data="dataOption8.dataX"
                    :chart-data="dataOption8.data"
                  />
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { getCountInfoByDay4DSB } from '@/api/system'
import { post } from '@/lib/Util'
import PieChart from '@/components/chart/pie-chart'
import moment from 'moment'
import LineChart from '@/components/chart/line-chart.vue'

export default {
  components: { LineChart, PieChart },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data: () => {
    return {
      loading: true,
      selectedDate: [],
      datePick: {},
      dataOption1: {
        dataX: [],
        data: []
      },
      dataOption2: {
        dataX: [],
        data: []
      },
      dataOption3: {
        dataX: [],
        data: []
      },
      dataOption4: {
        dataX: [],
        data: []
      },
      dataOption5: {
        dataX: [],
        data: []
      },
      dataOption6: {
        dataX: [],
        data: []
      },
      dataOption7: {
        dataX: [],
        data: []
      },
      dataOption8: {
        dataX: [],
        data: []
      },
      visible: false,
      searchForm: {},
      handleObj: {
        chart: []
      },
      tabActive: 'first',
      popHistory: {
        id: null,
        warningRuleID: null,
        list: [],
        handleList: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date()).format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '昨日',
            onClick(picker) {
              const end = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 23:59:59')
              )
              const start = new Date(
                moment(new Date())
                  .add(-1, 'days')
                  .format('YYYY-MM-DD 00:00:00')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('isoWeek')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date(
                moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
              )
              const start = new Date(
                moment(new Date())
                  .startOf('month')
                  .format('YYYY-MM-DD HH:mm:ss')
              )
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {},
  async created() {
    /*this.searchForm.dateTime = [
      this.$moment()
        .subtract(7, 'days')
        .format('YYYY-MM-DD'),
      this.$moment().format('YYYY-MM-DD')
    ]*/
  },
  destroyed() {},
  mounted() {
    this.generateMonthDates()
    this.generateweekDates()
  },
  methods: {
    //获取一个月内的日期
    generateMonthDates() {
      const currentDate = moment() // 当天
      // 创建一个空数组来存储日期
      let datesArray = []
      for (
        let date = currentDate.clone().subtract(1, 'months');
        date.isSameOrBefore(currentDate);
        date.add(1, 'days')
      ) {
        // 使用format方法将Moment对象转换为字符串，或者你可以直接将Moment对象推入数组
        datesArray.push(date.format('YYYY-MM-DD')) // 'YYYY-MM-DD'格式的日期
      }
      this.datePick = datesArray
    },
    //获取一周内的日期
    generateweekDates() {
      const currentDate = moment() // 当天
      for (
        let date = currentDate.clone().subtract(1, 'week');
        date.isSameOrBefore(currentDate);
        date.add(1, 'days')
      ) {
        // 使用format方法将Moment对象转换为字符串，或者你可以直接将Moment对象推入数组
        this.selectedDate.push(date.format('YYYY-MM-DD')) // 'YYYY-MM-DD'格式的日期
      }
    },
    getData() {
      this.loading = true
      post(getCountInfoByDay4DSB, {
        timeList: this.selectedDate
      }).then(res => {
        this.loading = false
        this.dataOption1 = res.data['处室人均排名'].dataOption
        this.dataOption2 = res.data['厂级人均排名'].dataOption
        this.dataOption3 = res.data['处室C层排名'].dataOption
        this.dataOption4 = res.data['厂级C层排名'].dataOption
        this.dataOption5 = res.data['处室E层排名'].dataOption
        this.dataOption6 = res.data['厂级E层排名'].dataOption
        this.dataOption7 = res.data['处室反馈排名'].dataOption
        this.dataOption8 = res.data['厂级反馈排名'].dataOption
      })
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;

  &:hover {
    background: #eee;
    color: #fff;
  }
}
</style>
