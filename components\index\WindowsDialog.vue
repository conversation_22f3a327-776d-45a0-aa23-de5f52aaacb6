<template>
  <div>
    <el-dialog
      v-for="(item, index) in miniAppList"
      :key="index"
      :top="'1%'"
      :visible.sync="item.visible"
      :close-on-click-modal="false"
      :destroy-on-close="false"
      :width="'98%'"
      :show-close="false"
      lock-scroll
      class="hmi-dialog"
      @close="miniFn">
      <div
        slot="title"
        class="header">
        <div class="title">
          <icon-png :icon-name="item.deskIcon"/>
          {{ item.name }}
        </div>
        <div class="oper">
          <el-popover
            :width="230"
            :popper-class="'video-pop'"
            :popper-options="{popperAppendToBody: false}"
            placement="bottom"
            trigger="hover">
            <span
              slot="reference">
              <i
                class="el-icon-reading"
              />
            </span>
            <ul
              v-if="matchVideos(item, 'pdfList').length"
              class="contextmenu">
              <li
                v-for="(video) in matchVideos(item, 'pdfList')"
                :key="video.id"
                @click="playPdf(video)">
                <i
                  class="el-icon-reading"
                />
                <span>{{ video.name.split('.')[0] }}</span>
              </li>
            </ul>
            <p
              v-else
              class="no-data">
              暂无手册
            </p>
          </el-popover>
          <el-popover
            :width="230"
            :popper-class="'video-pop'"
            :popper-options="{popperAppendToBody: false}"
            placement="bottom"
            trigger="hover">
            <span
              slot="reference">
              <i
                class="el-icon-video-camera"
              />
            </span>
            <ul
              v-if="matchVideos(item, 'videoList').length"
              class="contextmenu">
              <li
                v-for="(video) in matchVideos(item, 'videoList')"
                :key="video.id"
                @click="playVideo(video)">
                <i
                  class="el-icon-video-camera-solid"
                />
                <span>{{ video.name.split('.')[0] }}</span>
              </li>
            </ul>
            <p
              v-else
              class="no-data">
              暂无视频
            </p>
          </el-popover>
          <el-tooltip
            class="item"
            effect="dark"
            content="最小化"
            placement="top">
            <i
              class="el-icon-minus"
              @click="miniFn()"
            />
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="全屏"
            placement="top">
            <i
              class="el-icon-full-screen"
              @click="fullscreen(index)"
            />
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="关闭"
            placement="top">
            <i
              class="el-icon-close"
              @click="closeFn(item)"
            />
          </el-tooltip>
        </div>
      </div>
      <div
        class="iframe-wrapper">
        <iframe
          :id="'iframe' + index"
          :src="item.url + '&userId=' + userId + '&token=' + token + '&showHeader=0'"
          :name="'iframe' + index"
          allowfullscreen
          width="100%"
          height="100%"
          frameborder="0"
          scrolling="auto" />
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      v-if="popVideoVisible"
      :top="'3%'"
      :title="popVideoId.name.split('.')[0]"
      :width="'1200px'"
      :visible.sync="popVideoVisible"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >
      <div class="video-box">
        <video
          :src="showSrc"
          autoplay
          controls/>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      v-if="popPdfVisible"
      :top="'3%'"
      :title="popPdfId.name.split('.')[0]"
      :width="'1200px'"
      :visible.sync="popPdfVisible"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >
      <div style="background-color: #fff">
        <pdf-view :id="popPdfId.id"/>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import IconPng from '@/components/IconPng'
import { mapState } from 'vuex'
import { post } from '@/lib/Util'
import {
  attachFindAllByMultiCondition,
  downloadFileById,
  findBasicDataConfigByType
} from '@/api/system'
import { getTokenStatus, logout } from '@/lib/Menu'
import * as jwt from 'jsonwebtoken'
import { imgIp } from '@/config'
import PdfView from '~/components/pdfView'
export default {
  name: 'WindowsDialog',
  components: { PdfView, IconPng },
  data() {
    return {
      popVideoVisible: false,
      popVideoId: {},
      popPdfVisible: false,
      popPdfId: {},
      popVideoServiceName: '',
      serviceList: [],
      videoList: [],
      pdfList: [],
      maxNum: 12,
      thirdService: ['tpp'], //需要新窗口打开的应用
      blankService: ['ikb'], //需要新窗口打开的应用
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId')
    }
  },
  computed: {
    showSrc: function() {
      return imgIp + downloadFileById + this.popVideoId.id
    },
    ...mapState('desktop', ['miniAppList'])
  },
  destroyed() {
    this.$bus.$off('open-iframe')
    this.$bus.$off('close-iframe')
  },
  created() {
    this.getVideoData()
    //
    this.$bus.$on('open-iframe', (data, target = '_self') => {
      // 判断token登录状态
      if (getTokenStatus()) {
        // 记录子系统访问记录
        // this.handleAppLog(data)
        // data 菜单对象
        // 接收data  需包含 url icon name
        // 类型为custom为去处理等页面打开直接使用url
        if (this.thirdService.indexOf(data.serviceName) !== -1) {
          // 第三方系统 新窗口打开
          return window.open(data.url)
        }
        let url =
          data.type === 'custom'
            ? data.url
            : `http://${data.ip}:${data.port}${data.url}`
        if (this.blankService.indexOf(data.serviceName) !== -1) {
          // 新窗口打开
          return window.open(url)
        }
        // 判断url是否包含?符号
        const hasParams = data.url.includes('?')
        // 组建url
        url = url + `${hasParams ? '&' : '?'}org=redirect`
        if (target === '_blank') {
          // 新窗口打开
          return window.open(
            url + '&showHeader=1&token=' + this.token + '&userId=' + this.userId
          )
        }
        this.openIframe({
          visible: true,
          name: data.name || '自定义页面',
          deskIcon: data.deskIcon || data.icon || '',
          icon: data.icon || '',
          url,
          origin: data
        })
      } else {
        // token过期
        this.$message.warning('登录状态已过期，请重新登录')
        logout(this)
      }
    })
    this.$bus.$on('close-iframe', () => {
      // 最小化
      this.miniFn()
    })
  },
  methods: {
    // 打开iframe
    openIframe(data) {
      const iframes = this.miniAppList
      const index = iframes.findIndex(item => item.url === data.url)
      if (index !== -1) {
        iframes[index].visible = true
      } else {
        if (iframes.length >= this.maxNum) iframes.splice(0, 1)
        iframes.push(data)
      }
      this.$store.commit('desktop/miniAppList', iframes)
    },
    // 最小化
    miniFn() {
      const iframes = this.miniAppList
      iframes.forEach(item => (item.visible = false))
      this.$store.commit('desktop/miniAppList', iframes)
    },
    // 关闭
    closeFn(im) {
      const iframes = this.miniAppList
      //修改成通过名称判断,原先通过visible ==true判断 出现多层弹窗后失效
      const index = iframes.findIndex(item => item.name === im.name)
      iframes.splice(index, 1)
      this.$store.commit('desktop/miniAppList', iframes)
    },
    // 关闭
    fullscreen(index) {
      const iframeDom = document.getElementById('iframe' + index)
      if (iframeDom.webkitRequestFullScreen) {
        iframeDom.webkitRequestFullScreen()
      }
      // Firefox (works in nightly)
      else if (element.mozRequestFullScreen) {
        iframeDom.mozRequestFullScreen()
      }
    },
    // 记录用户访问记录
    handleAppLog(data) {
      // if (!data.serviceName) return
      // post(appLog, { serviceNo: data.serviceName, userNo: this.userId }).then(
      //   res => {
      //     if (!res.success) console.warn(res)
      //   }
      // )
    },
    async getVideoData() {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content

      // 获取视频列表
      post(attachFindAllByMultiCondition, {
        relatedId: '49d9a2cc-9b4a-4f7c-987a-4e3271c46774'
      }).then(res => {
        this.videoList = res.data
      })
      // 获pdf列表
      post(attachFindAllByMultiCondition, {
        relatedId: '49d9a2cc-9b4a-4f7c-987a-4e3271c46884'
      }).then(res => {
        this.pdfList = res.data
      })
    },
    matchVideos(menu, list) {
      return this[list].filter(
        item => menu.origin.serviceName === item.serviceNo
      )
    },
    playVideo(item) {
      this.popVideoId = item
      this.popVideoVisible = true
    },
    playPdf(item) {
      this.popPdfId = item
      this.popPdfVisible = true
    }
  }
}
</script>
<style scoped lang="less">
.hmi-dialog {
  /deep/ .el-dialog {
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
    background: transparent;
  }
  /deep/ .el-dialog__header {
    padding: 0;
  }
  /deep/ .el-dialog__body {
    height: 94vh;
    background-color: #cbd0e3;
    padding: 15px;
  }
  .header {
    padding: 4px 12px;
    height: 36px;
    background-color: #13356f;
    color: #fff;
    font-size: 18px;
    letter-spacing: 1px;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    > div {
    }
    .oper {
      > i {
        margin-right: 4px;
        cursor: pointer;
      }
    }
  }
}
/deep/ .video-dialog {
  .el-dialog {
    position: relative;
    border: 1px solid #0c4d63;
    background: #041a21;
    box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.12),
      0px 4px 8px rgba(0, 0, 0, 0.08), 0px 4px 16px 4px rgba(0, 0, 0, 0.04);
    .el-dialog__headerbtn {
      top: -19px;
      right: -19px;
      width: 36px;
      height: 36px;
      background: #0b3f67;
      text-align: center;
      line-height: 20px;
      /* 亮色 */
      border: 1px solid #1fc6ff;
      box-shadow: inset 0px 0px 10px #0e9cff;
      border-radius: 6px;
      transform: rotate(-45deg);

      .el-icon {
        transform: rotate(-45deg);
        font-size: 26px;
        color: #fff;
      }

      &:hover {
        box-shadow: inset 0px 0px 15px #54e1fa;
      }
    }
    .el-dialog__title {
      color: #bcd9f6;
    }
    .el-dialog__header {
      padding: 24px;
    }

    .el-dialog__body {
      padding-top: 0;
    }
  }
}

.video-box {
  position: relative;
  video {
    width: 100%;
  }
}
.iframe-wrapper {
  height: 100%;
}
.no-data {
  line-height: 36px;
  color: #fff;
  text-align: center;
}
.contextmenu {
  min-width: 150px;
  font-size: 14px;
  line-height: 2;
  color: #f0fbff;
  border-color: rgba(47, 133, 231, 0.59);
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    position: relative;
    padding: 5px 16px;
    cursor: pointer;
    border-bottom: 1px solid #4168a9;
    text-align: left;
    &:hover {
      background: #1f488d;
    }
    i {
      margin-right: 5px;
    }
    .close-btn {
      position: absolute;
      right: 2px;
      top: 10px;
      font-size: 16px;
      &:hover {
        color: #1087da;
      }
    }
  }
  li:last-child {
    border-bottom: none;
  }
}
</style>

<style lang="less">
.video-pop {
  background-color: #13356f;
  border-color: rgba(35, 120, 218, 0.83);
}
.video-pop[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #13356f;
}
.video-pop[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #13356f;
}
</style>
