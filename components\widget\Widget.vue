<template>
  <div
    ref="widget"
    :style="position"
    :class="{'move': isMove}"
    class="widget">
    <div
      class="widget-header"
      @mousedown.stop="mousedown">
      {{ title }}
      <div
        class="widget-operate"
        @mousedown.stop="defaultAction">
        <el-tooltip
          :content="isFullscreen? '还原' : '最大化'"
          placement="bottom">
          <el-icon
            class="widget-header-icon el-icon-full-screen"
            @click.native.stop="fullscreen" />
        </el-tooltip>
        <el-icon
          class="widget-header-icon el-icon-setting"
          @click.native.stop="fullscreen" />
        <el-dropdown
          @command="handleDrop">
          <el-icon
            class="widget-header-icon el-icon-more-outline"/>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="remove">移除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

      </div>
    </div>
    <div
      :class="{'dragging': $store.state.desktop.widgetDragging}"
      class="widget-content">
      <div
        class="widget-content-inner"
      >
        <iframe
          :id="'iframe'"
          :src="url"
          allowfullscreen
          width="100%"
          height="100%"
          frameborder="0"
          scrolling="auto" />
      </div>
      <div
        class="drag-right-bottom"
        @mousedown="mousedownRightBottom"/>
    </div>
    <div class="widget-footer"/>
  </div>
</template>

<script>
import { offSet } from '@/lib/Util'

export default {
  name: 'Widget',
  // eslint-disable-next-line vue/require-prop-types
  props: ['title', 'widgetData'],
  data() {
    return {
      size: {
        x: this.widgetData.x || 0, //left:x 百分比
        y: this.widgetData.y || 0, //top:y  像素
        width: this.widgetData.width || 100, //width:width 百分比
        height: this.widgetData.height || 400 //height:height  像素
      },
      leftOffset: 0, //鼠标距离移动窗口左侧偏移量
      topOffset: 35, //鼠标距离移动窗口顶部偏移量
      blankService: ['ikb'], //无需token的页面
      isMove: false, //是否移动标识
      isFullscreen: false, //是否最大化
      widgetRef: null,
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId')
    }
  },
  computed: {
    //top与left加上px
    position() {
      return this.isFullscreen
        ? `width:100%;height:100%;top:0;left:0;`
        : `width:${this.size.width}%;height:${this.size.height}px;top:${
            this.size.y
          }px;left:${this.size.x}%;`
    },
    url() {
      const data = this.widgetData
      if (!this.widgetData) return ''
      if (this.blankService.indexOf(data.serviceName) !== -1) {
        // 新窗口打开
        return `http://${data.ip}:${data.port}${data.url}`
      } else {
        const hasParams = data.url.includes('?')
        return `http://${data.ip}:${data.port}${data.url}${
          hasParams ? '&' : '?'
        }org=redirect&token=${this.token}&userId=${this.userId}`
      }
    }
  },
  mounted() {
    this.widgetRef = this.$refs.widget
  },
  methods: {
    fullscreen() {
      console.log(this.isFullscreen)
      this.isFullscreen = !this.isFullscreen
    },
    mousedown(event) {
      //鼠标按下事件
      console.log('鼠标按下事件')
      if (this.isFullscreen) return
      this.leftOffset = event.offsetX
      this.topOffset = event.offsetY
      this.isMove = true
      this.$store.commit('desktop/widgetDragging', true)
      document.onmousemove = this.mousemove
      document.onmouseup = () => {
        document.onmousemove = null
        document.onmouseup = null
        this.isMove = false
        this.$store.commit('desktop/widgetDragging', false)
        this.$emit('on-submit', Object.assign(this.widgetData, this.size))
      }
    },
    //鼠标移动
    mousemove(event) {
      if (!this.isMove) return
      let w =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth
      let h =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight
      let leftx = (event.clientX - this.leftOffset) / w
      let topy = event.clientY - this.topOffset - 35
      this.size.x = (leftx <= 0 ? 0 : leftx) * 100
      this.size.y = topy <= 0 ? 0 : topy
    },
    //鼠标抬起
    mousedownRightBottom(e) {
      if (this.isFullscreen) return
      this.leftOffset = event.offsetX
      this.topOffset = event.offsetY
      this.$store.commit('desktop/widgetDragging', true)
      document.onmousemove = this.mousemoveRightBottom
      document.onmouseup = () => {
        document.onmousemove = null
        document.onmouseup = null
        this.isMove = false
        this.$store.commit('desktop/widgetDragging', false)
        this.$emit('on-submit', Object.assign(this.widgetData, this.size))
      }
    },
    mousemoveRightBottom(e) {
      console.log(e)
      const wrapperOffset = offSet(this.widgetRef)
      const width = e.clientX - wrapperOffset.left,
        height = e.clientY - wrapperOffset.top,
        w =
          window.innerWidth ||
          document.documentElement.clientWidth ||
          document.body.clientWidth
      this.size.width = (width / w) * 100
      this.size.height = height
    },
    handleDrop(action) {
      console.log(action)
      if (action === 'remove') {
        this.$emit('on-delete')
      }
    },
    defaultAction() {}
  }
}
</script>

<style scoped lang="less">
.widget {
  position: absolute;
  margin-bottom: 15px;
  border-radius: 13px;
  overflow: hidden;
  flex-direction: column;
  display: flex;
  z-index: 9;
  &.move {
    cursor: move;
  }
  &-header {
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 45px;
    padding: 0 15px;
    letter-spacing: 2px;
    color: #fff;
    background: rgba(47, 133, 232, 0.7);
    font-weight: bolder;
    &-icon {
      font-weight: normal;
      font-size: 24px;
      margin-left: 5px;
      cursor: pointer;
    }
    .widget-header-icon {
      color: #fff;
    }
  }
  &-content {
    flex: 1;
    padding: 15px;
    background: rgba(219, 222, 227, 0.7);
    position: relative;
    overflow: auto;
    &.dragging:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }
    &-inner {
      height: 100%;
    }
    .drag-right-bottom {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 12px;
      height: 12px;
      background: rgba(47, 133, 231, 0.43);
      cursor: nwse-resize;
      user-select: none;
    }
  }
}
</style>
