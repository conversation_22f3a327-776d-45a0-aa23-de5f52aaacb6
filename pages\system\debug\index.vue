<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="模块"
              prop="serviceNo"
            >
              <el-select
                v-model="searchForm.serviceNo"
                :style="{width: '160px'}"
                size="small"
                clearable
                placeholder="选择应用"
              >
                <el-option
                  v-for="(item, index) in serviceList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="调试类别"
              prop="matterType"
            >
              <el-select
                v-model="searchForm.category"
                :style="{width: '120px'}"
                size="small"
                clearable
                placeholder="调试类别"
              >
                <el-option
                  v-for="(item, index) in typeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="status"
            >
              <el-select
                v-model="searchForm.status"
                :style="{width: '100px'}"
                size="small"
                clearable
                placeholder="选择状态"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="日期"
              prop="quesType"
            >
              <el-date-picker
                v-model="searchForm.date"
                :type="'daterange'"
                :append-to-body="true"
                :value-format="'yyyy-MM-dd'"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"/>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
          <div
            v-if="description"
            style="margin-bottom: 16px; text-align: right">{{ description }}</div>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="模块名称"
            prop="serviceNo"
          >
            <template
              v-slot="{row}"
            >
              {{ getServiceName('serviceList', row.serviceNo).label }}
            </template>
          </el-table-column>
          <el-table-column
            label="开始时间"
            prop="startTime"
            width="140"
          />
          <el-table-column
            label="结束时间"
            prop="endTime" 
            width="140"
          />
          <el-table-column
            label="时长"
            prop="handleDiff"
            width="120"
          >
            <template v-slot="{ row }">
              {{ showTime(row.handleDiff) }}
            </template>
          </el-table-column>
          <el-table-column
            label="调试类别"
            prop="matter"
            min-width="80"
          >
            <template v-slot="{ row }">
              <el-tag
                :type="getName('typeList', row.category).type"
                disable-transitions
              >{{ getName('typeList', row.category).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="调试内容"
            prop="debugContent"
          >
            <template v-slot="{ row }">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top">
                <div class="one-line">
                  {{ row.debugContent }}
                </div>
                <div
                  slot="content"
                  v-html="formatText(row.debugContent)"/>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="解决方案"
            prop="solution"
          >
            <template v-slot="{ row }">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top">
                <div class="one-line">
                  {{ row.solution }}
                </div>
                <div
                  slot="content"
                  v-html="formatText(row.solution)"/>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="调试状态"
            prop="status"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="getName('statusList', row.status).type"
                disable-transitions
              >{{ getName('statusList', row.status).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            min-width="180px"
            style="white-space: nowrap"
          >
            <template
              slot-scope="{row,$index}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail($index)"
                >详情
                </el-button>
              </span>
              <template>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span v-command="'/system/coordination/delete'">
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :status-list="statusList"
      :service-list="serviceList"
      :type-list="typeList"
      :detail="showDetail"
      @success="handleSearch"
      @next="next"
      @prev="prev"
    />
    <Edit
      ref="modalForm"
      :status-list="statusList"
      :service-list="serviceList"
      :type-list="typeList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  delDebugRecord,
  dictionaryDtlFindByDictCode,
  findDebugRecord,
  matterTop,
  saveDebugRecord
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Detail from './component/detail'

export default {
  layout: 'menuLayout',
  name: 'system-debug',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {},
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: findDebugRecord, //分页接口地址
        delete: delDebugRecord, //删除接口地址
        save: saveDebugRecord,
        getDict: dictionaryDtlFindByDictCode
      },
      userId: null,
      roleStatusList: ENUM.roleStatus,
      editRole: null,
      serviceList: [],
      typeList: [
        { label: '时间延误', value: 1, type: 'primary' },
        { label: '问题整改', value: 2, type: 'info' },
        { label: '功能优化', value: 3, type: 'warning' },
        { label: '计划调试', value: 4, type: 'success' },
        { label: '模型规则', value: 5, type: 'danger' }
      ], // 1-协调事项 2-会议要求 3-遗留事项
      statusList: [
        { label: '是', value: 1, type: 'success' },
        { label: '否', value: 2, type: 'warning' },
        { label: '待处理', value: 3, type: 'primary' },
        { label: '进行中', value: 4, type: 'primary' }
      ],
      timer: null,
      activeIndex: 0
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    },
    week: function() {
      return this.searchForm.week
        ? '第' + this.$moment(this.searchForm.week).week() + '周'
        : ''
    }
  },
  created() {
    this.handleSearch(true)
    this.getServiceInfo()
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    beforeHandleSearch() {
      if (!this.searchForm.date) return
      this.searchForm.startTime = this.searchForm.date[0]
      this.searchForm.endTime = this.searchForm.date[1]
    },
    afterHandleSearch() {
      //
      this.tableData.forEach(item => {
        if (item.endTime) {
          item.handleDiff = this.$moment(item.endTime).diff(
            this.$moment(item.startTime),
            'seconds'
          )
        }
      })
    },
    showTime(val) {
      if (!val) return ''
      if (val < 60) {
        return val + '秒'
      } else {
        const min_total = Math.floor(val / 60) // 分钟
        const sec =
          Math.floor(val % 60) > 9
            ? Math.floor(val % 60)
            : '' + Math.floor(val % 60) // 余秒
        if (min_total < 60) {
          return min_total + '分' + sec + '秒'
        } else {
          const hour_total =
            Math.floor(min_total / 60) > 9
              ? Math.floor(min_total / 60)
              : '' + Math.floor(min_total / 60) // 小时数
          const min =
            Math.floor(min_total % 60) > 9
              ? Math.floor(min_total % 60)
              : '0' + Math.floor(min_total % 60) // 余分钟
          return hour_total + '小时' + min + '分'
        }
      }
    },

    getServiceName(list, status) {
      return (
        this[list].find(item => Number(item.value) === Number(status)) || {}
      )
    },

    getName(list, status) {
      return this[list].find(item => item.value == status) || {}
    },

    // 编辑
    handleEdit(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },

    handleTop(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm(
        '是否确认' + data.priority === null ? '置顶' : '取消置顶' + '此数据项?',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 删除操作
        post(matterTop, { id: data.id, flag: !data.priority }).then(res => {
          this.handleSearch()
        })
      })
    },
    next() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex < this.tableData.length - 1) {
        this.activeIndex++
      } else if (this.page.pageIndex < this.page.totalPages) {
        console.log(this.page.pageIndex)
        this.page.pageIndex++
        this.handleSearch()
        this.activeIndex = 0
      } else {
        this.$message.info('已经是最后一条了')
      }
    },
    prev() {
      if (this.activeIndex > 0) {
        this.activeIndex--
      } else if (this.page.pageIndex > 1) {
        this.page.pageIndex--
        this.handleSearch()
        this.activeIndex = this.page.pageSize - 1
      } else {
        this.$message.warning('已经是第一条了')
      }
    },
    tableRowClassName({ row }) {
      if (row.matterType === 4) {
        return 'warning-row'
      }
      return ''
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },

    async getServiceInfo() {
      const { data: module } = await post(this.url.getDict, {
        dictCode: 'module'
      })
      this.serviceList = module.map(item => {
        return {
          value: item.code,
          label: item.value
        }
      })
      return new Promise(resolve => resolve(true))
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
