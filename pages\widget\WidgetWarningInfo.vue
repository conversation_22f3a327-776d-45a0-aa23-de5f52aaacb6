<template>
  <div
    class="content"
  >
    <div
      class="page-operate"
      style="align-items: flex-start">
      <div class="search-wrapper">
        <el-form
          ref="form"
          :model="searchForm"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label="设备名称"
            prop="deviceName"
          >
            <el-input
              v-model="searchForm.deviceName"
              clearable
              size="small"
              placeholder="设备名称"
              style="width: 130px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="区域名称"
            prop="ruleName"
          >
            <el-input
              v-model="searchForm.areaName"
              clearable
              size="small"
              placeholder="区域名称"
              style="width: 130px"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="报警内容"
            prop="alertContent"
          >
            <el-input
              v-model="searchForm.alertContent"
              clearable
              size="small"
              placeholder="报警内容"
              style="width: 130px"
              type="text"
            />
          </el-form-item>

          <el-form-item
            label="角色"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.roleID"
              size="small"
              placeholder="选择角色"
              style="width: 130px"
            >
              <el-option
                v-for="(item, index) in roleList"
                :key="index"
                :label="item.roleName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="模块"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.moduleCode"
              size="small"
              clearable
              style="width: 130px"
              placeholder="选择模块"
            >
              <el-option
                v-for="(item, index) in moduleList"
                :key="index"
                :label="item.Name"
                :value="item.ID"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报警类型"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.warningType"
              size="small"
              clearable
              style="width: 130px"
              placeholder="报警类型"
            >
              <el-option
                v-for="(item, index) in warningTypeList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报警级别"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.alertLevel"
              size="small"
              clearable
              style="width: 130px"
              placeholder="报警级别"
            >
              <el-option
                v-for="(item, index) in alertLevelList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="状态"
            prop="ruleName"
          >
            <el-select
              v-model="searchForm.status"
              size="small"
              clearable
              style="width: 130px"
              placeholder="状态"
            >
              <el-option
                v-for="(item, index) in statusList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="时间"
            prop="dateTime"
          >
            <el-date-picker
              v-model="searchForm.dateTime"
              :picker-options="pickerOptions"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"/>
          </el-form-item>
        </el-form>
      </div>
      <div
        class="text-right"
        style="white-space: nowrap">
        <el-button
          icon="ios-search"
          type="primary"
          @click="handleRuleNum"
        >规则统计
        </el-button>
        <el-button
          icon="ios-search"
          type="primary"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button
          icon="ios-search"
          type="primary"
          @click="handleExport"
        >导出
        </el-button>
      </div>
    </div>
    <div class="widget-list">
      <el-table
        :data="tableData"
        :row-class-name="getRowClass"
        size="small"
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="所属模块"
          prop="moduleName"
          width="100"
        >
          <!--          <template-->
          <!--            v-slot="{row}"-->
          <!--          >-->
          <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column
          label="报警描述"
          prop="alertContent"
          min-width="100"
        />
        <el-table-column
          label="设备名"
          show-overflow-tooltip
          prop="deviceName"
          width="180"
        />
        <el-table-column
          label="区域名"
          prop="areaName"
          width="100"
        />
        <el-table-column
          label="报警类型"
          prop="alertTypeName"
          width="100"
        />
        <el-table-column
          label="报警等级"
          prop="alertLevel"
          width="90"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.alertLevel == 1"
              type="danger">一级</el-tag>
            <el-tag
              v-if="row.alertLevel == 2"
              type="warning">二级</el-tag>
            <el-tag
              v-if="row.alertLevel == 3">三级</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          width="80"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.status == 0"
              type="danger">
              未处理
            </el-tag>
            <el-tag
              v-if="row.status == 2"
              type="success">
              已处理
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="是否误报"
          prop="status"
          width="80"
        >
          <template v-slot="{row}">
            <span
              v-if="row.isFalse">
              是
            </span>
            <span
              v-else>
              否
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="处理人"
          prop="dealUser"
          width="80"
        />
        <el-table-column
          label="报警分析"
          prop="alertAnalysis"
          width="180"
        />
        <el-table-column
          label="处理意见"
          prop="alertAdvice"
          width="180"
        />
        <el-table-column
          label="报警时间"
          prop="createDateTime"
          width="150"
        />
        <el-table-column
          label="操作"
          prop="createDateTime"
          width="80"
        >
          <template v-slot="{ row }">
            <el-button 
              type="text" 
              @click="showHistory(row.warningRuleID, true)">历史记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-row
      align="middle"
      class="table-pagination"
      justify="center"
      type="flex"
    >
      <el-pagination
        :current-page="page.pageIndex"
        :page-size="page.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="page.total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>
    <el-dialog
      v-el-drag-dialog
      :top="'3%'"
      :title="'报警规则统计'"
      :width="'80%'"
      :visible.sync="popVisible"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >

      <el-table
        :data="ruleNumList.list"
        size="small"
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="报警规则"
          prop="ruleName"
        >
          <!--          <template-->
          <!--            v-slot="{row}"-->
          <!--          >-->
          <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column
          label="报警数量"
          show-overflow-tooltip
          prop="total"
        >
          <template v-slot="{ row }">
            <el-button 
              type="text"
              style="cursor: pointer; text-decoration: underline"
              @click="showHistoryByRuleId(row.id, true, row.ruleName)">{{ row.total }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; margin-top: 10px">
        <el-pagination
          :current-page="ruleNumList.pageIndex"
          :page-size="ruleNumList.pageSize"
          :page-sizes="[10, 20, 30, 40, 100]"
          :total="ruleNumList.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleRuleSizeChange"
          @current-change="handleRuleCurrentChange"
        />
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :top="'3%'"
      :title="'报警历史记录'"
      :width="'80%'"
      :visible.sync="popVisible1"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >
      <div
        class="page-operate"
        style="align-items: flex-start; padding-top: 0">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :label-width="'80px'"
            :model="searchForm"
            inline
            @keyup.enter.native="showHistory(popHistory.id, true)"
          >
            <el-form-item
              label="处理意见"
              prop="ruleName"
            >
              <el-input
                v-model="popHistory.alertAdvice"
                clearable
                size="small"
                placeholder="处理意见"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            type="primary"
            @click="showHistory(popHistory.id, true)"
          >查询
          </el-button>
          <el-button
            type="primary"
            @click="handleHistoryExport"
          >导出
          </el-button>
        </div>
      </div>
      <el-table
        :data="popHistory.list"
        :row-class-name="getRowClass"
        size="small"
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="所属模块"
          prop="moduleName"
          width="100"
        >
          <!--          <template-->
          <!--            v-slot="{row}"-->
          <!--          >-->
          <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column
          label="报警描述"
          prop="alertContent"
          min-width="100"
        />
        <el-table-column
          label="设备名"
          prop="deviceName"
          width="140"
        />
        <el-table-column
          label="区域名"
          prop="areaName"
          width="100"
        />
        <el-table-column
          label="报警类型"
          prop="alertTypeName"
          width="100"
        />
        <el-table-column
          label="报警等级"
          prop="alertLevel"
          width="70"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.alertLevel == 1"
              type="danger">一级</el-tag>
            <el-tag
              v-if="row.alertLevel == 2"
              type="warning">二级</el-tag>
            <el-tag
              v-if="row.alertLevel == 3">三级</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          width="75"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.status == 0"
              type="danger">
              未处理
            </el-tag>
            <el-tag
              v-if="row.status == 2"
              type="success">
              已处理
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="处理人"
          prop="dealUser"
          width="80"
        />
        <el-table-column
          label="处理意见"
          prop="alertAdvice"
          width="180"
        />
        <el-table-column
          label="报警时间"
          prop="createDateTime"
          width="135"
        />
      </el-table>
      <div style="text-align: center; margin-top: 10px">
        <el-pagination
          :current-page="popHistory.pageIndex"
          :page-size="popHistory.pageSize"
          :page-sizes="[10, 20, 30, 40, 100]"
          :total="popHistory.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePopSizeChange"
          @current-change="handlePopCurrentChange"
        />
      </div>
    </el-dialog>

    <!--报警记录by ruleID-->
    <el-dialog
      v-el-drag-dialog
      :top="'3%'"
      :title="popHistoryByRuleId.ruleName + ' 历史记录'"
      :width="'80%'"
      :visible.sync="popVisible2"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >
      <el-table
        :data="popHistoryByRuleId.list"
        :row-class-name="getRowClass"
        size="small"
        border
        style="width: 100%"
      >
        <el-table-column
          label="所属模块"
          prop="moduleName"
          width="100"
        >
          <!--          <template-->
          <!--            v-slot="{row}"-->
          <!--          >-->
          <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column
          label="报警描述"
          prop="alertContent"
          min-width="100"
        />
        <el-table-column
          label="设备名"
          prop="deviceName"
          width="140"
        />
        <el-table-column
          label="区域名"
          prop="areaName"
          width="100"
        />
        <el-table-column
          label="报警类型"
          prop="alertTypeName"
          width="100"
        />
        <el-table-column
          label="报警等级"
          prop="alertLevel"
          width="70"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.alertLevel == 1"
              type="danger">一级</el-tag>
            <el-tag
              v-if="row.alertLevel == 2"
              type="warning">二级</el-tag>
            <el-tag
              v-if="row.alertLevel == 3">三级</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          width="75"
        >
          <template v-slot="{row}">
            <el-tag
              v-if="row.status == 0"
              type="danger">
              未处理
            </el-tag>
            <el-tag
              v-if="row.status == 2"
              type="success">
              已处理
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="处理人"
          prop="dealUser"
          width="80"
        />
        <el-table-column
          label="处理意见"
          prop="alertAdvice"
          width="180"
        />
        <el-table-column
          label="报警时间"
          prop="createDateTime"
          width="135"
        />
      </el-table>
      <div style="text-align: center; margin-top: 10px">
        <el-pagination
          :current-page="popHistoryByRuleId.pageIndex"
          :page-size="popHistoryByRuleId.pageSize"
          :page-sizes="[10, 20, 30, 40, 100]"
          :total="popHistoryByRuleId.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePopByRuleIdSizeChange"
          @current-change="handlePopByRuleIdCurrentChange"
        />
      </div>
    </el-dialog>

    <warning-detail
      ref="modalDetailForm"
      :id="popHistory.id"/>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  exportDoneRate,
  exportHistory,
  exportInfos,
  findBasicDataConfigByType,
  findHistoryByAlertID,
  findHistoryByWarningRuleID,
  findModuleInfoList,
  findRuleAlertCount,
  roleList,
  WarningInfoList,
  WarningInfoListNew
} from '@/api/system'
import { exportWarningRule, findWarningRule } from '~/api/system'
import WarningDetail from '@/pages/widget/component/warningDetail'

export default {
  name: 'WidgetWarningRule',
  components: { WarningDetail },
  layout: 'staticPage',
  mixins: [listMixins],
  data() {
    return {
      popVisible: false,
      ruleNumList: {
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      popVisible1: false,
      popHistory: {
        id: null,
        alertAdvice: '',
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      popVisible2: false,
      popHistoryByRuleId: {
        id: null,
        ruleName: '',
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      searchForm: {
        isFalse: 0
      },
      url: {
        list: WarningInfoList
      },
      tableData: [],
      moduleList: [],
      roleList: [],
      serviceList: [],
      warningTypeList: [
        // 0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        {
          value: 5,
          name: '生产过程监测'
        },
        {
          value: 6,
          name: '趋势校验报警'
        }
      ],
      isConfirm: [
        {
          value: 0,
          name: '未确认'
        },
        {
          value: 1,
          name: '确认'
        }
      ],
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ],
      statusList: [
        {
          value: 0,
          name: '未处理'
        },
        {
          value: 1,
          name: '已挂起'
        },
        {
          value: 2,
          name: '已处理'
        },
        {
          value: 3,
          name: '持续中'
        },
        {
          value: 9,
          name: '审核中'
        }
      ]
    }
  },
  computed: {},
  watch: {
    'searchForm.dateTime': function() {
      if (this.searchForm.dateTime && this.searchForm.dateTime.length) {
        this.searchForm.startTime = this.searchForm.dateTime[0]
        this.searchForm.endTime = this.searchForm.dateTime[1]
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    }
  },
  mounted() {
    this.loadData()
    // this.findBasicDataConfigByType()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        console.log(res)
        this.moduleList = res.data || []
      })
      post(roleList, {
        pageIndex: 1,
        pageSize: 1000,
        roleType: 2
      }).then(res => {
        this.roleList = res.data.content || []
      })
    },
    beforeHandleSearch() {
      this.searchForm.roleID &&
        (this.searchForm.roleIDs = [this.searchForm.roleID])
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content.map(item => {
        return {
          value: item.name,
          label: item.cname
        }
      })
    },
    // 报警规则分组统计
    handleRuleNum() {
      this.popVisible = true
      post(
        findRuleAlertCount,
        Object.assign({
          pageIndex: this.ruleNumList.pageIndex,
          pageSize: this.ruleNumList.pageSize,
          startTime: this.searchForm.startTime,
          endTime: this.searchForm.endTime
        })
      ).then(res => {
        this.ruleNumList.list = res ? res.data.content : []
        this.ruleNumList.pageSize = res.data.pageable.pageSize
        this.ruleNumList.totalPages = res.data.totalPages
        this.ruleNumList.total = res.data.totalElements
      })
    },

    handleRuleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.ruleNumList.pageSize = val
      this.handleRuleNum()
    },
    handleRuleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.ruleNumList.pageIndex = val
      this.handleRuleNum()
    },

    // 历史报警记录
    showHistory(id, reset = true) {
      this.popHistory.id = id
      this.$refs.modalDetailForm.visible = true
      this.$refs.modalDetailForm.showHistory(id, true)
      this.$refs.modalDetailForm.showHandleHistory(id)
    },
    handlePopSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.popHistory.pageSize = val
      this.showHistory(this.popHistory.id)
    },
    handlePopCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.popHistory.pageIndex = val
      this.showHistory(this.popHistory.id)
    },
    // 历史报警记录byRuleId
    showHistoryByRuleId(id, reset = true, ruleName = '') {
      this.popHistoryByRuleId.list = []
      this.popHistoryByRuleId.id = id
      ruleName && (this.popHistoryByRuleId.ruleName = ruleName)
      this.popVisible2 = true
      reset && (this.popHistoryByRuleId.pageIndex = 1)
      post(
        findHistoryByWarningRuleID,
        Object.assign(
          { warningRuleID: id },
          {
            pageIndex: this.popHistoryByRuleId.pageIndex,
            pageSize: this.popHistoryByRuleId.pageSize
          }
        )
      ).then(res => {
        this.popHistoryByRuleId.list = res ? res.data.content : []
        this.popHistoryByRuleId.pageSize = res.data.pageable.pageSize
        this.popHistoryByRuleId.totalPages = res.data.totalPages
        this.popHistoryByRuleId.total = res.data.totalElements
      })
    },
    handlePopByRuleIdSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.popHistoryByRuleId.pageSize = val
      this.showHistoryByRuleId(this.popHistoryByRuleId.id)
    },
    handlePopByRuleIdCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.popHistoryByRuleId.pageIndex = val
      this.showHistoryByRuleId(this.popHistoryByRuleId.id)
    },
    handleHistoryExport() {
      this.loading = true
      post(
        exportHistory,
        Object.assign({ alertInfoID: this.popHistory.id }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警历史记录.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    handleWarning(data) {
      // window.parent.postMessage(
      //   {
      //     type: 'link',
      //     url: data.warningEventBody.alertUrl,
      //     title: 'data.warningEventBody.title'
      //   },
      //   '*'
      // )
    },
    getRowClass({ row }) {
      if (
        this.$moment().format('YYYY-MM-DD') ===
        this.$moment(row.createDateTime).format('YYYY-MM-DD')
      ) {
        return 'select-row'
      }
    },
    handleExport() {
      this.loading = true
      post(
        exportInfos,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警消息.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-operate {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
    .operate-icon {
      margin-left: 8px;
    }
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
/deep/ .select-row {
  background: #ffeff0 !important;
}
/deep/
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped.select-row
  td.el-table__cell {
  background: #ffeff0 !important;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
