<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="role.roleName || '' + '-- 权限管理'"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        label-width="120px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="角色名称："
          prop="roleName"
        >
          {{ role.roleName || '' }}
        </el-form-item>
        <el-form-item
          label="角色权限："
          prop="org"
        >
          <el-tree
            ref="tree"
            :data="data"
            :default-checked-keys="defaultSelected"
            :default-expanded-keys="defaultExpanded"
            :props="defaultProps"
            node-key="id"
            show-checkbox
            @check="handleCheck"
          >
            <template
              v-slot="{ node, data }"
            >
              {{ data.type === 'plugin' ? '(小部件) -- ' : '' }}{{ node.label }}
            </template>

          </el-tree>

        </el-form-item>

      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  findSelectResourceByUserNo,
  saveOrUpdate,
  resourceListNoPage,
  roleById,
  filterResourceSelectedByRole,
  getHomePageResource,
  saveHomePageResource
} from '@/api/system'
import { post } from '@/lib/Util'
import { generateTree, getMenuData } from '@/lib/Menu'

export default {
  components: {},
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'role'],
  data() {
    return {
      loading: false,
      url: {},
      data: [],
      totalData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultSelected: [],
      defaultExpanded: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set() {}
    }
  },
  watch: {},
  async created() {
    await this.loadData()
    await this.loadFilterResource()
  },
  methods: {
    loadData() {
      // 获取所有资源
      post(resourceListNoPage, {}).then(res => {
        if (res.success) {
          this.totalData = res.data
          this.data = generateTree(res.data).sort(
            (a, b) => (a.type < b.type ? -1 : 1)
          )
        }
      })
      // 获取角色下资源
      // post(findSelectResourceByUserNo, { userNo: this.role.userNo }).then(res => {
      //   if (res.success) {
      //     this.defaultSelected = res.data
      //     /*.filter(item => item.isSelected === '0').forEach(item => {
      //         this.$refs.tree && this.$refs.tree.setChecked(item.id, false)
      //       })*/
      //   }
      // })
    },
    loadFilterResource() {
      post(getHomePageResource, {
        userNo: localStorage.getItem('userId')
      }).then(res => {
        if (Array.isArray(res.data) && res.data.length > 0) {
          this.defaultSelected = res.data.map(item => item.resourceID)
        }
      })
    },
    handleCheck(node, checked) {
      // 如果是button类型的叶子节点，自动选中其父节点
      if (node.type === 'button' && !node.children) {
        const parentNode = this.findParentNode(this.data, node.id)
        if (parentNode) {
          this.$refs.tree.setChecked(parentNode, true)
        }
      }
    },

    calculateEffectiveNodes() {
      // 获取所有选中的节点
      const selectedNodes = this.$refs.tree.getCheckedNodes(true)

      // 按规则计算有效节点
      let effectiveNodes = []

      selectedNodes.forEach(node => {
        // 叶子节点且type为menu的直接计入
        if (!node.children && node.type === 'menu') {
          effectiveNodes.push(node)
        }
        // 如果是非叶子节点，检查是否有button类型的子节点被选中
        else if (node.children) {
          // 查找当前节点下是否有button类型的子节点被选中
          const hasButtonChild = this.hasSelectedButtonChild(
            node,
            selectedNodes
          )
          if (hasButtonChild) {
            effectiveNodes.push(node)
          }
        }
      })

      // 去重
      effectiveNodes = effectiveNodes.filter(
        (node, index, self) => index === self.findIndex(n => n.id === node.id)
      )

      return effectiveNodes
    },

    hasSelectedButtonChild(node, selectedNodes) {
      // 递归检查是否有选中的button类型子节点
      if (!node.children) {
        return (
          node.type === 'button' && selectedNodes.some(n => n.id === node.id)
        )
      }

      return node.children.some(child =>
        this.hasSelectedButtonChild(child, selectedNodes)
      )
    },

    findParentNode(nodes, nodeId, parent = null) {
      // 递归查找父节点
      for (const node of nodes) {
        if (node.id === nodeId) {
          return parent
        }

        if (node.children && node.children.length > 0) {
          const found = this.findParentNode(node.children, nodeId, node)
          if (found) return found
        }
      }

      return null
    },

    handelConfirm() {
      // 确保树组件已渲染
      if (!this.$refs.tree) {
        console.error('树组件未找到')
        return
      }

      // 计算有效节点
      const effectiveNodes = this.calculateEffectiveNodes()

      // 获取所有选中的节点
      const allSelectedNodes = this.$refs.tree.getCheckedNodes(true)

      // 构建最终需要提交的节点列表（替换button节点为其父节点）
      let finalSelectedNodes = []

      allSelectedNodes.forEach(node => {
        if (node.type === 'button' && !node.children) {
          // 如果是button类型的叶子节点，找到其父节点
          const parentNode = this.findParentNode(this.data, node.id)
          if (
            parentNode &&
            !finalSelectedNodes.some(n => n.id === parentNode.id)
          ) {
            finalSelectedNodes.push(parentNode)
          }
        } else {
          // 其他节点直接添加
          if (!finalSelectedNodes.some(n => n.id === node.id)) {
            finalSelectedNodes.push(node)
          }
        }
      })

      // 提取节点ID
      const selectIDs = finalSelectedNodes.map(node => node.id)

      console.log('%c selectIDs', 'color: red', selectIDs)

      // 检查有效节点数量是否超过4个
      if (selectIDs.length > 4) {
        this.$message.warning('最多只能选择4个资源')
        return
      }

      // 计算未选中的节点ID
      const unSelectIDs = this.totalData
        .filter(item => !selectIDs.includes(item.id))
        .map(item => item.id)

      this.loading = true

      post(saveHomePageResource, {
        userNo: this.role.userNo,
        selectIDs: selectIDs,
        unSelectIDs: unSelectIDs
      })
        .then(res => {
          this.loading = false
          if (res.success) {
            this.$message.success('分配成功！')
            this.close()
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style scoped>
</style>
