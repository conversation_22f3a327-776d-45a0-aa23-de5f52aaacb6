<template>
  <div>

    <div class="page-content">
      <div class="page-card shadow-light">
        <div style="position: relative">
          <el-tabs
            v-model="activeName"
            style="user-select: none"
            @tab-click="handleClick">
            <el-tab-pane
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :name="item.value.toString()"/>
            <template>
              <div
                class="operate-wrapper">
                <div class="page-operate">
                  <div class="search-wrapper">
                    {{ getDict(Number(activeName), 'moduleList').label }}模块综合得分：<span style="color: crimson; font-size: 20px">{{ score }}</span>
                  </div>
                  <div>
                    <el-button
                      icon="el-icon-circle-plus-outline"
                      type="success"
                      @click="handleAdd"
                    >新增
                    </el-button>
                  </div>
                </div>
                <el-table
                  v-loading="loading"
                  :data="dataList"
                  :size="size"
                  :header-cell-class-name="'custom-header'"
                  border
                  style="width: 100%"
                  height="70vh"
                >
                  <el-table-column
                    label="一级菜单栏"
                    prop="name"
                    fixed="left"
                    width="150"
                    show-overflow-tooltip
                  >
                    <template v-slot="{ row }">
                      <span v-if="row.parentId === '0'">{{ row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    fixed="left"
                    label="子菜单栏"
                    prop="name"
                    width="180"
                    show-overflow-tooltip
                  >
                    <template v-slot="{ row }">
                      <span v-if="row.parentId !== '0'">{{ row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'综合\n评分'"
                    prop="comprehensiveScore"
                    width="50"
                  />
                  <el-table-column
                    :label="'页面\n开发'"
                    prop="pageDev"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="orange node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.pageDev"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'后端\n开发'"
                    prop="backendDev"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="green node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.backendDev"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'数据\n接入'"
                    prop="dataAccess"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="blue node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.dataAccess"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'功能\n上线'"
                    prop="functionOnline"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="red node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.functionOnline"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'投入\n运行'"
                    prop="putIntoOperation"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="yellow node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.putIntoOperation"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="'交付\n验收'"
                    prop="deliveryAcceptance"
                    width="60"
                  >
                    <template v-slot="{ row }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="lightBlue node">
                        <el-progress
                          :text-inside="true"
                          :stroke-width="15"
                          :percentage="row.deliveryAcceptance"
                          status="success"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :class-name="'node-cell'"
                    :width="36 * weekNum +1"
                  >
                    <template
                      slot-scope="{ column, $index }"
                      slot="header">
                      <div
                        v-for="(val, month) in cycle"
                        :key="month + guid()"
                        class="header-wrapper">
                        <div class="header-month">{{ month }}</div>
                        <div class="header-weeks">
                          <span
                            v-for="(week, index) in cycle[month]"
                            :key="index"
                            class="header-week"
                            @click="viewSummary(month, week)">{{ week }}</span>
                        </div>
                      </div>
                    </template>
                    <template v-slot="{ row, column, $index }">
                      <div
                        v-if="row.parentId !== '0'"
                        class="node-wrapper">
                        <template v-for="(val, month) in cycle">
                          <span
                            :key="month">
                            <div
                              v-for="(week, index) in cycle[month]"
                              :class="['color' + row.ganttData[month][index]]"
                              :key="month + week"
                              class="color-block"
                              @click.stop="editGantt(row, month, index)">
                              <div
                                v-if="currentRow && row.id === currentRow.id && month === currentEdit[0] && index === currentEdit[1]"
                                :class="{'top': $index > 4 && dataList.length - $index < 5}"
                                class="color-select shadow-light">
                                <span class="pop-arrow"/>
                                <div
                                  v-for="(work, wIndex) in workList"
                                  :key="wIndex + guid()"
                                  class="select-item"
                                  @click="setGantt(row, work.value)">
                                  <span
                                    :class="'color' + work.value"
                                    class="color-node"/>
                                  {{ work.label }}</div>
                                <div
                                  class="select-item"
                                  @click="setGantt(row, null)">
                                  <span
                                    class="color-node"/>
                                  空白</div>
                              </div>
                            </div>
                          </span>

                        </template>
                      </div>
                    </template>
                  </el-table-column>


                  <el-table-column
                    label="操作"
                    width="100"
                    fixed="right"
                  >
                    <template
                      v-slot="{row}"
                    >
                      <span>
                        <el-button
                          slot="reference"
                          type="text"
                          @click="handleEdit(row)"
                        >编辑
                        </el-button>
                      </span>
                      <span v-if="!row.children.length">
                        <el-divider direction="vertical" />
                        <el-button
                          slot="reference"
                          type="text"
                          @click="handleDelete(row)"
                        >删除
                        </el-button>
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-tabs>
        </div>
      </div>
    </div>
    <Edit
      ref="modalForm"
      :module="Number(activeName)"
      @success="handleSearch(true)"
    />
    <Handle
      ref="modalHandle"
      :current-edit="currentEdit"
      :current-row="currentRow"
      @success="handleSearch(true)" />
    <Detail
      ref="modalDetailForm"
      :detail="showDetail"
    />
  </div>
</template>

<script>
import {
  dictionaryDtlFindByDictCode,
  progressDelete,
  progressFind,
  progressSave,
  weeklySummaryFind
} from '@/api/system'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'
import * as _ from 'lodash'
import listMixins from '@/mixins/ListMixins'
import Edit from '@/pages/system/progress/component/edit'
import Handle from '@/pages/system/progress/component/handle'
import Detail from '@/pages/system/summary/component/detail'

export default {
  name: 'Progress',
  components: { Handle, Edit, Detail },
  layout: 'menuLayout',
  mixins: [listMixins],
  data() {
    return {
      formData: {
        work: 1
      },
      activeName: '1',
      dataList: [],
      url: {
        edit: progressSave,
        list: progressFind, //分页接口地址
        delete: progressDelete, //删除接口地址
        save: progressSave,
        summaryList: weeklySummaryFind,
        getDict: dictionaryDtlFindByDictCode
      },
      page: {
        pageIndex: 1,
        pageSize: 2000,
        total: 0,
        totalPages: 0
      },
      moduleList: [],
      workList: ENUM.workList,
      loading: true,
      selectVisible: false,
      currentRow: null,
      currentEdit: [],
      weeks: [1, 2, 3, 4],
      cycle: {
        '2022-07': [],
        '2022-08': [],
        '2022-09': [],
        '2022-10': [],
        '2022-11': [],
        '2022-12': [],
        '2023-01': [],
        '2023-02': [],
        '2023-03': [],
        '2023-04': [],
        '2023-05': [],
        '2023-06': [],
        '2023-07': [],
        '2023-08': [],
        '2023-09': []
      },
      showDetail: {}
    }
  },
  computed: {
    score: function() {
      if (!this.dataList.length) return '0.00'
      return (
        _.sumBy(
          this.dataList.filter(item => item.parentId !== '0'),
          'comprehensiveScore'
        ) / this.dataList.filter(item => item.parentId !== '0').length
      ).toFixed(2)
    },
    weekNum: function() {
      let num = 0
      for (const key in this.cycle) {
        num += this.cycle[key].length
      }
      return num
    }
  },
  watch: {},
  created() {
    this.formatData()
    this.reset()
  },
  mounted() {
    this.bindClick()
  },
  destroyed() {
    this.unbindClick()
  },
  methods: {
    // 生成时间数据
    formatData() {
      for (const key in this.cycle) {
        const dayOfWeek = this.$moment(key).weekday()
        let num = this.$moment(key).daysInMonth()
        if (dayOfWeek <= 5) {
          num -= 5 - dayOfWeek
        } else {
          num -= 8 - dayOfWeek + 4
        }
        // 月含周数
        const weekNum = parseInt(num / 7) + (num % 7 > 0 ? 1 : 0)
        // 计算月第一周数
        let firstWeek = 0
        if (dayOfWeek <= 5) {
          firstWeek = this.$moment(key)
            .add(5 - dayOfWeek, 'days')
            .week()
        } else {
          firstWeek = this.$moment(key)
            .add(8 - dayOfWeek + 4, 'days')
            .week()
        }
        // 添加周数
        for (let i = 0; i < weekNum; i++) {
          this.cycle[key].push(firstWeek + i)
        }
      }
      console.log(this.cycle)
    },
    bindClick() {
      document.addEventListener('click', this.cancelPop)
    },
    unbindClick() {
      document.removeEventListener('click', this.cancelPop)
    },
    cancelPop() {
      this.currentRow = null
    },
    async handleSearch(reset = false) {
      if (!this.moduleList.length) {
        const { data: module } = await post(this.url.getDict, {
          dictCode: 'module'
        })
        this.moduleList = module.map(item => {
          return {
            value: item.code,
            label: item.value
          }
        })
        this.activeName = this.moduleList.length ? this.moduleList[0].value : ''
      }
      if (!this.url || !this.url.list) {
        this.$message.warning('请设置url.list属性!')
        return
      }
      if (!this.activeName) {
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      // 搜索
      this.loading = true
      this.searchForm.module = this.activeName
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data.content : []
      this.page.pageSize = data.pageable.pageSize
      this.page.totalPages = data.totalPages
      this.page.total = data.totalElements
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    afterHandleSearch() {
      const arr = this.tableData.filter(res => res.parentId === '0')
      for (let i = arr.length - 1; i >= 0; i--) {
        arr[i].children = this.tableData.filter(
          res => res.parentId === arr[i].id
        )
        arr.splice(
          i + 1,
          0,
          ...this.tableData.filter(res => res.parentId === arr[i].id)
        )
      }
      arr.forEach(item => {
        item.children = item.children || []
        item.ganttData = Object.assign(
          {},
          this.cycle,
          item.ganttData ? JSON.parse(item.ganttData) : {}
        )
      })
      this.dataList = arr
    },
    reset() {},
    handleClick(tab, event) {
      this.handleSearch()
    },
    handleAdd: function() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.visible = true
      this.$refs.modalForm.formData.module = Number(this.activeName)
      this.$refs.modalForm.getMenuList()
    },
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
      this.$refs.modalForm.getMenuList()
    },
    handleHandle: function(row, month, week) {
      this.$refs.modalHandle.edit(row)
      this.$refs.modalHandle.visible = true
      this.$refs.modalHandle.currentRow = _.cloneDeep(row)
      this.$refs.modalHandle.currentEdit = [month, week]
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          this.currentRow.ganttData[this.currentEdit[0]][
            this.currentEdit[1]
          ] = this.formData.work
          const params = Object.assign(this.currentRow, {
            ganttData: JSON.stringify(this.currentRow.ganttData)
          })
          post(this.url.save, params).then(res => {
            if (res.success) {
              this.$message.success('修改成功！')
              this.selectVisible = false
              this.loading = false
              this.handleSearch()
            } else {
              this.$message.warning('修改失败！')
              this.loading = false
            }
          })
        })
      }
    },
    async viewSummary(month, week) {
      const _year = month.substring(0, 4)
      const _month = Number(month.substring(5, 7))
      const _week = week
      console.log(_year, _month, _week)
      console.log(
        this.$moment(`${month}-01`).week(),
        this.$moment(`${_year}-${_week}`, 'gggg-ww')
          .weekday(1)
          .format('YYYY-MM-DD'),
        this.$moment(`${_year}-${_week}`, 'gggg-ww')
          .weekday(7)
          .format('YYYY-MM-DD')
      )
      const { data } = await post(
        this.url.summaryList,
        Object.assign(
          {
            startDate: this.$moment(`${_year}-${_week}`, 'gggg-ww')
              .weekday(1)
              .format('YYYY-MM-DD'),
            endDate: this.$moment(`${_year}-${_week}`, 'gggg-ww')
              .weekday(7)
              .format('YYYY-MM-DD'),
            module: this.module
          },
          this.searchForm,
          {
            pageIndex: 1,
            pageSize: 99
          }
        )
      )
      if (data.content.length) {
        this.showDetail = data.content[0]
        this.$refs.modalDetailForm.visible = true
      } else {
        this.$message.warning('暂无周总结！')
      }
    },
    setGantt(row, work) {
      this.currentRow.ganttData[this.currentEdit[0]][this.currentEdit[1]] = work
      const params = Object.assign(this.currentRow, {
        ganttData: JSON.stringify(this.currentRow.ganttData)
      })
      post(this.url.edit, params).then(res => {
        if (res.success) {
          this.$message.success('修改成功！')
          this.selectVisible = false
          this.currentRow = null
          this.handleSearch()
        } else {
          this.$message.warning('修改失败！')
          this.currentRow = null
        }
      })
    },
    editGantt(row, month, week) {
      console.log(month, week)
      // this.handleHandle()
      this.currentRow = _.cloneDeep(row)
      this.currentEdit = [month, week]
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style scoped lang="less">
.operate-wrapper {
  margin: 10px;
}
.tab-select {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 9;
}
.operate-wrapper {
  .node {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 5px;
  }
  /deep/ .color1,
  /deep/ .orange {
    background-color: #f4731e !important;
    border-right-color: #f4731e !important;
  }
  /deep/ .color2,
  /deep/ .green {
    background-color: #91c650 !important;
    border-right-color: #91c650 !important;
  }
  /deep/ .color3,
  /deep/ .blue {
    background-color: #1239aa !important;
    border-right-color: #1239aa !important;
  }
  /deep/ .color4,
  /deep/ .red {
    background-color: #de291d !important;
    border-right-color: #de291d !important;
  }
  /deep/ .color5,
  /deep/ .yellow {
    background-color: #9a60b4 !important;
    border-right-color: #9a60b4 !important;
  }
  /deep/ .color6,
  /deep/ .lightBlue {
    background-color: #159fec !important;
    border-right-color: #159fec !important;
  }
  /deep/ .el-progress-bar__outer {
    background-color: #ccc;
  }
  /deep/ .custom-header {
    text-align: center;
    background-color: #666;
  }
  /deep/ .color-block {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    border-right: 1px solid #e6e9f4;
    width: 36px;
    height: 36px;
    cursor: pointer;
  }
  /deep/ .node-wrapper {
    white-space: nowrap;
    font-size: 0;
  }
  /deep/ .node-cell .cell {
    padding: 0;
    overflow: visible;
  }
  /deep/ .node-cell {
    padding: 0 !important;
  }
  /deep/ .el-dropdown {
    font-size: 0;
  }
  /deep/ .el-table thead th.el-table__cell {
    background: #f5f7fa !important;
    border-right-color: #e6e9f4 !important;
  }
  /deep/ .el-table--small .el-table__cell {
    padding: 2px 0;
  }
}
.header-wrapper {
  display: inline-block;
  text-align: center;
  .header-month {
    text-align: center;
    border-bottom: 1px solid #e6e9f4;
    border-right: 1px solid #e6e9f4;
  }
  .header-weeks {
    font-size: 0;
  }
  .header-week {
    font-size: 12px;
    display: inline-block;
    vertical-align: middle;
    border-right: 1px solid #e6e9f4;
    width: 36px;
    height: 36px;
    line-height: 36px;
    cursor: pointer;
  }
}
.color-select {
  width: 100px;
  position: absolute;
  top: 95%;
  left: 0;
  font-size: 12px;
  background: #fff;
  line-height: 25px;
  z-index: 999;
  text-align: center;
  &.top {
    top: unset;
    bottom: 95%;
    .pop-arrow {
      top: unset;
      bottom: -12px;
      border-top-width: 6px;
      border-bottom-color: transparent;
      border-top-color: #ebeef5;
    }
  }
  .pop-arrow {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
    top: -6px;
    left: 13px;
    margin-right: 3px;
    border-top-width: 0;
    border-bottom-color: #ebeef5;
  }
  .select-item:hover {
    background: #d2d0d0;
  }
  .color-node {
    display: inline-block;
    vertical-align: middle;
    height: 15px;
    width: 20px;
  }
}
</style>
