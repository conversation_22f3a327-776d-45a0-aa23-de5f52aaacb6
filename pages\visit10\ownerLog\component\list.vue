<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="userNo + ' 访问记录'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="800px"
      v-on="$listeners"
    >
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        class="custom-table"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
        />
        <el-table-column
          label="所属应用"
          prop="serviceNo"
          width="140"
        >
          <template v-slot="{ row }">
            {{ getServiceName(row.serviceName).cname }}
          </template>
        </el-table-column>
        <el-table-column
          label="责任页面"
          prop="pageName"
        >
          <template v-slot="{ row }">
            {{ row.parentName }} -  {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column
          label="访问次数"
          prop="num"
          width="160"
        />
      </el-table>
      <el-row
        align="middle"
        class="table-pagination"
        justify="end"
        type="flex"
      >
        <el-pagination
          :current-page="page.pageIndex"
          :page-size="page.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
      <div slot="footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { pageLogSecret } from '@/api/system'
import ListMixins from '@/mixins/ListMixins'
import BaseMixins from '@/mixins/BaseMixins'

export default {
  components: {},
  mixins: [ListMixins, BaseMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: {
    loginTimeStart: {
      type: String,
      default: ''
    },
    loginTimeEnd: {
      type: String,
      default: ''
    },
    serviceList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      url: {
        list: pageLogSecret.getDetailHandleUser
      },
      userNo: '',
      resourceName: ''
    }
  },
  computed: {},
  created() {},
  methods: {
    beforeHandleSearch() {
      if (!this.loginTimeStart || !this.userNo)
        return new Promise(resolve => false)
      this.searchForm.loginTimeStart = this.loginTimeStart
      this.searchForm.loginTimeEnd = this.loginTimeEnd
      this.searchForm.userNo = this.userNo
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.name === name) || {}
    }
  }
}
</script>
<style scoped>
.table-pagination {
  margin-top: 20px;
}
</style>
