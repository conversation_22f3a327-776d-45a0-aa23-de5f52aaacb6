<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '周例会协调事项'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入协调事项',
              trigger: 'change'
            }
          ]"
          label="协调事项"
          prop="matter"
        >
          <el-input
            v-model="formData.matter"
            :rows="6"
            type="textarea"
            placeholder="请输入协调事项" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择事项类型',
              trigger: 'change'
            }
          ]"
          label="事项类型"
          prop="matterType"
        >
          <el-select
            v-model="formData.matterType"
            size="small"
            clearable
            placeholder="选择事项类型"
          >
            <el-option
              v-for="(item, index) in typeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入提出人单位',
              trigger: 'blur'
            }
          ]"
          label="提出人单位"
          prop="presenterUnit"
        >
          <el-input
            v-model="formData.presenterUnit"
            placeholder="请输入提出人单位" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入提出人',
              trigger: 'blur'
            }
          ]"
          label="提出人"
          prop="presenter"
        >
          <el-input
            v-model="formData.presenter"
            placeholder="请输入提出人" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择日期',
              trigger: 'blur'
            }
          ]"
          label="任务日期"
          prop="taskDate"
        >
          <el-date-picker
            v-model="formData.taskDate"
            :value-format="'yyyy-MM-dd'"
            type="date"
            placeholder="选择时间" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入责任单位',
              trigger: 'blur'
            }
          ]"
          label="责任单位"
          prop="handleUnit"
        >
          <el-input
            v-model="formData.handleUnit"
            placeholder="请输入责任单位" />
        </el-form-item>
        <el-form-item
          label="责任人"
          prop="handlePerson"
        >
          <el-input
            v-model="formData.handlePerson"
            placeholder="请输入责任人" />
        </el-form-item>
        <el-form-item
          label="计划完成时间"
          prop="planCompleteDate"
        >
          <el-date-picker 
            v-model="formData.planCompleteDate"
            :value-format="'yyyy-MM-dd'"
            type="date"
            placeholder="选择时间" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择处理状态',
              trigger: 'blur'
            }
          ]"
          label="处理状态"
          prop="handleStatus"
        >
          <el-radio-group
            v-model="formData.handleStatus"
            size="medium"
          >
            <el-radio
              v-for="(item, index) in statusList"
              :key="index"
              :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="处理结果"
          prop="handleResults"
        >
          <el-input
            v-model="formData.handleResults"
            :rows="6"
            type="textarea"
            placeholder="请输入处理结果" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  matterSave,
  saveFeedback,
  uploadFile
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: matterSave,
        add: matterSave,
        file: uploadFile
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    async handelUpload() {
      // 删除图片
      if (this.deleteIds.length) {
        const del = await post(deleteFileByIds, { ids: this.deleteIds })
      }
      //上传
      if (!this.$refs.upload.uploadFiles.length) return
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      const res = await post(this.url.file, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (res.success) {
        this.uploadFiles = res.data
        return Promise.resolve(true)
      } else {
        this.$message.warning('图片上传失败！')
        this.loading = false
        return Promise.resolve(false)
      }
      return Promise.reject(false)
    },
    httpRequest(params) {},
    submitBefore() {},
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
