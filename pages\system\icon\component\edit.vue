<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '图标'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="160px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="图标类型"
          prop="roleCode"
        >
          <el-radio
            v-for="item in typeList"
            :key="item.value"
            v-model="formData.iconType"
            :label="item.value">{{ item.label }}</el-radio>
        </el-form-item>
        <el-form-item
          label="上传文件"
          prop="roleCode"
        >
          <el-upload
            ref="upload"
            :auto-upload="false"
            :http-request="httpRequest"
            multiple
            class="upload-demo"
            action="#">
            <el-button 
              slot="trigger" 
              size="small" 
              type="primary">选取文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit, uploadImg, userAdd, userEdit } from '@/api/system'
import { post } from '@/lib/Util'

export default {
  components: {},
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      url: {
        edit: roleEdit,
        add: uploadImg
      },
      formData: {},
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      rules: {
        roleCode: [
          {
            required: true,
            message: '请输入角色编号',
            trigger: 'blur'
          }
        ],
        roleName: [
          {
            required: true,
            message: '请输入角色名称',
            trigger: 'blur'
          }
        ],
        isDef: [
          {
            required: true,
            message: '请选择是否为默认角色',
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择角色状态',
            type: 'number',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('角色编辑页面')
  },
  methods: {
    handelConfirm() {
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      formData.append('iconType', this.formData.iconType)
      post(this.url.add, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        console.log(res)
        if (res.success) {
          this.$refs.upload.clearFiles()
          this.close()
        }
      })
    },
    httpRequest(params) {}
  }
}
</script>
<style scoped>
</style>
