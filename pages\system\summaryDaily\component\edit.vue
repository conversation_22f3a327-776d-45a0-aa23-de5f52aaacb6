<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '日总结'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择模块',
              trigger: 'change'
            }
          ]"
          label="模块："
          prop="module"
        >
          <el-select
            v-model="formData.module"
            size="small"
            placeholder="选择模块"
          >
            <el-option
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择时间',
              trigger: 'change'
            }
          ]"
          label="时间："
          prop="weekDate"
        >
          <el-date-picker
            v-model="formData.weekDate"
            :placeholder="'选择日期'"
            :append-to-body="true"
            :format="'yyyy-MM-dd'"
            :value-format="'yyyy-MM-dd'"
            :clearable="false"
            style="width: 140px"/>
          <span
            style="top: 2px; bottom: 2px; left: 20px;">{{ week }}</span>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入本日总结',
              trigger: 'change'
            }
          ]"
          label="本日总结"
          prop="currWeekSummary"
        >
          <el-input
            v-model="formData.currWeekSummary"
            :rows="6"
            type="textarea"
            placeholder="请输入本日总结" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入明日计划',
              trigger: 'change'
            }
          ]"
          label="明日计划"
          prop="nextWeekPlan"
        >
          <el-input
            v-model="formData.nextWeekPlan"
            :rows="6"
            type="textarea"
            placeholder="请输入明日计划" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  matterSave,
  saveFeedback,
  uploadFile,
  weeklySummarySave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: weeklySummarySave,
        add: weeklySummarySave,
        file: uploadFile
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    weekChange() {
      this.$nextTick(() => {
        this.formData.weekDate = this.$moment(this.formData.weekDate)
          .startOf('isoWeek')
          .add(4, 'days')
          .format('yyyy-MM-DD')
      })
    },
    httpRequest(params) {},
    submitBefore() {
      // 提交前操作
      this.formData.type = 1
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {
        weekDate: this.$moment().format('yyyy-MM-DD')
      }
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
