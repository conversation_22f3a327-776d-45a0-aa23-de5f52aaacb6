<template>
  <div
    :style="{backgroundImage: `url(${backgroundUrl})`}"
    class="header">
    <!-- 标题部分 -->
    <ul class="item-list">
      <li 
        v-for="(item, index) in itemList" 
        :key="index">
        <img
          :src="item.icon"
          alt=""
          class="img-left"
        >
        <a 
          :href="item.link" 
          class="item-link" 
          target="_blank">{{ item.name }}</a>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'ZhbjFactoryOverview',
  props: {
    backgroundUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeIndex: 0,
      itemList: [
        {
          name: '智慧质量',
          icon: require('../../../assets/desktop/zhbj/quality.png'),
          link: 'http://*************:9700/'
        },
        {
          name: '智慧运维',
          icon: require('../../../assets/desktop/zhbj/operation.png'),
          link: 'http://*************:9110/'
        },
        {
          name: '智慧能源',
          icon: require('../../../assets/desktop/zhbj/water.png'),
          link: 'http://*************:9202/'
        },
        {
          name: '智慧成本',
          icon: require('../../../assets/desktop/zhbj/cost.png'),
          link: 'http://*************:9502/'
        }
      ]
    }
  }
}
</script>

<style scoped lang="less">
.header {
  width: 100%;
  height: 72px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #000;
  overflow: hidden;

  .item-list {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 20px;
    font-size: 16px;
    margin-top: 10px;
    transform: translateX(-20px);

    .img-left {
      width: 24px;
      height: 24px;
      margin-right: 4px;
      vertical-align: top;
      cursor: pointer;
    }

    .item-link {
      color: #000;
      text-decoration: none;
    }
  }
}
</style>
