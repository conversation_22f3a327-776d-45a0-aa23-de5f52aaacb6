<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="设备名称"
              prop="deviceName"
            >
              <el-input
                v-model="searchForm.deviceName"
                clearable
                size="small"
                placeholder="设备名称"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="区域名称"
              prop="ruleName"
            >
              <el-input
                v-model="searchForm.areaName"
                clearable
                size="small"
                placeholder="区域名称"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="报警内容"
              prop="alertContent"
            >
              <el-input
                v-model="searchForm.alertContent"
                clearable
                size="small"
                placeholder="报警内容"
                style="width: 130px"
                type="text"
              />
            </el-form-item>

            <el-form-item
              label="角色"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.roleID"
                size="small"
                placeholder="选择角色"
                style="width: 130px"
              >
                <el-option
                  v-for="(item, index) in roleList"
                  :key="index"
                  :label="item.roleName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="模块"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.moduleCode"
                size="small"
                clearable
                style="width: 130px"
                placeholder="选择模块"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.Name"
                  :value="item.ID"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警类型"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.warningType"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警类型"
              >
                <el-option
                  v-for="(item, index) in warningTypeList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警级别"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.alertLevel"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警级别"
              >
                <el-option
                  v-for="(item, index) in alertLevelList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.status"
                size="small"
                clearable
                style="width: 130px"
                placeholder="状态"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="时间"
              prop="dateTime"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                :picker-options="pickerOptions"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right"/>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleRuleNum"
          >规则统计
          </el-button>
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleExport"
          >导出
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">

        <el-table
          :data="tableData"
          :row-class-name="getRowClass"
          size="small"
          border
          style="width: 100%"
        >
          <el-table-column
            label="所属模块"
            prop="moduleName"
            width="80"
          >
            <!--          <template-->
            <!--            v-slot="{row}"-->
            <!--          >-->
            <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
            <!--          </template>-->
          </el-table-column>
          <el-table-column
            label="报警描述"
            prop="alertContent"
            min-width="150"
          />
          <el-table-column
            label="设备名"
            show-overflow-tooltip
            prop="deviceName"
            width="140"
          />
          <el-table-column
            label="区域名"
            prop="areaName"
            width="100"
          />
          <el-table-column
            label="报警类型"
            prop="alertTypeName"
            width="75"
          />
          <el-table-column
            label="报警等级"
            prop="alertLevel"
            width="72"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.alertLevel == 1"
                type="danger">一级</el-tag>
              <el-tag
                v-if="row.alertLevel == 2"
                type="warning">二级</el-tag>
              <el-tag
                v-if="row.alertLevel == 3">三级</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            prop="status"
            width="75"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.status == 0"
                type="danger">
                未处理
              </el-tag>
              <el-tag
                v-if="row.status == 2"
                type="success">
                已处理
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="是否误报"
            prop="status"
            width="70"
          >
            <template v-slot="{row}">
              <span
                v-if="row.isFalse">
                是
              </span>
              <span
                v-else>
                否
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="处理人"
            prop="dealUser"
            width="80"
          />
          <el-table-column
            label="报警分析"
            prop="alertAnalysis"
            width="160"
          />
          <el-table-column
            label="处理意见"
            prop="alertAdvice"
            width="160"
          />
          <el-table-column
            label="报警时间"
            prop="createDateTime"
            width="135"
          />
          <el-table-column
            label="误报反馈"
            prop="falseFeedBack"
            width="160"
          />
          <el-table-column
            label="操作"
            prop="createDateTime"
            width="80"
          >
            <template v-slot="{ row }">
              <el-button
                type="text"
                @click="handleSingle(row)">处理</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <el-dialog
      v-el-drag-dialog
      :top="'3%'"
      :title="'报警规则统计'"
      :width="'80%'"
      :visible.sync="popVisible"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >

      <el-table
        :data="ruleNumList.list"
        size="small"
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="报警规则"
          prop="ruleName"
        />
        <el-table-column
          label="报警数量"
          show-overflow-tooltip
          prop="total"
        />
      </el-table>
      <div style="text-align: center; margin-top: 10px">
        <el-pagination
          :current-page="ruleNumList.pageIndex"
          :page-size="ruleNumList.pageSize"
          :page-sizes="[10, 20, 30, 40, 100]"
          :total="ruleNumList.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleRuleSizeChange"
          @current-change="handleRuleCurrentChange"
        />
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :top="'3%'"
      :title="'误报处置'"
      :width="'600px'"
      :visible.sync="popHandleVisible"
      v-bind="$attrs"
      class="video-dialog"
      v-on="$listeners"
    >

      <el-form
        ref="form"
        :model="formData"
        label-width="120px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入报警分析',
              trigger: 'change'
            }
          ]"
          :prop="'alertAdvice'"
          label="报警分析："
        >
          <el-input
            v-model="formData.alertAnalysis"
            :rows="4"
            :disabled="true"
            type="textarea"
            placeholder="请输入报警分析"
            style="color: #3a3f63 !important"
          />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入处理意见',
              trigger: 'change'
            }
          ]"
          :prop="'alertAdvice'"
          label="处理意见："
        >
          <el-input
            v-model="formData.alertAdvice"
            :rows="4"
            :disabled="true"
            type="textarea"
            placeholder="请输入处理意见"
            style="color: #3a3f63"
          />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入误报反馈',
              trigger: 'change'
            }
          ]"
          :prop="'falseFeedBack'"
          label="误报反馈："
        >
          <el-input
            v-model="formData.falseFeedBack"
            :rows="4"
            type="textarea"
            placeholder="请输入误报反馈"
          />
        </el-form-item>

        <el-form-item
          label="是否误报"
          prop="ruleName"
        >
          <el-select
            v-model="formData.isFalse"
            :clearable="false"
            size="small"
            placeholder="是否误报"
            style="width: 130px"
          >
            <el-option
              v-for="(item, index) in isFalse"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="popHandleVisible = false">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
    <warning-detail
      ref="modalDetailForm"
      :id="popHistory.id"/>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  batchSaveInfos,
  exportFalseInfos,
  exportHistory,
  exportInfos,
  findBasicDataConfigByType,
  findModuleInfoList,
  findRuleAlertCount,
  roleList,
  saveFalseInfo,
  WarningFalseInfoList,
  WarningInfoList
} from '@/api/system'
import WarningDetail from '@/pages/widget/component/warningDetail'

export default {
  name: "'warning-widgetWarningFalse",
  components: { WarningDetail },
  layout: 'menuLayout',
  mixins: [listMixins],
  data() {
    return {
      popVisible: false,
      ruleNumList: {
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      popVisible1: false,
      popHistory: {
        id: null,
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      searchForm: {},
      url: {
        list: WarningFalseInfoList
      },
      tableData: [],
      moduleList: [],
      roleList: [],
      serviceList: [],
      isFalse: [
        {
          value: 1,
          label: '是'
        },
        {
          value: 0,
          label: '否'
        }
      ],
      warningTypeList: [
        // 0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        {
          value: 5,
          name: '生产过程监测'
        },
        {
          value: 6,
          name: '趋势校验报警'
        }
      ],
      isConfirm: [
        {
          value: 0,
          name: '未确认'
        },
        {
          value: 1,
          name: '确认'
        }
      ],
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ],
      statusList: [
        {
          value: 0,
          name: '未处理'
        },
        {
          value: 1,
          name: '已挂起'
        },
        {
          value: 2,
          name: '已处理'
        },
        {
          value: 3,
          name: '持续中'
        },
        {
          value: 9,
          name: '审核中'
        }
      ],
      multipleSelection: [],
      handleList: [],
      formData: {
        alertAnalysis: '',
        alertAdvice: '',
        falseFeedBack: '',
        isFalse: null
      },
      popHandleVisible: false
    }
  },
  computed: {},
  watch: {
    'searchForm.dateTime': function() {
      if (this.searchForm.dateTime && this.searchForm.dateTime.length) {
        this.searchForm.startTime = this.searchForm.dateTime[0]
        this.searchForm.endTime = this.searchForm.dateTime[1]
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    }
  },
  mounted() {
    this.loadData()
    // this.findBasicDataConfigByType()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        console.log(res)
        this.moduleList = res.data || []
      })
      post(roleList, {
        pageIndex: 1,
        pageSize: 1000,
        roleType: 2
      }).then(res => {
        this.roleList = res.data.content || []
      })
    },
    beforeHandleSearch() {
      this.searchForm.roleID &&
        (this.searchForm.roleIDs = [this.searchForm.roleID])
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content.map(item => {
        return {
          value: item.name,
          label: item.cname
        }
      })
    },
    // 报警规则分组统计
    handleRuleNum() {
      this.popVisible = true
      post(
        findRuleAlertCount,
        Object.assign({
          pageIndex: this.ruleNumList.pageIndex,
          pageSize: this.ruleNumList.pageSize,
          startTime: this.searchForm.startTime,
          endTime: this.searchForm.endTime
        })
      ).then(res => {
        this.ruleNumList.list = res ? res.data.content : []
        this.ruleNumList.pageSize = res.data.pageable.pageSize
        this.ruleNumList.totalPages = res.data.totalPages
        this.ruleNumList.total = res.data.totalElements
      })
    },

    handleRuleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.ruleNumList.pageSize = val
      this.handleRuleNum()
    },
    handleRuleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.ruleNumList.pageIndex = val
      this.handleRuleNum()
    },

    // 历史报警记录
    showHistory(id) {
      this.popHistory.id = id
      this.$refs.modalDetailForm.visible = true
      this.$refs.modalDetailForm.showHistory(id, true)
      this.$refs.modalDetailForm.showHandleHistory(id)
    },
    handlePopSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.popHistory.pageSize = val
      this.showHistory(this.popHistory.id)
    },
    handlePopCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.popHistory.pageIndex = val
      this.showHistory(this.popHistory.id)
    },
    handleHistoryExport() {
      this.loading = true
      post(
        exportHistory,
        Object.assign({ alertInfoID: this.popHistory.id }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警历史记录.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    handleWarning(data) {
      // window.parent.postMessage(
      //   {
      //     type: 'link',
      //     url: data.warningEventBody.alertUrl,
      //     title: 'data.warningEventBody.title'
      //   },
      //   '*'
      // )
    },
    getRowClass({ row }) {
      if (
        this.$moment().format('YYYY-MM-DD') ===
        this.$moment(row.createDateTime).format('YYYY-MM-DD')
      ) {
        return 'select-row'
      }
    },
    handleExport() {
      this.loading = true
      post(
        exportFalseInfos,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '报警消息.xls')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    // 处置
    handleSingle(row) {
      this.handleList = [row]
      Object.assign(this.formData, row)
      this.popHandleVisible = true
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          const params = {
            list: this.handleList.map(item => {
              item.alertAdvice = this.formData.alertAdvice
              item.alertAnalysis = this.formData.alertAnalysis
              item.falseFeedBack = this.formData.falseFeedBack
              item.isFalse = this.formData.isFalse
              item.isConfirmFalse = 1
              return item
            })
          }
          post(saveFalseInfo, params).then(res => {
            this.loading = false
            if (res.success) {
              this.popHandleVisible = false
              this.$message.success('处理成功！')
              this.handleSearch()
            } else {
              this.$message.warning('保存失败！')
            }
          })
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-operate {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
    .operate-icon {
      margin-left: 8px;
    }
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
/deep/ .select-row {
  background: #ffeff0 !important;
}
/deep/
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped.select-row
  td.el-table__cell {
  background: #ffeff0 !important;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
/deep/ .el-textarea.is-disabled .el-textarea__inner {
  color: #3a3f63;
}
</style>
