<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="search-wrapper">
        <el-form
          ref="searchForm"
          :label-width="'80px'"
          :model="searchForm"
          size="small"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label="所属应用"
            prop="userNo"
          >
            <el-select
              v-model="searchForm.serviceNo"
              placeholder="应用选择"
              style="width: 140px">
              <el-option
                v-for="item in serviceList"
                :key="item.name"
                :label="item.cname"
                :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item
            label=""
            prop="userNo"
          >
            <el-input
              v-model="searchForm.pageName"
              clearable

              size="small"
              placeholder="页面名称"
              style="width: 200px"
              suffix-icon="el-icon-search"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="开始时间"
            prop="startTime"
          >
            <el-date-picker
              v-model="searchForm.dateRange"
              :value-format="'yyyy-MM-dd'"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @input="$forceUpdate()"/>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handelExport"
        >导出
        </el-button>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handelExportDetail"
        >明细导出
        </el-button>
        <el-button
          size="small"
          @click="handleReset"
        >重置
        </el-button>
      </div>
    </div>
    <div class="page-card shadow-light">
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        class="custom-table"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"/>
        <el-table-column
          label="访问应用"
          prop="serviceNo"
          width="140"
        >
          <template v-slot="{ row }">
            {{ getServiceName(row.code).cname }}
          </template>
        </el-table-column>
        <el-table-column
          label="访问页面"
          prop="pageName"
        >
          <template v-slot="{ row }">
            {{ row.parentName }} -  {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column
          label="访问次数"
          prop="percent"
          width="280"
        >
          <template v-slot="{ row }">
            <div
              :class="getStatus(row.percent)"
              class="progress-box">
              <el-progress
                :stroke-width="18"
                :text-inside="true"
                :percentage="row.percent || 0"
                :status="getStatus(row.percent)"
                style="display: inline-block; width: 180px"/>
              <span class="progress-num">
                {{ row.num }}次
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          prop="percent"
          width="120"
        >
          <template v-slot="{ row }">
            <el-button
              slot="reference"
              type="text"
              @click="handleView(row)"
            >访问记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row
        align="middle"
        class="table-pagination"
        justify="end"
        type="flex"
      >
        <el-pagination
          :current-page="page.pageIndex"
          :page-size="page.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </div>
    <list
      ref="modalView"
      :service-list="serviceList"
      :login-time-start="searchForm.loginTimeStart"
      :login-time-end="searchForm.loginTimeEnd"/>
  </div>
 
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { findBasicDataConfigByType, pageLogSecret } from '@/api/system'
import SelectOrg from '@/components/SelectOrg'
import { post } from '@/lib/Util'
import List from '@/pages/visit10/pageLog/component/list'

export default {
  name: 'visit10-pageLog',
  components: { List, SelectOrg },
  layout: 'menuLayout',
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {},
      serviceList: [],
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: pageLogSecret.findEachPageAccessList //分页接口地址
      },
      editUserId: null,
      maxNum: 20
    }
  },
  created() {
    this.findBasicDataConfigByType()
  },
  methods: {
    //
    afterHandleSearch() {
      this.tableData = this.tableData.map(item => {
        item.percent = Number(((item.num / this.maxNum) * 100).toFixed(1))
        item.percent = item.percent > 100 ? 100 : item.percent
        return item
      })
    },
    beforeHandleSearch() {
      if (!this.searchForm.dateRange) {
        this.searchForm.dateRange = [
          this.$moment().format('yyyy-MM-DD'),
          this.$moment().format('yyyy-MM-DD')
        ]
      }
      this.searchForm.loginTimeStart = this.searchForm.dateRange[0]
      this.searchForm.loginTimeEnd = this.searchForm.dateRange[1]
    },
    getStatus(percent) {
      if (percent >= 80) {
        return 'success'
      } else if (percent >= 50) {
        return 'warning'
      } else {
        return 'exception'
      }
    },

    // 导出
    async handelExport() {
      this.beforeHandleSearch()
      post(
        pageLogSecret.exportPageExcel,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '页面访问记录(' +
            this.searchForm.loginTimeStart +
            '-' +
            this.searchForm.loginTimeEnd +
            ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.visible = false
        this.loading = false
      })
    },
    // 导出明细
    async handelExportDetail() {
      this.beforeHandleSearch()
      post(
        pageLogSecret.exportUserPageDetExcel,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '页面访问详细记录(' +
            this.searchForm.loginTimeStart +
            '-' +
            this.searchForm.loginTimeEnd +
            ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.visible = false
        this.loading = false
      })
    },
    handleView: function(row) {
      this.$refs.modalView.resourceId = row.resourceId
      this.$refs.modalView.resourceName = row.name
      this.$refs.modalView.visible = true
      this.$refs.modalView.handleSearch(true)
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.name === name) || {}
    },
    async findBasicDataConfigByType() {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
      return Promise.resolve(true)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.progress-box {
  display: flex;
  font-size: 18px;
  font-weight: bold;
  .progress-num {
    flex: 1;
    margin-left: 5px;
    text-align: right;
  }
  &.exception {
    color: #ff2855;
  }
  &.warning {
    color: #ffb243;
  }
  &.success {
    color: #19be6b;
  }
}
</style>
