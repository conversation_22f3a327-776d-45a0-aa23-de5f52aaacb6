const pkg = require('./package')
const CompressionPlugin = require('compression-webpack-plugin')
module.exports = {
  // mode: 'universal',
  // mode: 'spa',
  ssr: false,
  /*
  ** Headers of the page
  */
  head: {
    title: '板材全流程智能制造',
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: pkg.description }
    ],
    link: [{ rel: 'icon', href: 'ng.ico' }]
  },

  /*
  ** Customize the progress-bar color
  */
  // loading: { color: '#fff' },
  loading: false,

  /*
  ** Global CSS
  */
  css: [
    'element-ui/lib/theme-chalk/index.css',
    '@/assets/css/reset.css',
    '@/assets/css/theme/index.css'
  ],

  script: [{ src: '/js/iconfont.js', body: true }],

  /*
  ** Plugins to load before mounting the App
  */
  plugins: [
    '@/plugins/element-ui',
    '@/plugins/directive',
    '@/plugins/api',
    '@/plugins/filters',
    '@/plugins/i18n',
    { src: '@/plugins/interceptors', ssr: false },
    { src: '~/plugins/route', ssr: false },
    { src: '~/plugins/persistedstate.js', ssr: false },
    { src: '~/plugins/vue-echarts.js', ssr: false },
    { src: '~/plugins/vue-tools.js', ssr: false }
  ],

  /*
  ** Nuxt.js modules
  */
  modules: [
    // Doc: https://github.com/nuxt-community/axios-module#usage
    '@nuxtjs/axios',
    '@nuxtjs/style-resources',
    'nuxt-precompress'
  ],
  styleResources: {
    less: '@/assets/css/global.less'
  },

  server: {
    port: 9730 || process.env.PORT,
    host: '0.0.0.0' || process.env.BASE_URL
  },
  /*
  ** Axios module configuration
  */
  env: {
    baseUrl: 'http://************:9800/'
  },
  axios: {
    baseURL: 'http://************:9800/', //设置统一URL,关闭代理时使用.
    timeout: 60000,
    retry: { retries: 3 },
    proxy: true // 表示开启代理
  },
  proxy: {
    '/api': {
      //匹配代理对象
      target: 'http://************:9800/', // 目标接口域名
      pathRewrite: { '^/api/': '' }, // 匹配 /api 替换成 /
      changeOrigin: true, // 表示是否跨域
      logLevel: 'debug'
    }
  },
  router: {
    middleware: 'redirect'
  },
  nuxtPrecompress: {
    gzip: {
      enabled: true,
      filename: '[path].gz[query]',
      threshold: 10240,
      minRatio: 0.8,
      compressionOptions: { level: 9 }
    },
    brotli: {
      enabled: true,
      filename: '[path].br[query]',
      compressionOptions: { level: 11 },
      threshold: 10240,
      minRatio: 0.8
    },
    enabled: true,
    report: false,
    test: /\.(js|css|html|txt|xml|svg)$/,
    // Serving options
    middleware: {
      enabled: true,
      enabledStatic: true,
      encodingsPriority: ['br', 'gzip']
    }
  },
  /*
  ** Build configuration
  */
  build: {
    /*
    ** You can extend webpack config here
    */
    transpile: ['vue-grid-layout'],
    uglify: {
      uglifyOptions: {
        compress: true
      },
      cache: true
    },
    extend(config, ctx) {
      // Run ESLint on save
      if (ctx.isDev && ctx.isClient) {
        config.module.rules.push({
          enforce: 'pre',
          test: /\.(js|vue)$/,
          loader: 'eslint-loader',
          exclude: /(node_modules)/
        })
      }
    },
    plugins: [
      new CompressionPlugin({
        test: /\.js$|\.html$|\.css/, // 匹配文件名
        threshold: 10240, // 对超过10kb的数据进行压缩
        deleteOriginalAssets: false // 是否删除原文件
      })
    ],
    optimization: {
      minimize: true,
      splitChunks: {
        chunks: 'all',
        automaticNameDelimiter: '.',
        name: true,
        minSize: 10000,
        maxSize: 244000,
        cacheGroups: {
          vendor: {
            name: 'node_vendors',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            maxSize: 244000
          },
          styles: {
            name: 'styles',
            test: /\.(css|vue)$/,
            chunks: 'all',
            enforce: true
          },
          commons: {
            test: /node_modules[\\/](vue|vue-loader|vue-router|vuex|vue-meta|core-js|@babel\/runtime|axios|webpack|setimmediate|timers-browserify|process|regenerator-runtime|cookie|js-cookie|is-buffer|dotprop|nuxt\.js)[\\/]/,
            chunks: 'all',
            priority: 10,
            name: true
          }
        }
      }
    }
  }
}
