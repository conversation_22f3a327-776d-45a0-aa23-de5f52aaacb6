// 报警转隐患
<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="模块"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.moduleCode"
                size="small"
                clearable
                style="width: 130px"
                placeholder="选择模块"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.Name"
                  :value="item.ID"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警类型"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.warningType"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警类型"
              >
                <el-option
                  v-for="(item, index) in warningTypeList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警级别"
              prop="ruleName"
            >
              <el-select
                v-model="searchForm.alertLevel"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警级别"
              >
                <el-option
                  v-for="(item, index) in alertLevelList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="status"
            >
              <el-select
                v-model="searchForm.status"
                size="small"
                clearable
                style="width: 130px"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="时间"
              prop="dateTime"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                :picker-options="pickerOptions"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right"/>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">

        <el-table
          :data="tableData"
          :row-class-name="getRowClass"
          size="small"
          border
          style="width: 100%"
        >
          <el-table-column
            label="所属模块"
            prop="moduleName"
            width="75"
          >
            <!--          <template-->
            <!--            v-slot="{row}"-->
            <!--          >-->
            <!--            {{ getDict(row.serviceName, 'serviceList').label }}-->
            <!--          </template>-->
          </el-table-column>
          <el-table-column
            label="报警描述"
            prop="alertContent"
            min-width="100"
          />
          <el-table-column
            label="设备名"
            show-overflow-tooltip
            prop="deviceName"
            width="180"
          />
          <el-table-column
            label="区域名"
            prop="areaName"
            width="80"
          />
          <el-table-column
            label="报警类型"
            prop="alertTypeName"
            width="95"
          />
          <el-table-column
            label="报警等级"
            prop="alertLevel"
            width="72"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.alertLevel == 1"
                type="danger">一级</el-tag>
              <el-tag
                v-if="row.alertLevel == 2"
                type="warning">二级</el-tag>
              <el-tag
                v-if="row.alertLevel == 3">三级</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            prop="status"
            width="80"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.status == 0"
                type="danger">未处理</el-tag>
              <el-tag
                v-if="row.status == 2"
                type="success">已处理</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="转隐患时间"
            prop="createDateTime"
            width="135"
          />
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  findBasicDataConfigByType,
  findModuleInfoList,
  roleList,
  AlarmToHiddenDanger
} from '@/api/system'

export default {
  name: 'warning-WidgetAlarmToHiddenDanger',
  layout: 'menuLayout',
  mixins: [listMixins],
  data() {
    return {
      popVisible: false,
      ruleNumList: {
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      popVisible1: false,
      popHistory: {
        id: null,
        list: [],
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      },
      searchForm: {},
      url: {
        list: AlarmToHiddenDanger
      },
      tableData: [],
      moduleList: [],
      roleList: [],
      serviceList: [],
      warningTypeList: [
        // 0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        {
          value: 5,
          name: '生产过程监测'
        },
        {
          value: 6,
          name: '趋势校验报警'
        }
      ],
      isConfirm: [
        {
          value: 0,
          name: '未确认'
        },
        {
          value: 1,
          name: '确认'
        }
      ],
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ],
      statusList: [
        {
          value: 0,
          name: '未处理'
        },
        {
          value: 2,
          name: '已处理'
        }
      ]
    }
  },
  computed: {},
  watch: {
    'searchForm.dateTime': function() {
      if (this.searchForm.dateTime && this.searchForm.dateTime.length) {
        this.searchForm.startTime = this.searchForm.dateTime[0]
        this.searchForm.endTime = this.searchForm.dateTime[1]
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
    }
  },
  mounted() {
    this.loadData()
    // this.findBasicDataConfigByType()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        this.moduleList = res.data || []
      })
      post(roleList, {
        pageIndex: 1,
        pageSize: 1000,
        roleType: 2
      }).then(res => {
        this.roleList = res.data.content || []
      })
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content.map(item => {
        return {
          value: item.name,
          label: item.cname
        }
      })
    },
    handleWarning(data) {
      // window.parent.postMessage(
      //   {
      //     type: 'link',
      //     url: data.warningEventBody.alertUrl,
      //     title: 'data.warningEventBody.title'
      //   },
      //   '*'
      // )
    },
    getRowClass({ row }) {
      if (
        this.$moment().format('YYYY-MM-DD') ===
        this.$moment(row.createDateTime).format('YYYY-MM-DD')
      ) {
        return 'select-row'
      }
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-operate {
    display: flex;
    justify-content: space-between;
    padding-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
    .operate-icon {
      margin-left: 8px;
    }
  }
}
.widget-list {
  flex: 1;
  overflow: auto;
}
/deep/ .select-row {
  background: #ffeff0 !important;
}
/deep/
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped.select-row
  td.el-table__cell {
  background: #ffeff0 !important;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
