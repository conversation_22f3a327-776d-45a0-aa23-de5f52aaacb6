<template>
  <div class="login">
    <transition name="loading-fade">
      <div
        v-if="loading"
        class="loading">
        <svg
          class="circular"
          viewBox="25 25 50 50">
          <circle
            class="path"
            cx="50"
            cy="50"
            r="20"
            fill="none"/>
        </svg>
        <div class="loading-text">loading</div>
      </div>
    </transition>
    <header class="logo center">
      <img
        src="../assets/images/logo-color-replace.png"
      >
      <div>
        南京钢铁集团有限公司
      </div>
    </header>
    <div class="content">
      <nuxt/>
    </div>
    <footer class="center footerDesc">Copyright© 北京科技大学工程技术研究院</footer>
  </div>
</template>
<script>
export default {
  filters: {},
  props: {},
  data() {
    return {
      loading: true
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    // this.$Spin.hide()
    this.loading = false
  },
  methods: {}
}
</script>

<style lang="less">
.center {
  text-align: center;
}
loading-fade-enter,
loading-fade-leave-active {
  opacity: 0;
}
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  text-align: center;
  padding: 15% 0;
  background: #fff;
  transition: opacity 0.5s ease-in-out;
  .loading-text {
    color: #13356f;
    font-size: 20px;
    margin-top: 10px;
  }
  .circular {
    height: 50px;
    width: 50px;
    -webkit-animation: loading-rotate 2s linear infinite;
    animation: loading-rotate 2s linear infinite;
  }
  .path {
    -webkit-animation: loading-dash 1.5s ease-in-out infinite;
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: #13356f;
    stroke-linecap: round;
  }
  @-webkit-keyframes loading-rotate {
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes loading-rotate {
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes loading-dash {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -40px;
    }
    100% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -120px;
    }
  }
}
.login {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(../assets/images/login-bg.jpg) no-repeat fixed;
  background-size: cover;
  .logo {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 50px;
    font-size: 32px;
    letter-spacing: 2px;
    color: #fff;
    font-weight: bolder;
    img {
      height: 80px;
      margin-right: 20px;
    }
  }
  .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -56%);
    display: flex;
    align-items: center;
  }
  .ivu-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3);
  }
  .login-tip {
    font-size: 10px;
    text-align: center;
    color: #c3c3c3;
  }
  .footerDesc {
    position: absolute;
    bottom: 50px;
    left: 0;
    width: 100%;
    color: #fff;
    font-size: 18px;
    &:before {
      content: '';
      display: inline-block;
      height: 8px;
      width: 100px;
      vertical-align: middle;
      background: url('../assets/images/footer-line.png') center no-repeat;
      background-size: contain;
      margin-right: 18px;
    }
    &:after {
      content: '';
      display: inline-block;
      height: 8px;
      width: 100px;
      vertical-align: middle;
      transform: rotateY(180deg);
      background: url('../assets/images/footer-line.png');
      background-size: contain;
      margin-left: 18px;
    }
  }
  .otherWay {
    font-size: 14px;
    .inline {
      display: inline-block;
    }
    .align {
      vertical-align: middle;
    }
    .marginLeft {
      margin-left: 20px;
      float: right;
    }
  }
  .remember {
    font-size: 14px;
  }
}
.draw {
  position: fixed;
  width: 1px;
  z-index: 99999;
  line-height: 1px;
  pointer-events: none;
}

@keyframes floatOne {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translate3D(0, -20px, 0) scale(5) rotate(45deg);
  }
}
</style>
