<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="标题"
              prop="module"
            >
              <el-input
                v-model="searchForm.title"
                style="width: 100px"
                placeholder="标题" />
            </el-form-item>
            <el-form-item
              :prop="'projectTeamLeader'"
              label="内容"
            >
              <el-input
                v-model="searchForm.content"
                style="width: 100px"
                placeholder="内容" />
            </el-form-item>
            <el-form-item
              label="时间范围"
              prop="quesType"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                :type="'datetimerange'"
                :append-to-body="true"
                :format="'yyyy-MM-dd HH:mm:ss'"
                :clearable="false"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 340px"/>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="公告标题"
            prop="title"
            width="140"
          />
          <el-table-column
            label="公告内容"
            prop="content"
          />
          <el-table-column
            label="开始时间"
            prop="startTime"
          />
          <el-table-column
            label="结束时间"
            prop="endTime"
          />
          <el-table-column
            label="状态"
            prop="status">
            <template v-slot="{ row }">
              <el-tag
                v-if="row.status"
                :type="getName('statusList', row.status).type"
                disable-transitions
              >
                {{ getName('statusList', row.status).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            style="white-space: nowrap"
            width="120"
          >
            <template
              slot-scope="{row,$index}"
            >
              <template>
                <span>
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :detail="showDetail"
      :status-list="statusList"
      @success="handleSearch"
      @next="next"
      @prev="prev"
    />
    <Edit
      ref="modalForm"
      :status-list="statusList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlFindByDictCode,
  sysNoticeDelete,
  sysNoticeFind,
  sysNoticeSave
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Detail from './component/detail'

export default {
  layout: 'menuLayout',
  name: 'operation-notice',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {
        type: 2,
        noticeCategory: 2
      },
      description: '',
      url: {
        list: sysNoticeFind, //分页接口地址
        delete: sysNoticeDelete, //删除接口地址
        save: sysNoticeSave,
        getDict: dictionaryDtlFindByDictCode
      },
      moduleList: [],
      statusList: [
        {
          label: '草稿',
          type: 'info',
          value: 1
        },
        {
          label: '发布',
          type: 'success',
          value: 2
        },
        {
          label: '关闭',
          type: 'warning',
          value: 3
        }
      ],
      workList: ENUM.workList,
      timer: null,
      activeIndex: 0,
      editRow: {}
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    }
  },
  async created() {
    this.handleSearch(true)
  },
  destroyed() {},
  methods: {
    beforeHandleSearch() {
      if (this.searchForm.date && this.searchForm.date.length) {
        this.searchForm.startTime = this.searchForm.date[0]
        this.searchForm.endTime = this.searchForm.date[1]
      }
    },

    afterHandleSearch() {
      //
      this.tableData.forEach(item => {
        if (
          item.handleStatus !== 1 &&
          this.$moment().format('yyyy-MM-DD') > item.planCompleteDate
        ) {
          item.warning = true
        }
      })
    },

    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },

    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },

    next() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex < this.tableData.length - 1) {
        this.activeIndex++
      } else if (this.page.pageIndex < this.page.totalPages) {
        console.log(this.page.pageIndex)
        this.page.pageIndex++
        this.handleSearch()
        this.activeIndex = 0
      } else {
        this.$message.info('已经是最后一条了')
      }
    },
    prev() {
      if (this.activeIndex > 0) {
        this.activeIndex--
      } else if (this.page.pageIndex > 1) {
        this.page.pageIndex--
        this.handleSearch()
        this.activeIndex = this.page.pageSize - 1
      } else {
        this.$message.warning('已经是第一条了')
      }
    },
    tableRowClassName({ row }) {
      if (row.matterType === 4) {
        return 'warning-row'
      }
      return ''
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },

    weeks(date) {
      return date ? '第' + (this.$moment(date).week() + 1) + '周' : ''
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
