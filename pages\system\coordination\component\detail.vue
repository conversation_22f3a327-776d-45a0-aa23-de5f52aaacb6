<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'问题反馈详情'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-row
        :gutter="2"
        type="flex"
        align="middle">
        <el-col :span="2">
          <div
            class="btn-arrow"
            title="上一条"
            @click="prev">
            <i class="el-icon-caret-left" />
          </div>
        </el-col>
        <el-col :span="20">
          <el-form
            v-if="visible"
            ref="form"
            :model="formData"
            label-width="140px"
            size="medium"
          >
            <el-form-item
              label="事项:"
              prop="matter"
            >
              <span
                style="font-size: 22px"
                v-html="formatText(formData.matter)"/>
            </el-form-item>
            <el-form-item
              label="事项类型:"
              prop="matterType"
            >
              <el-tag
                :type="getName('typeList', formData.matterType).type"
                disable-transitions
              >{{ getName('typeList', formData.matterType).label }}
              </el-tag>
            </el-form-item>
            <el-form-item
              label="提出人单位:"
              prop="presenter"
            >
              {{ formData.presenterUnit }}
            </el-form-item>
            <el-form-item
              label="提出人:"
              prop="presenter"
            >
              {{ formData.presenter }}
            </el-form-item>
            <el-form-item
              label="责任单位:"
              prop="handleUnit"
            >

              {{ formData.handleUnit }}
            </el-form-item>
            <el-form-item
              label="责任人:"
              prop="handlePerson"
            >
              {{ formData.handlePerson }}
            </el-form-item>
            <el-form-item
              label="计划完成时间:"
              prop="planCompleteDate"
            >
              {{ formData.planCompleteDate }}
            </el-form-item>
            <el-form-item
              label="是否已处理:"
              prop="handleStatus"
            >
              <el-tag
                :type="getName('statusList', formData.handleStatus).type"
                disable-transitions
              >{{ getName('statusList', formData.handleStatus).label }}
              </el-tag>
            </el-form-item>
            <el-form-item
              label="处理结果:"
              prop="handleResults"
            >
              <span
                style="font-size: 22px"
                v-html="formatText(formData.handleResults)"/>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2">
          <div
            class="btn-arrow"
            title="下一条"
            @click="next">
            <i class="el-icon-caret-right" />
          </div>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { deleteFileByIds, saveFeedback, uploadFile } from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    },
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: saveFeedback,
        add: saveFeedback,
        file: uploadFile
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {
    statusName: function(no) {
      return this.getName('statusList', no).label
    }
  },
  watch: {
    detail: {
      deep: true,
      handler: function() {
        // console.log(this.detail)
        this.formData = this.detail
      }
    }
  },
  created() {
    // console.log('')
  },
  methods: {
    onOpen() {
      this.formData = this.detail
    },
    async handelUpload() {
      // 删除图片
      if (this.deleteIds.length) {
        const del = await post(deleteFileByIds, { ids: this.deleteIds })
      }
      //上传
      if (!this.$refs.upload.uploadFiles.length) return
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      const res = await post(this.url.file, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (res.success) {
        this.uploadFiles = res.data
        return Promise.resolve(true)
      } else {
        this.$message.warning('图片上传失败！')
        this.loading = false
        return Promise.resolve(false)
      }
      return Promise.reject(false)
    },
    httpRequest(params) {},
    async handelSubmit() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.loading = true
        const file = await this.handelUpload()
        this.handelConfirm()
      })
    },

    submitBefore() {
      if (!this.formData.attachList) this.formData.attachList = []
      this.formData.attachList.push(...this.uploadFiles, ...this.attachList)
      this.formData.anonymous = !!this.formData.anonymous
      this.formData.quesType = this.quesType
      this.formData.quesUserNo = localStorage.getItem('userId')
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getName: function(list, status) {
      console.log(
        '=====',
        status,
        this[list].find(item => item.value === status)
      )
      return this[list].find(item => item.value === status) || {}
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    },
    next() {
      this.$emit('next')
    },
    prev() {
      this.$emit('prev')
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  &:hover {
    background: #eee;
    color: #fff;
  }
}
</style>
