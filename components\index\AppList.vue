<template>
  <div class="app-wrapper">
    <hmi-dialog
      :dialog-visible="dialogVisible"
      :title="dialogTitle"
      :mini-id="miniId"
      :append-to-body="true"
      @miniBtn="miniBtn()"
      @closeFn="closeFn()"
    >
      <template v-slot:content>
        <div class="dialog-content">
          <iframe
            id="iframeContain"
            :src="projectUrl"
            name="iframeContain"
            seamless="seamless"
            frameborder="0"
            scrolling="auto"
          />
        </div>
      </template>
    </hmi-dialog>
    <div
      id="APP"
      class="app-list">
      <div class="toptop">
        <div
          id="grid"
          class="row">
          <div
            id="icnpp"
            class="col">
            <!--            <div class="conip">
              <img class="icon"><span>&nbsp;</span>
            </div>
            <div class="conip">
              <img class="icon"><span>&nbsp;</span>
            </div>
            <div class="conip">
              <img class="icon"><span>&nbsp;</span>
            </div>
            <div class="conip">
              <img class="icon"><span>&nbsp;</span>
            </div>
            <div class="conip">
              <img class="icon"><span>百度搜索</span>
            </div>
            <div class="conip">
              <img
                class="icon"
                src="https://www.github.com/favicon.ico"><span>百度搜索</span>
            </div>
            <div class="conip">
              <img
                class="icon"
                src="https://www.baidu.com/favicon.ico"><span>百度搜索</span>
            </div>-->
          </div>
        </div>
        <div
          id="drawer"
          onDragOver="allowDrop(event)"
          onclick="onDrawerClick(event)"/>

      </div>

      <!--文件夹-->
      <table
        id="drwppp"
        border="0"
        bordercolor="#ff0000"
        cellpadding="5">
        <tr height="auto">
          <td>
            <div id="drwpp">
              <div class="conip"><img class="icon"><span>&nbsp;</span></div>
              <div class="conip"><img class="icon"><span>&nbsp;</span></div>
              <div class="conip"><img class="icon"><span>&nbsp;</span></div>
              <div class="conip"><img class="icon"><span>&nbsp;</span></div>
              <div class="conip"><img class="icon"><span>&nbsp;</span></div>
              <div class="conip"><img class="icon"><span>&nbsp;</span></div>
            </div>
          </td>
        </tr>

        <tr
          height="auto"
          valign="top">
          <td>
            <hr>
            <span
              id="folderName"
              contentEditable="true"
              onkeydown="onInputKeyDown(event)">百度搜索</span></td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import Sortable from '@/lib/Sortable'
import hmiDialog from '@/components/index/HmiDialog'
import {
  getDefaultDesktop,
  findAllResourceNoPage,
  findRootResource,
  saveDesktopConfig
} from '@/api/desktop'
import { post } from '@/lib/Util'
export default {
  name: 'AppList',
  components: {
    hmiDialog
  },
  props: {
    resData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      deskTopId: '',
      dialogVisible: false,
      dialogTitle: '默认标题',
      projectUrl: 'http://*************:9202/login', //调试
      closeAppList: [],
      miniId: ''
    }
  },
  mounted() {
    this.getDefaultDesktop()
  },
  methods: {
    init(configJson) {
      var Anima = 300

      var DrawerGrid

      var drawer = document.getElementById('drawer') //抽屉

      var drwpp = document.getElementById('drwpp')

      var drwppp = document.getElementById('drwppp')

      var folderName = document.getElementById('folderName')

      var draggingFolder

      var icnpp = document.getElementById('icnpp') //第一个拖拽区

      var toptop = document.getElementById('APP') //整个html部分

      //在元素正在拖动到放置目标时触发(绑定在容器上)
      function allowDrop(event, ghostEl) {
        var target = window.FoverStart
        if (DrawerGrid && target && target.hovable) {
          if (event instanceof DragEvent)
            //判断event是否是 通过DragEvent(构造函数)生成的对象
            event.preventDefault() //阻止event默认事件
          var time = new Date().getTime()
          if (window.FoverTarget != target) {
            if (window.FoverTarget) {
              window.nullFovering()
            }
            window.foverStarTime = time
            window.FoverTarget = target
          }
          if (window.foverStarTime > 0 && time - window.foverStarTime > 500) {
            // console.log('人计有失 天算无漏')
            window.FoverTargetComitted = true
            SameGroup(1)
            ghostEl.parentNode.removeChild(ghostEl)
            toptop.appendChild(ghostEl)
            dissmissDrawer()
            // console.log(ghostEl)
            //ghostEl.style.position='absolute';
            //ghostEl.style.transform='none';
            // window.DecorateHovered();
          }
        }
      }

      window.allowDrop = allowDrop

      //笼罩层点击事件(做点击返回用)
      function onDrawerClick(event) {
        // console.log('点击笼罩层')
        dissmissDrawer()
        event.preventDefault()
        event.stopPropagation()
        window.multiDragElements = mainmultiDragElements
      }

      window.onDrawerClick = onDrawerClick
      let swapFolderTimeout
      let RemoverFolderTimeout
      let canSwapWithFolder = false
      const FOLDER_SWAP_TIMEOUT = 500

      var readMode = 0

      window.wrappedOnDownFunc = function(e) {
        // console.log('开始拖拽')
        window.nullHovering()
        window.nullFovering()
        window.onDownTarget = e.target
        // window.gridis = readMode || onDownTarget.className.indexOf('icon') < 0
        window.gridis = readMode
        //console.log('wrappedOnDownFunc', e.target, window.gridis);
        window.HoverTargetCommitted = 0
        window.FoverTargetComitted = 0
        window.FoverStart = null
        window.overFolder = null
        window.disani = 0
        //console.log(e.target)
      }
      //window.addEventListener('mousedown',window.wrappedOnDownFunc);
      //window.addEventListener('touchstart',window.wrappedOnDownFunc);
      window.addEventListener('click', wrappedOnClickFunc)

      function wrappedOnClickFunc(e) {
        e = e.target
        // console.log('wrappedOnClickFunc', e)
        if (e === icnpp || e === drwpp || e === toptop) {
          ActiveGrid.multiDrag._deselectMultiDrag()
        } else if (readMode || e.tagName == 'SPAN') {
          // console.log('触发')
          onItemClicked(e)
        }
      }

      // 键盘事件
      function onInputKeyDown(e) {
        //console.log(e);
        if (e.keyCode == 13 && e.target.id === 'folderName') {
          onDrawerClick(e)
        }
      }

      window.window.vanishable = 1

      //浏览器窗口缩放监听
      window.addEventListener('resize', wrappedResizeFunc)

      function wrappedResizeFunc(e) {
        // console.log('what')
        var devW = document.body.clientWidth //获取浏览器当前窗口宽度
        var devW = 950 //用浏览器固定高度算
        var minW = Math.min(devW, document.documentElement.clientHeight) //对比宽度和高度取最小值
        var minW = 1080 //用浏览器固定高度算
        var itemWidth = Math.floor(minW / 12) + -1 //最小宽度除以12 再向下取整 -1
        var itemCount = Math.floor(devW / itemWidth) //视图宽度/每个图片宽度 获取一行多少个
        var maxIC = 8 //最大放8个
        /*if (itemCount > maxIC) {
          console.log('超过8个')
          //处理如果超过8个
          itemWidth = Math.floor(devW / maxIC) + -1
          itemCount = maxIC
        }*/
        var item_width = itemWidth * itemCount //个数*每个的宽度
        window.itemCount = itemCount
        window.itemWidth = itemWidth
        window.iconWidth = itemWidth * 0.75 //图标的宽度
        window.imaLeft = (itemWidth - iconWidth) / 2
        window.pmaLeft = (devW - item_width) / 2
        var iconSmallWidth = itemWidth * 0.75 * 0.4
        var value = iconSmallWidth + 'px'
        var margin = (iconWidth - iconSmallWidth * 2) / 3 + 'px'
        var panelWidth = itemWidth * (itemCount - 1)
        let styleStr =
          '.conip {' +
          '            width: ' +
          itemWidth +
          'px;' +
          '            height: ' +
          (itemWidth + 12) +
          'px;' +
          '        }' +
          '        .conip.selected::after{' +
          '            width: ' +
          iconWidth +
          'px;' +
          '            height:' +
          iconWidth +
          'px;' +
          '            margin-left:' +
          imaLeft +
          'px;' +
          '        }' +
          '        .icon{' +
          '            width: ' +
          iconWidth +
          'px;' +
          '            height:' +
          iconWidth +
          'px;' +
          '            margin-left:' +
          imaLeft +
          'px;' +
          '        }' +
          '        .icon img {' +
          '            width: ' +
          value +
          ';' +
          '            height: ' +
          value +
          ';' +
          '            margin: ' +
          margin +
          ';' +
          '        }' +
          /* 删除hover事件  '        .icon:hover{' +
          '            width: ' +
          (itemWidth - 2) +
          'px;' +
          '            height: ' +
          itemWidth +
          'px;' +
          '            margin:0;' +
          '        }' +*/
          '        .toptop{' +
          /*'            margin-left:' +
          pmaLeft +
          'px;' +*/
          '            visibility:visible' +
          '        }' +
          '        #drawer{' +
          '            margin-left: -' +
          pmaLeft +
          'px;' +
          '        }' /* +
          '        #drwpp{' +
          '            width: ' +
          panelWidth +
          'px;' +
          '        }' +
          '        #drwppp{' +
          '            width:' +
          panelWidth +
          'px;' +
          '        }'  删除固定弹窗宽度让其自适应 */
        addNewStyle(styleStr)
      }

      icnpp.isPP = 1
      let times = 0
      let that = this
      //if(0)
      //创建Sortable 实例
      window.AppGrid = new Sortable(icnpp, {
        multiDrag: true,
        swapThreshold: 0.34,
        invertSwap: true,
        selectedClass: 'selected',
        animation: Anima, //切换动画时长
        ghostClass: 'blue-background-class', //切换时背景样式
        group: 'appoug',
        root: true,
        forceFallback: false,
        sort: true,
        onStart: function(evt) {
          if (evt.item.iconItem.tagName === 'DIV') {
            draggingFolder = evt.item
          } else {
            draggingFolder = null
          }
          console.log('onStart', draggingFolder)
        },
        onEnd: function(evt) {
          console.log(JSON.stringify(AppGrid.toArray()))
          //处理拖拽后的顺序
          let forArr = AppGrid.toArray()
          let newArr = []
          for (let i = 0; i < forArr.length; i++) {
            configJson[forArr[i]].indexNum = i
            // newArr[i] = configJson[forArr[i]]
          }
          // configJson = newArr
          console.log('排序后', configJson)
          // that.saveDesktopConfig(configJson)
          draggingFolder = null
        },
        onMove(evt) {
          //console.log('onMove', evt);
          //https://github.com/SortableJS/Sortable/issues/1615#issuecomment-529704348
          if (
            evt.related.iconItem.tagName === 'DIV' &&
            evt.related !== window.overFolder
          ) {
            //console.log('starting timeout')
            clearTimeout(swapFolderTimeout)
            if (RemoverFolderTimeout != null) clearTimeout(RemoverFolderTimeout)
            canSwapWithFolder = false
            window.overFolder = evt.related
            swapFolderTimeout = setTimeout(() => {
              canSwapWithFolder = true
            }, FOLDER_SWAP_TIMEOUT)
          } else if (canSwapWithFolder) {
            //window.overFolder = null;
          }

          if (evt.related === window.overFolder && !canSwapWithFolder) {
            return false
          }
        },
        onChoose(evt) {
          times++
          if (times >= 2) {
            //双击
            console.log('双击', evt.item.data.pageUrl)
            that.dialogVisible = true
            that.dialogTitle = evt.item.data.title
            that.projectUrl = evt.item.data.pageUrl
            //双击 记录最小化id 传入组件
            that.miniId = evt.item.data.url
            console.log(that.dialogVisible)
          } else {
            //超过时间 重置单击
            setTimeout(() => {
              times = 0
            }, 400)
          }
        }
      })

      var ActiveGrid = AppGrid

      window.RemoverFolder = function() {
        //clearTimeout(RemoverFolderTimeout);
        if (RemoverFolderTimeout == null)
          RemoverFolderTimeout = setTimeout(() => {
            //console.log('removed');
            window.overFolder = null
            RemoverFolderTimeout = null
          }, 800)
      }
      //初始化窗口事件
      wrappedResizeFunc()

      let thats = this
      function UpdateAppDatForList(appdata, grid, isFolder) {
        console.log('初始化数据')
        if (grid === undefined) return
        var childs = grid.childNodes,
          ln = childs.length
        for (var i = 0; i < ln; i++) {
          grid.removeChild(childs[0])
        }
        ln = appdata.length
        for (var i = 0; i < ln; i++) {
          var iI = appdata[i]
          var urls = iI.url
          var pageUrl = iI.pageUrl
          var item = document.createElement('div')
          item.className = 'conip'
          item.setAttribute('data-id', iI.indexNum)
          item.infolder = isFolder
          var iconItem
          if (urls.constructor === Array) {
            // console.log('文件夹')
            //is folder
            iconItem = document.createElement('div')
            iconItem.className = 'icon file' //公共js 判断icon有没有子元素 有的话加上fileclass
            iconItem.style.position = 'relative'
            var size = Math.min(urls.length, 4)
            for (var j = 0; j < size; j++) {
              let svg = document.createElement('svg')
              let use = document.createElement('use')
              use.setAttribute(':href', '#' + urls[j].url + '')
              svg.setAttribute('aria-hidden', true)
              svg.classList.add('icon-svg')
              svg.appendChild(use)
              svg.classList.add('icon_' + j)
              svg.setAttribute('page-url', urls[j].pageUrl)
              iconItem.appendChild(svg)
              //追加之前
              /*var iconSmallItem = document.createElement('img')
              iconSmallItem.className = 'icon_' + j
              // iconSmallItem.src = urls[j].url + '/favicon.ico'
              iconSmallItem.src = urls[j].url
              iconSmallItem.setAttribute('page-url', urls[j].pageUrl)
              // iconSmallItem.src = '/_nuxt/assets/desktop/applist/computer.png'
              iconItem.appendChild(iconSmallItem)*/
            }
          } else {
            iconItem =
              '<svg aria-hidden="true" class="icon-svg icon" page-url="' +
              pageUrl +
              '"><use href="#' +
              urls +
              '" class="icon"></use></svg>'
            //dd
            /*iconItem = document.createElement('svg')
            let use = document.createElement('use')
            use.setAttribute('href', '#' + urls + '')
            use.classList.add('icon')
            iconItem.setAttribute('aria-hidden', true)
            iconItem.classList.add('icon-svg')
            iconItem.appendChild(use)
            iconItem.classList.add('icon')
            iconItem.setAttribute('page-url', pageUrl)*/
            //dddd
            /*iconItem = document.createElement('img')
            iconItem.className = 'icon'
            iconItem.setAttribute('page-url', pageUrl)
            iconItem.src = urls + '/favicon.ico'
            iconItem.src = urls*/
          }
          // var textItem = document.createElement('span')
          // textItem.innerText = iI.title
          let textItem = '<span>' + iI.title + '</span>'
          item.innerHTML = iconItem + textItem
          // item.appendChild(iconItem)
          // item.appendChild(textItem)
          item.iconItem = iconItem
          item.textItem = textItem
          item.hovable = 1
          item.data = iI
          grid.appendChild(item)
        }
      }

      /*function UpdateAppDatForList(appdata, grid, isFolder) {
        console.log('初始化数据')
        if (grid === undefined) return
        var childs = grid.childNodes,
          ln = childs.length
        for (var i = 0; i < ln; i++) {
          grid.removeChild(childs[0])
        }
        ln = appdata.length
        for (var i = 0; i < ln; i++) {
          var iI = appdata[i]
          var urls = iI.url
          var item = document.createElement('div')
          item.className = 'conip'
          item.infolder = isFolder
          var iconItem
          if (urls.constructor === Array) {
            console.log('文件夹')
            //is folder
            iconItem = document.createElement('div')
            iconItem.className = 'icon file' //公共js 判断icon有没有子元素 有的话加上fileclass
            iconItem.style.position = 'relative'
            var size = Math.min(urls.length, 4)
            for (var j = 0; j < size; j++) {
              var iconSmallItem = document.createElement('img')
              iconSmallItem.className = 'icon_' + j
              // iconSmallItem.src = urls[j].url + '/favicon.ico'
              iconSmallItem.src = urls[j].url
              // iconSmallItem.src = '/_nuxt/assets/desktop/applist/computer.png'
              iconItem.appendChild(iconSmallItem)
            }
          } else {
            iconItem = document.createElement('img')
            iconItem.className = 'icon'
            // iconItem.src = urls + '/favicon.ico'
            iconItem.src = urls
          }
          var textItem = document.createElement('span')
          textItem.innerText = iI.title
          item.appendChild(iconItem)
          item.appendChild(textItem)
          item.iconItem = iconItem
          item.textItem = textItem
          item.hovable = 1
          item.data = iI
          grid.appendChild(item)
        }
      }*/

      function invalidateFolder(item) {
        var iI = item.data
        var urls = iI.url
        var ikonItem = item.iconItem
        var ti
        // if (ikonItem.tagName === 'IMG') {
        if (ikonItem.indexOf('svg') >= 0) {
          item.removeChild(ikonItem)
          ikonItem = document.createElement('div')
          ikonItem.className = 'icon file'
          ikonItem.style.position = 'relative'
          window.addClass(item, ' icon_h')
          item.prepend(ikonItem)
          item.iconItem = ikonItem
          ti = ''
          setTimeout(() => {
            removeClass(item, ' icon_h')
          }, 80)
        } else {
          removeClass(item, ' icon_h')
        }
        if (draggingFolder && (ti || iI.title) === '') {
          ti = draggingFolder.data.title
        }
        if (ti != undefined) {
          iI.title = ti
        }
        var childs = ikonItem.childNodes,
          ln = childs.length
        for (var i = 0; i < ln; i++) {
          ikonItem.removeChild(childs[0])
        }
        var size = Math.min(urls.length, 4)
        for (var j = 0; j < size; j++) {
          //console.log(urls[j]);
          var iconSmallItem = document.createElement('img')
          iconSmallItem.className = 'icon_' + j
          // iconSmallItem.src = urls[j].url + '/favicon.ico'
          iconSmallItem.src = urls[j].url
          ikonItem.appendChild(iconSmallItem)
        }
        // item.textItem.innerText = iI.title
        // item.textItem.innerText = urls[0].title //默认使用title第一个
        item.textItem.innerText = '新建文件夹'
      }

      //configJson
      UpdateAppDatForList(configJson, icnpp, 0)
      // UpdateAppDatForList(navData.navdata, icnpp, 0)

      window.CommitHoverTarget = function(target, toAppend) {
        var original = target.data //拖动项
        var originurl = original.url
        if (!(originurl.constructor === Array)) {
          originurl = [{ url: originurl, title: original.title }]
          original.url = originurl
        }
        //追加合并文件夹 indexNum
        toAppend.forEach(function(taI, i) {
          //console.log(taI);
          if (taI.url.constructor === Array) {
            taI.url.forEach(function(taII, j) {
              originurl.push(taII)
            })
          } else {
            //console.log('push', taI);
            originurl.push(taI)
          }
        })
        // console.log('copy done...', original)
        invalidateFolder(target)
        // console.log(2, configJson)
        //优先处理合并文件夹之后的冗余数据(处理合并文件之后 原数据还有之前数据)
        let titles = []
        for (let i = 0; i < configJson.length; i++) {
          if (configJson[i].url.constructor === Array) {
            for (let j = 0; j < configJson[i].url.length; j++) {
              titles.push(configJson[i].url[j].title)
            }
          }
        }
        for (let q = 0; q < titles.length; q++) {
          for (let t = 0; t < configJson.length; t++) {
            if (configJson[t].url.constructor != Array) {
              if (configJson[t].title === titles[q]) {
                configJson.splice(t, 1)
              }
            }
          }
        }
        // console.log('删除后', configJson)
        //合并文件夹之后indexNum会出现空缺需要重置indexNum
        // configJson.sort(sortBy('indexNum'))
        for (let i = 0; i < configJson.length; i++) {
          configJson[i].indexNum = i
        }
        // console.log('重新排序后', configJson)
      }
      window.addClass = function(elem, className) {
        if (elem.className.indexOf(className) == -1) elem.className += className
      }

      window.removeClass = function(elem, className) {
        if (elem.className.indexOf(className) != -1)
          elem.className = elem.className.replace(className, '')
      }

      window.nullHovering = function() {
        window.HoverTargetCommitted = false
        if (window.HoverTarget) {
          window.removeClass(window.HoverTarget, ' icon_h')
          window.HoverTarget = null
        }
        window.hoverStart = 0
      }

      window.nullFovering = function() {
        window.FoverTargetComitted = false
        window.foverStarTime = 0
      }

      if (window.AppGrid)
        DrawerGrid = new Sortable(document.getElementById('drwpp'), {
          multiDrag: true,
          swapThreshold: 0.34,
          invertSwap: true,
          selectedClass: 'selected',
          animation: 300,
          ghostClass: 'blue-background-class',
          onStart: function(evt) {
            //console.log('onStart');
            window.FoverStart = evt.item
          },
          onEnd: function(evt) {
            //console.log('onEnd');
            window.FoverStart = null
          }
        })

      var mainmultiDragElements = []

      var foldermultiDragElements = []

      window.multiDragElements = mainmultiDragElements

      window.onItemClicked = function(e) {
        // console.log('onItemClicked', e)
        if (e && e.parentNode) {
          var item
          var deselect = false
          var showDrawer = readMode
          if (e.className === 'icon') {
            // 正主
            item = e.parentNode
          } else if (e.tagName === 'SPAN') {
            // 名称
            item = e.parentNode
            showDrawer = true
            deselect = true
          }
          if (showDrawer && item) {
            addClass(item.iconItem, ' ihover')
            setTimeout(() => {
              removeClass(item.iconItem, ' ihover')
            }, 300)
            var jsonData = item.data
            if (jsonData && jsonData.url) {
              var urls = jsonData.url
              if (urls.constructor === Array) {
                window.FolderItem = item
                var devH = document.documentElement.clientHeight
                // console.log('openning folder...', jsonData, item)
                var colCount = Math.max(itemCount - 1, 1)
                var rowCount = Math.ceil(urls.length / colCount)
                var itemHeight = itemWidth + 32
                var rowMax = Math.floor((devH - 100) / itemHeight)
                console.log(rowMax, rowCount)
                rowCount = Math.min(rowMax, rowCount) * itemHeight

                var currentX = item.offsetLeft + itemWidth / 2 + imaLeft
                var currentY = item.offsetTop + iconWidth / 2

                var top = item.offsetTop - rowCount / 2

                var min = document.documentElement.scrollTop
                var max = min + devH - rowCount - 100

                top = Math.min(max, Math.max(min, top))
                // console.log('what')
                drwppp.style.top = top + 3 + 'px'
                drwpp.style.height = rowCount + 'px'

                // console.log(top, min, max, 'height=' + rowCount, 'devH=' + devH)
                let left = 32
                if (
                  item.offsetLeft >
                  document.documentElement.clientWidth / 2 - itemWidth / 2
                ) {
                  left = itemWidth - left
                }
                drwppp.style.left = left + 3 + 'px'
                drwppp.style.transformOrigin =
                  currentX - left + 'px ' + (currentY - top) + 'px'

                folderName.innerText = jsonData.title

                drawer.style.visibility = 'visible'
                removeClass(drwppp, ' d_hide')

                UpdateAppDatForList(urls, drwpp, 1)

                SameGroup(0)

                window.multiDragElements = foldermultiDragElements
                ActiveGrid = DrawerGrid
                deselect = false
              }
            }
            //console.log(e.parentNode.data);
          }
          if (deselect) {
            ActiveGrid.multiDrag._deselectMultiDrag()
          }
        }
      }

      window.ComitFoverTarget = function() {
        var target = window.FolderItem
        if (target) {
          var original = target.data
          var url = []
          var childs = drwpp.childNodes,
            ln = childs.length
          for (var i = 0; i < ln; i++) {
            //console.log(childs[i]);
            if (childs[i].data) {
              // ghostEl
              url.push(childs[i].data)
            }
          }
          original.url = url
          invalidateFolder(target)
          // console.log('folder commit done...', original, target)
        }
      }

      window.postDrop = function(isMultiDragisMultiDrag) {
        // console.log('postDrop', isMultiDragisMultiDrag)
        if (isMultiDragisMultiDrag) {
          foldermultiDragElements.forEach(function(multiDragElement, i) {
            mainmultiDragElements.push(multiDragElement)
          })
          foldermultiDragElements = []
        }
        window.multiDragElements = mainmultiDragElements
      }

      //关闭整个笼罩层
      function dissmissDrawer() {
        drawer.style.visibility = 'hidden' //整个笼罩层
        window.addClass(drwppp, ' d_hide') //给table增加class
        ActiveGrid = AppGrid
        if (window.FolderItem) {
          var jsonData = window.FolderItem.data
          var newTitle = folderName.innerText
          //console.log('111', DrawerGrid.dragEl);
          if (jsonData.title != newTitle) {
            jsonData.title = newTitle
            invalidateFolder(window.FolderItem)
            if (window.FoverStart == null) {
              // console.log('delay a sec and save')
            }
          }
        }
        //drwppp.style.visibility='hidden';
      }

      dissmissDrawer()

      window.printCallStack = function() {
        throw 1
      }

      window.SameGroup = function(same) {
        DrawerGrid.options.group = same ? 'appoug' : 'null'
        window._prepareGroup(DrawerGrid.options)
      }

      function addNewStyle(newStyle) {
        //获取样式文件
        var styleElement = document.getElementById('styles_js')
        if (!styleElement) {
          //新增样式文件
          styleElement = document.createElement('style')
          styleElement.type = 'text/css'
          styleElement.id = 'styles_js'
          document.getElementsByTagName('head')[0].appendChild(styleElement)
        } else {
          // 清空之前缓存样式
          styleElement.innerHTML = ''
        }
        //插入文档流
        styleElement.appendChild(document.createTextNode(newStyle))
      }

      /*------------------------------------------------------------------------*/
      //关闭默认右击事件
      window.oncontextmenu = function(event) {
        event.preventDefault()
      }
      let rightMenu = document.getElementById('rightmenu')
      let iconList = document.querySelectorAll('.icon')
      for (let i = 0; i < iconList.length; i++) {
        iconList[i].addEventListener('contextmenu', event => {
          event.preventDefault()
          //2.设置右键行为
          rightMenu.style.display = 'none' //重置已经block的菜单
          rightMenu.style.display = 'block'
          rightMenu.style.left = event.clientX + 'px'
          rightMenu.style.top = event.clientY + 'px'
        })
      }
      document.onclick = function(event) {
        rightMenu.style.display = 'none'
      }

      function deleteNode(paramId, obj, idstr) {
        if (typeof obj !== 'object' || obj === null) {
          return obj
        }
        const copy = Array.isArray(obj) ? [] : {}
        Object.keys(obj).forEach(key => {
          if (paramId !== obj[key][idstr]) {
            copy[key] = deleteNode(paramId, obj[key])
          }
        })
        return copy
      }

      Array.prototype.del = function(filter) {
        var idx = filter
        if (typeof filter == 'function') {
          for (var i = 0; i < this.length; i++) {
            if (filter(this[i], i)) idx = i
          }
        }
        this.splice(idx, 1)
      }

      // 根据indexNum对数据进行排序
      function sortBy(field) {
        return (x, y) => {
          return x[field] - y[field]
        }
      }
      function parseToDOM(htmlstring) {
        const tpl = document.createElement('template')
        tpl.innerHTML = htmlstring
        return tpl.content
      }
    },
    getDefaultDesktop() {
      //获取desktopid 关联applist 组成configjson
      post(getDefaultDesktop, {}).then(res => {
        this.deskTopId = res.data.id
      })
      post(findRootResource, {
        userNo: localStorage.getItem('userId'), //传入
        // userNo: '021179', //传入
        type: 'menu',
        serviceName: ''
      }).then(res => {
        let configJson = []
        for (let i = 0; i < res.data.length; i++) {
          if (
            res.data[i].serviceName == 'res' ||
            res.data[i].serviceName == 'idm'
          ) {
          } else {
            configJson.push({
              title: res.data[i].name,
              url: res.data[i].icon,
              pageUrl:
                'http://' +
                res.data[i].ip +
                ':' +
                res.data[i].port +
                res.data[i].url +
                '?org=redirect&token=' +
                localStorage.getItem('token') +
                '&userId=' +
                localStorage.getItem('userId'),
              indexNum: i
            })
          }
        }
        this.init(configJson)
      })
    },
    saveDesktopConfig(config) {
      post(saveDesktopConfig, {
        // config: JSON.stringify(config),
        config: config,
        userNo: '021179',
        desktopID: this.deskTopId
      }).then(res => {
        console.log(res)
      })
    },
    miniBtn() {
      this.dialogVisible = false
    },
    closeFn() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
</style>
