<template>
  <el-popover
    v-model="visible"
    placement="bottom"
    width="400"
    trigger="click">
    <div class="tree-box">
      <div class="tree-mode">
        <el-form
          ref="searchForm"
          :label-width="'80px'"
          :model="form"
          size="mini"
          inline
          @submit.native.prevent="handleSearch"
        >
          <el-input
            v-model="form.searchKey"
            size="small"
            placeholder="请输入菜单名"
            style="width: 200px;"
          />
          <el-button
            size="small"
            @click="handleSearch"
          >搜索</el-button>
          <el-button
            size="small"
            @click="show"
          >重置</el-button>
        </el-form>
      </div>

      <el-table
        v-if="showSearch"
        :data="tableData"
        :show-header="false"
        :size="'small'"
        border
        style="width: 100%"
        @row-click="nodeClick"
      >
        <el-table-column
          label="指标名称"
          prop="name"
        />
      </el-table>
      <el-tree
        v-else
        :data="treeData"
        :props="defaultProps"
        highlight-current
        node-key="id"
        @node-click="nodeClick"
      />
    </div>
    <el-input
      slot="reference"
      v-model="form.name"
      :title="form.name"
      readonly
      placeholder="请选择菜单"
    />
  </el-popover>
</template>

<script>
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'MenuSelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    treeData: {
      type: Array,
      default: () => {
        return []
      }
    },
    menuData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      mode: false,
      data: [], // 树状数据
      dataSearch: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      form: {
        id: this.value,
        name: null,
        searchKey: ''
      },
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      tableData: [],
      showSearch: false
    }
  },
  watch: {
    value: function(newVal) {
      console.log('=======', newVal)
      this.form.id = newVal
      this.getName()
    },
    'form.searchKey': function(newVal) {
      if (this.form.searchKey === '') {
        this.showSearch = false
      }
    }
  },
  created() {
    this.getName()
  },
  methods: {
    async handleSearch() {
      console.log(this.menuData)
      if (this.form.searchKey === '') {
        return this.$message.warning('请输入查询条件')
      }
      this.tableData = this.menuData.filter(
        item => item.name.indexOf(this.form.searchKey) !== -1
      )
      this.showSearch = true
      console.log(this.tableData)
    },
    // async loadNode(node, resolve) {
    //   tt data = await this.loadData(node.data.orgCode || '*********')
    //   resolve(data)
    // },
    nodeClick(data, node = null) {
      this.form.id = data.id
      this.getName()
      this.$emit('input', this.form.id)
      // 推送变化
      this.$emit('on-change', this.form.id)
      this.visible = false
    },
    /**
     * 根据后端全量列表生成树状菜单结构数据
     * @param orgList 需处理菜单列表
     * @param pid  父级id
     * @returns {*[]}
     */
    getOrgData(orgList = [], pid = '') {
      const data = []
      // 本次递归第一级菜单
      for (let item of orgList) {
        if (!item.parentOrgCode) item.parentOrgCode = ''
        if (item.parentOrgCode === pid) {
          data.push(item)
        }
      }
      // 本次递归二级菜单
      for (let item of data) {
        item.children = this.getOrgData(orgList, item.orgCode)
        if (!item.children.length) {
          delete item.children
        }
      }
      // console.log(data)
      return data
    },
    handleReset() {
      this.form.searchKey = ''
      this.showSearch = false
      this.tableData = []
    },
    show() {
      console.log(this.form.name)
    },

    getName() {
      if (!this.form.id) return (this.form.name = '')
      const match = this.menuData.find(item => item.id === this.form.id)
      this.form.name = match ? match.name : ''
    }
  }
}
</script>

<style scoped>
.tree-box {
  max-height: 300px;
  overflow: auto;
}
.tree-mode {
  margin-bottom: 8px;
}
</style>
