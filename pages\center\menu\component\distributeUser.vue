<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'分配用户'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1220px"
      v-on="$listeners"
    >
      <div class="page-content">
        <div class="search-wrapper">
          <el-form
            ref="searchForm"
            :label-width="'80px'"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="用户编号"
              prop="userNo"
            >
              <el-input
                v-model="searchForm.userName"
                clearable
                placeholder="请输入用户编号"
                style="width: 200px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              class="br"
            >
              <el-button
                icon="ios-search"
                type="primary"
                @click="handleSearch"
              >搜索
              </el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-table
              v-loading="loading"
              ref="table"
              :data="tableData"
              :size="size"
              border
              style="width: 100%"
              @select="onSelectChange"
              @select-all="onSelectChange"
            >
              <el-table-column
                type="selection"
                width="50"
              />
              <el-table-column
                label="序号"
                type="index"
                width="80"
              />
              <el-table-column
                label="员工编号"
                min-width="100"
                prop="userNo"
              />
              <el-table-column
                label="员工姓名"
                min-width="100"
                prop="userName"
              />
            </el-table>

            <el-row
              align="middle"
              class="table-pagination"
              justify="end"
              type="flex"
            >
              <el-pagination
                :current-page="page.pageIndex"
                :page-size="page.pageSize"
                :page-sizes="[10, 20, 30, 40]"
                :total="page.total"
                layout="total, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-row>
          </el-col>
          <el-col :span="12">

            <el-table
              v-loading="loading"
              ref="tableSelected"
              :data="selectedUsers"
              :size="size"
              height="484"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="80"
              />
              <el-table-column
                label="员工编号"
                min-width="100"
                prop="userNo"
              />
              <el-table-column
                label="员工姓名"
                min-width="100"
                prop="userName"
              />
              <el-table-column
                fixed="right"
                label="操作"
                width="80"
              >
                <template
                  v-slot="{row, index}"
                >
                  <el-button
                    slot="reference"
                    :style="{padding: 0}"
                    type="text"
                    @click="cancelSelect(row, index)"
                  >取消
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { relateUser, roleById, userDelete, userList } from '@/api/system'
import { post } from '@/lib/Util'

export default {
  mixins: [listMixins],
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'id'],
  data: () => {
    return {
      selectedUsers: [],
      url: {
        list: userList, //分页接口地址
        delete: userDelete //删除接口地址
      }
    }
  },
  created() {
    post(roleById, { id: this.id }).then(res => {
      this.selectedUsers = res.data.userList || []
      this.afterHandleSearch()
    })
  },
  methods: {
    afterHandleSearch() {
      // 设置已选择用户
      this.tableData.forEach((data, index) => {
        const match = this.selectedUsers.find(
          item => item.userNo === data.userNo
        )
        if (match) {
          this.$nextTick(() =>
            this.$refs.table.toggleRowSelection(this.tableData[index])
          )
        }
      })
    },
    onSelectChange(selectedRowKeys) {
      // 根据选择结果更新全量选择列表
      // 添加选中用户
      selectedRowKeys.forEach(select => {
        !this.selectedUsers.find(item => item.userNo === select.userNo) &&
          this.selectedUsers.push(select)
      })
      // 去除未选用户
      const notSelectedList = this.tableData.filter(
        item =>
          selectedRowKeys.findIndex(
            selected => selected.userNo === item.userNo
          ) === -1
      )
      notSelectedList.forEach(noSelect => {
        const matchIndex = this.selectedUsers.findIndex(
          item => item.userNo === noSelect.userNo
        )
        matchIndex !== -1 && this.selectedUsers.splice(matchIndex, 1)
      })
      // console.log('修改了', this.selectedUsers)
    },
    cancelSelect(row, index) {
      this.selectedUsers.splice(index, 1)
      const matchIndex = this.tableData.findIndex(
        item => item.userNo === row.userNo
      )
      if (matchIndex !== -1) {
        // console.log(matchIndex)
        this.$nextTick(() => {
          this.$refs.table.toggleRowSelection(this.tableData[matchIndex], false)
        })
      }
    },
    handelConfirm() {
      this.loading = true
      post(relateUser, {
        id: this.id,
        userIDs: this.selectedUsers.map(item => item.id)
      }).then(res => {
        this.loading = false
        if (res.success) {
          this.$message.success('分配成功')
          this.close()
        } else {
          this.$message.warning('分配失败')
        }
      })
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  box-shadow: 0 0 10px rgba(117, 116, 116, 0.1);
}

.search-wrapper {
  margin-bottom: 10px;
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}
</style>
