<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'编辑'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入责任人',
              trigger: 'change'
            }
          ]"
          label="责任人"
          prop="liablePersonName"
        >
          <el-input
            v-model="formData.liablePersonName"
            placeholder="请输入责任人">
            <el-button
              slot="append"
              icon="el-icon-user"
              @click="changeUser()"/>
          </el-input>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入本周工作',
              trigger: 'change'
            }
          ]"
          label="本周工作"
          prop="workDesc"
        >
          <el-input
            v-model="formData.workDesc"
            type="textarea"
            autosize
            placeholder="请输入本周工作" />
        </el-form-item>
        <el-form-item
          label="下周计划"
          prop="plan"
        >
          <el-input
            v-model="formData.plan"
            type="textarea"
            autosize
            placeholder="请输入下周计划" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
    <DistributeUser
      v-if="visibleDistribute"
      :role="editRole"
      :visible.sync="visibleDistribute"
      @getInputStr="getInputStrFn"
    />
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  dictionarySave,
  saveInfo,
  uploadFile
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'
import DistributeUser from './distributeUser.vue'

export default {
  components: { DistributeUser, ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      visibleDistribute: false,
      loading: false,
      visible: false,
      url: {
        edit: saveInfo,
        add: saveInfo,
        file: uploadFile
      },
      moduleList: ENUM.moduleList,
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    // 选择角色
    changeUser(data) {
      this.editRole = data
      this.visibleDistribute = !this.visibleDistribute
    },
    getInputStrFn(params) {
      this.formData.liablePersonName = params
    },
    httpRequest(params) {},
    submitBefore() {},
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
