<template>
  <div class="factory-overview">
    <!-- 头部标题 -->
    <div class="factory-header">
      <div class="header-bg">
        <img
          src="../../../assets/images/screen/header-bg.png"
          alt=""
          @drag.prevent
        >
        <div class="header-text">中板厂厂级总览</div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="factory-content">
      <!-- 生产概况 -->
      <div class="overview-section">
        <div class="section-title">生产概况</div>
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-title">轧制/精整</div>
            <div class="card-value">{{ productionData.rolling }}</div>
            <div class="card-unit">台</div>
          </div>
          <div class="overview-card">
            <div class="card-title">包装</div>
            <div class="card-value">{{ productionData.packaging }}</div>
            <div class="card-unit">台</div>
          </div>
          <div class="overview-card">
            <div class="card-title">作业人数</div>
            <div class="card-value">{{ productionData.operationPersonnel }}</div>
            <div class="card-unit">人</div>
          </div>
          <div class="overview-card">
            <div class="card-title">工作台</div>
            <div class="card-value">{{ productionData.workstations }}</div>
            <div class="card-unit">台</div>
          </div>
        </div>
      </div>

      <!-- 生产线状态图 -->
      <div class="production-line">
        <div class="line-diagram">
          <!-- 这里可以放置生产线的可视化图表 -->
          <div class="production-visual">
            <div
              v-for="(group, groupIndex) in equipmentGroups"
              :key="groupIndex"
              class="equipment-group">
              <div class="group-title">{{ group.name }}</div>
              <div class="equipment-list">
                <div
                  v-for="(equipment, index) in group.equipment"
                  :key="index"
                  class="equipment-item">
                  <div
                    :class="equipment.status"
                    class="equipment-icon">
                    <i class="el-icon-cpu"/>
                  </div>
                  <div class="equipment-name">{{ equipment.name }}</div>
                  <div
                    v-if="equipment.temperature"
                    class="equipment-temp">{{ equipment.temperature }}°C</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 温度监控区域 -->
      <div class="temperature-section">
        <div class="temp-gauges">
          <div
            v-for="(gauge, index) in temperatureGauges"
            :key="index"
            class="gauge-item">
            <div class="gauge-title">{{ gauge.name }}</div>
            <div
              :id="'gauge-' + index"
              class="gauge-container"/>
            <div class="gauge-value">{{ gauge.value }}°C</div>
          </div>
        </div>
      </div>

      <!-- 数据图表区域 -->
      <div class="charts-section">
        <div class="chart-row">
          <div class="chart-item">
            <div class="chart-title">产量</div>
            <div
              id="output-chart"
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">生产对时</div>
            <div
              id="production-time-chart"
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">重分单耗</div>
            <div
              id="consumption-chart"
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">卷计划</div>
            <div
              id="plan-chart"
              class="chart-container"/>
          </div>
        </div>

        <div class="chart-row">
          <div class="chart-item">
            <div class="chart-title">成材率</div>
            <div
              id="yield-chart"
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">合格率</div>
            <div
              id="pass-rate-chart"
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">公司精整和事业部重量</div>
            <div
              id="weight-chart"
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">成份合格率</div>
            <div
              id="composition-chart"
              class="chart-container"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZbcFactoryOverview',
  data() {
    return {
      productionData: {
        rolling: 50,
        packaging: 50,
        operationPersonnel: 50,
        workstations: 50
      },
      equipmentGroups: [
        {
          name: '轧制区',
          equipment: [
            { name: '轧机1', status: 'running' },
            { name: '轧机2', status: 'running' },
            { name: '轧机3', status: 'running' }
          ]
        },
        {
          name: '精整区',
          equipment: [
            { name: '精整1', status: 'running' },
            { name: '精整2', status: 'running' },
            { name: '精整3', status: 'running' }
          ]
        }
      ],
      temperatureGauges: [
        { name: '温度1', value: 78.1 },
        { name: '温度2', value: 78.1 },
        { name: '温度3', value: 78.1 },
        { name: '温度4', value: 78.1 }
      ],
      charts: {}
    }
  },
  mounted() {
    this.initCharts()
    this.initGauges()
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart && chart.dispose) {
        chart.dispose()
      }
    })
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        this.initOutputChart()
        this.initProductionTimeChart()
        this.initConsumptionChart()
        this.initPlanChart()
        this.initYieldChart()
        this.initPassRateChart()
        this.initWeightChart()
        this.initCompositionChart()
      })
    },

    initGauges() {
      this.$nextTick(() => {
        this.temperatureGauges.forEach((gauge, index) => {
          this.initGauge(index, gauge.value)
        })
      })
    },

    initGauge(index, value) {
      const chartDom = document.getElementById('gauge-' + index)
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts['gauge-' + index] = chart

      const option = {
        series: [
          {
            type: 'gauge',
            startAngle: 180,
            endAngle: 0,
            center: ['50%', '75%'],
            radius: '90%',
            min: 0,
            max: 100,
            splitNumber: 8,
            axisLine: {
              lineStyle: {
                width: 6,
                color: [
                  [0.25, '#FF6E76'],
                  [0.5, '#FDDD60'],
                  [0.75, '#58D9F9'],
                  [1, '#7CFFB2']
                ]
              }
            },
            pointer: {
              icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
              length: '12%',
              width: 20,
              offsetCenter: [0, '-60%'],
              itemStyle: {
                color: 'auto'
              }
            },
            axisTick: {
              length: 12,
              lineStyle: {
                color: 'auto',
                width: 2
              }
            },
            splitLine: {
              length: 20,
              lineStyle: {
                color: 'auto',
                width: 5
              }
            },
            axisLabel: {
              color: '#464646',
              fontSize: 20,
              distance: -60,
              rotate: 'tangential',
              formatter: function(value) {
                if (value === 87.5) {
                  return 'A+'
                } else if (value === 62.5) {
                  return 'A'
                } else if (value === 37.5) {
                  return 'B'
                } else if (value === 12.5) {
                  return 'C'
                }
                return ''
              }
            },
            title: {
              offsetCenter: [0, '-10%'],
              fontSize: 20
            },
            detail: {
              fontSize: 30,
              offsetCenter: [0, '-35%'],
              valueAnimation: true,
              formatter: function(value) {
                return Math.round(value) + '°C'
              },
              color: 'auto'
            },
            data: [
              {
                value: value,
                name: 'SCORE'
              }
            ]
          }
        ]
      }
      chart.setOption(option)
    },

    initOutputChart() {
      const chartDom = document.getElementById('output-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.output = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [120, 132, 101, 134, 90, 230, 210],
            type: 'bar',
            itemStyle: { color: '#4CAF50' }
          }
        ]
      }
      chart.setOption(option)
    },

    initProductionTimeChart() {
      const chartDom = document.getElementById('production-time-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.productionTime = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [95, 97, 94, 96, 98, 95, 97],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#2196F3' }
          }
        ]
      }
      chart.setOption(option)
    },

    initConsumptionChart() {
      const chartDom = document.getElementById('consumption-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.consumption = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [80, 85, 78, 82, 88, 85, 87],
            type: 'bar',
            itemStyle: { color: '#FF9800' }
          }
        ]
      }
      chart.setOption(option)
    },

    initPlanChart() {
      const chartDom = document.getElementById('plan-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.plan = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [75, 78, 76, 79, 82, 80, 83],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#9C27B0' }
          }
        ]
      }
      chart.setOption(option)
    },

    initYieldChart() {
      const chartDom = document.getElementById('yield-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.yield = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [88, 90, 87, 89, 92, 90, 91],
            type: 'bar',
            itemStyle: { color: '#4CAF50' }
          }
        ]
      }
      chart.setOption(option)
    },

    initPassRateChart() {
      const chartDom = document.getElementById('pass-rate-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.passRate = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [93, 95, 92, 94, 96, 94, 95],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#2196F3' }
          }
        ]
      }
      chart.setOption(option)
    },

    initWeightChart() {
      const chartDom = document.getElementById('weight-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.weight = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [85, 87, 84, 86, 89, 87, 88],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#FF5722' }
          }
        ]
      }
      chart.setOption(option)
    },

    initCompositionChart() {
      const chartDom = document.getElementById('composition-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.composition = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [91, 93, 90, 92, 94, 92, 93],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#607D8B' }
          }
        ]
      }
      chart.setOption(option)
    }
  }
}
</script>

<style scoped lang="less">
.factory-overview {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #fff;
  overflow-y: auto;
}

.factory-header {
  position: relative;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;

  .header-bg {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .header-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 28px;
      font-weight: bold;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
  }
}

.factory-content {
  padding: 20px;
}

.overview-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #fff;
  }

  .overview-cards {
    display: flex;
    gap: 20px;

    .overview-card {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .card-title {
        font-size: 14px;
        color: #ccc;
        margin-bottom: 10px;
      }

      .card-value {
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 5px;
      }

      .card-unit {
        font-size: 12px;
        color: #ccc;
      }
    }
  }
}

.production-line {
  margin-bottom: 30px;

  .line-diagram {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .production-visual {
      display: flex;
      gap: 30px;

      .equipment-group {
        flex: 1;

        .group-title {
          font-size: 16px;
          font-weight: bold;
          color: #fff;
          margin-bottom: 15px;
          text-align: center;
        }

        .equipment-list {
          display: flex;
          justify-content: space-around;
          flex-wrap: wrap;
          gap: 15px;

          .equipment-item {
            text-align: center;

            .equipment-icon {
              width: 50px;
              height: 50px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 8px;
              font-size: 20px;

              &.running {
                background: #4caf50;
                color: #fff;
              }

              &.warning {
                background: #ff9800;
                color: #fff;
              }

              &.error {
                background: #f44336;
                color: #fff;
              }
            }

            .equipment-name {
              font-size: 12px;
              color: #ccc;
              margin-bottom: 4px;
            }

            .equipment-temp {
              font-size: 10px;
              color: #fff;
              background: rgba(255, 255, 255, 0.2);
              padding: 2px 6px;
              border-radius: 10px;
              display: inline-block;
            }
          }
        }
      }
    }
  }
}

.temperature-section {
  margin-bottom: 30px;

  .temp-gauges {
    display: flex;
    gap: 20px;

    .gauge-item {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 15px;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .gauge-title {
        font-size: 14px;
        color: #fff;
        margin-bottom: 10px;
        font-weight: bold;
      }

      .gauge-container {
        height: 150px;
        width: 100%;
      }

      .gauge-value {
        font-size: 16px;
        color: #fff;
        font-weight: bold;
        margin-top: 10px;
      }
    }
  }
}

.charts-section {
  .chart-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .chart-item {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .chart-title {
        font-size: 14px;
        color: #fff;
        margin-bottom: 10px;
        text-align: center;
        font-weight: bold;
      }

      .chart-container {
        height: 200px;
        width: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .overview-cards {
    flex-wrap: wrap;

    .overview-card {
      flex: 1 1 calc(50% - 10px);
    }
  }

  .temp-gauges {
    flex-wrap: wrap;

    .gauge-item {
      flex: 1 1 calc(50% - 10px);
    }
  }

  .chart-row {
    flex-wrap: wrap;

    .chart-item {
      flex: 1 1 calc(50% - 10px);
    }
  }
}

@media (max-width: 768px) {
  .overview-cards {
    .overview-card {
      flex: 1 1 100%;
    }
  }

  .temp-gauges {
    .gauge-item {
      flex: 1 1 100%;
    }
  }

  .chart-row {
    .chart-item {
      flex: 1 1 100%;
    }
  }

  .factory-header .header-text {
    font-size: 20px;
  }

  .production-visual {
    flex-direction: column;
  }
}
</style>
