<template>
  <div>
    <el-dialog
      v-bind="$attrs"
      :title="title + '菜单'"
      :visible.sync="dialogVisible"
      @close="onClose"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="120px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="父级菜单"
          prop="parentId"
        >
          <menu-select
            :tree-data="treeData"
            :menu-data="totalMenu"
            v-model="formData.parentId"/>
        </el-form-item>
        <el-form-item
          label="菜单编码"
          prop="code"
        >
          <el-input
            v-model="formData.code"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入菜单编码"
          />
        </el-form-item>
        <el-form-item
          label="菜单名称"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入菜单名称"
          />
        </el-form-item>
        <el-form-item
          label="开发责任人"
          prop="handleDeveloper"
        >
          <user-select v-model="formData.handleDeveloper"/>
        </el-form-item>
        <el-form-item
          label="用户责任人"
          prop="handleUser"
        >
          <user-select v-model="formData.handleUser"/>
        </el-form-item>
        <el-form-item
          label="权限责任人"
          prop="handleUser"
        >
          <user-select v-model="formData.approveUser"/>
        </el-form-item>
        <el-form-item
          label="是否展示"
          prop="isShow"
        >
          <el-switch
            v-model="formData.isShow"
            :active-value="1"
            :inactive-value="0"
            active-text="展示"
            inactive-text="不展示"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="status"
        >
          <el-switch
            v-model="formData.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item
          label="菜单类型"
          prop="type"
        >
          <el-radio-group
            v-model="formData.type"
            size="medium"
            @change="handleChangeMenu"
          >
            <el-radio
              v-if="mode === 'plugin'"
              :label="'plugin'">小部件</el-radio>
            <template v-else>
              <el-radio :label="'menu'">菜单</el-radio>
              <el-radio :label="'button'">按钮</el-radio>
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="mode === 'plugin'"
          :rules=" [
            {
              required: true,
              message: '请选择小部件默认尺寸',
              trigger: 'blur'
            }
          ]"
          label="小部件默认尺寸"
          prop="pluginSize"
        >
          <el-radio-group
            v-model="formData.pluginSize"
            size="medium"
          >
            <el-radio :label="'3:2'">3×2</el-radio>
            <el-radio :label="'4:4'">4×4</el-radio>
            <el-radio :label="'6:4'">6×4</el-radio>
            <el-radio :label="'12:5'">12×5</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="formData.type === 'menu'"
          :rules=" [
            {
              required: true,
              message: '请选择菜单图标',
              trigger: 'blur'
            }
          ]"
          label="菜单图标"
          prop="icon"
        >
          <icon-png
            v-if="formData.icon"
            :icon-name="formData.icon"
            style="font-size: 35px; vertical-align: middle;background-color: #999"/>
          <IconSelect
            :type="2"
            @select="setIcon"/>
        </el-form-item>
        <el-form-item
          v-if="formData.type === 'menu'"
          :rules=" [
            {
              required: true,
              message: '请选择桌面图标',
              trigger: 'blur'
            }
          ]"
          label="桌面图标"
          prop="icon"
        >
          <icon-png
            v-if="formData.deskIcon"
            :icon-name="formData.deskIcon"
            style="font-size: 35px; vertical-align: middle"/>
          <IconSelect
            :type="1"
            @select="setDeskIcon"/>
        </el-form-item>
        <el-form-item
          label="地址"
          prop="url"
        >
          <el-input
            v-model="formData.url"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入地址"
          />
        </el-form-item>
        <el-form-item
          label="排序"
          prop="sort"
        >
          <el-input-number
            v-model="formData.sort"
            :min="1"
            :max="99"
            clearable
            controls-position="right"
            placeholder="请输入排序"/>
        </el-form-item>
        <el-form-item
          label="服务名称"
          prop="serviceName"
        >
          <el-select
            v-model="formData.serviceName"
            placeholder="请选择">
            <el-option
              v-for="item in serviceList"
              :key="item.name"
              :label="item.cname"
              :value="item.name"/>
          </el-select>
          <span
            v-if="formData.serviceName"
            class="ip-text">
            {{ serviceObj.ip }}:{{ serviceObj.port }}
          </span>
        </el-form-item>
        <el-form-item
          label="分配角色"
          prop="roles"
        >
          <el-select
            v-model="formData.roles"
            multiple
            filterable
            placeholder="请选择"
            style="width: 100%">
            <el-option
              v-for="item in roleList"
              :key="item.key"
              :label="item.label"
              :value="item.key"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { post } from '@/lib/Util'
import {
  findBasicDataConfigByType,
  resourceAdd,
  resourceEdit,
  resourceRelateRole,
  roleByRscID,
  roleListNoPage
} from '@/api/system'
import IconSelect from '@/components/IconSelect'
import IconPng from '@/components/IconPng'
import MenuSelect from '@/components/menuSelect'
import * as _ from 'lodash'
import UserSelect from '@/components/userSelect'

export default {
  components: {
    UserSelect,
    MenuSelect,
    IconPng,
    IconSelect
  },
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'editNode', 'mode', 'treeData', 'totalMenu'],
  data() {
    return {
      loading: false,
      title: '',
      editType: 'edit', // edit add
      formData: {
        parentId: '',
        code: null,
        name: null,
        isShow: 1,
        status: 1,
        type: this.mode,
        icon: '',
        deskIcon: '',
        url: null,
        sort: 1,
        serviceName: '',
        roles: [],
        pluginSize: null,
        handleDeveloper: null,
        handleUser: null,
        approveUser: null
      },
      rules: {
        code: [
          {
            required: true,
            message: '请输入菜单编码',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入菜单名称',
            trigger: 'blur'
          }
        ],
        type: [
          {
            required: true,
            message: '菜单类型不能为空',
            trigger: 'change'
          }
        ],
        url: [
          {
            required: true,
            message: '请输入地址',
            trigger: 'blur'
          }
        ],
        sort: [
          {
            required: true,
            message: '请输入排序',
            trigger: 'blur'
          }
        ],
        serviceName: [
          {
            required: true,
            message: '请输入服务名称',
            trigger: 'blur'
          }
        ]
      },
      typeOptions: [
        {
          label: '页面菜单',
          value: 'menu'
        },
        {
          label: '按钮',
          value: 'button'
        },
        {
          label: '小部件',
          value: 'plugin'
        }
      ],
      serviceList: [],
      roleList: [],
      originalRole: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set() {}
    },
    serviceObj: {
      get() {
        const match = this.serviceList.find(
          item => item.name === this.formData.serviceName
        )
        return {
          ip: match ? match.IP : '',
          port: match ? match.PORT : ''
        }
      },
      set() {}
    }
  },
  watch: {
    mode: function(value) {
      this.rules.pluginSize = [
        {
          required: value === 'plugin',
          message: '请选择默认尺寸',
          trigger: 'blur'
        }
      ]
    }
  },
  created() {
    this.findBasicDataConfigByType()
    this.getRoleList()
  },
  mounted() {},
  methods: {
    getRoles() {
      post(roleByRscID, { rscID: this.editNode.id }).then(res => {
        this.formData.roles = res.data.map(item => item.id)
        this.originalRole = res.data.map(item => item.id)
      })
    },
    onOpen() {
      this.formData.type = this.mode
      this.originalRole = []
      this.formData.roles = []
      if (this.editType === 'edit') {
        Object.assign(this.formData, _.cloneDeep(this.editNode))
        this.getRoles()
      } else if (this.editType === 'add' && this.editNode) {
        this.formData.serviceName = this.editNode.serviceName
        this.formData.url = this.editNode.url
      }
    },
    async getRoleList() {
      const roleAll = await post(roleListNoPage, { id: this.id })
      if (!roleAll.success) return
      this.roleList = roleAll.data.map(item => {
        item.label = item.roleName
        item.key = item.id
        return item
      })
    },
    onClose() {
      this.$refs['form'].resetFields()
      this.formData = {
        parentId: '',
        code: undefined,
        name: undefined,
        isShow: 1,
        status: 1,
        type: 'menu',
        icon: '',
        deskIcon: '',
        url: undefined,
        sort: 1,
        serviceName: '',
        roles: null,
        pluginSize: null,
        handleDeveloper: null,
        handleUser: null,
        approveUser: null
      }
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelConfirm() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.loading = true
        switch (this.editType) {
          case 'edit':
            const editParams = Object.assign({}, this.formData, {
              ip: this.serviceObj.ip,
              port: this.serviceObj.port,
              url: this.handleUrl(this.formData.url)
            })
            post(resourceEdit, editParams).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('修改成功')
                this.$emit('success', ['edit', editParams])
                this.saveRole(res.data)
                this.close()
              }
            })
            break
          case 'add':
            const params = Object.assign({}, this.formData, {
              parentId: this.editNode ? this.editNode.id : '',
              ip: this.serviceObj.ip,
              port: this.serviceObj.port,
              url: this.handleUrl(this.formData.url)
            })
            const data = await post(resourceAdd, params)
            this.loading = false
            if (!data.success) return this.$message.warning('新增失败！')
            this.$message.success('添加成功')
            params.id = data.data
            this.$emit('success', ['add', params])
            if (this.formData.roles && this.formData.roles.length) {
              await this.saveRole(data.data)
            }
            this.close()
            break
          default:
            this.loading = false
            break
        }
      })
    },
    async saveRole(id) {
      const addRoleIDs = this.formData.roles.filter(
        item => !this.originalRole.find(or => or === item)
      )
      const deleteRoleIDs = this.originalRole.filter(
        item => !this.formData.roles.find(or => or === item)
      )
      if (!addRoleIDs.length && !deleteRoleIDs.length) return
      // console.log(addRoleIDs, deleteRoleIDs)
      const roleRes = await post(resourceRelateRole, {
        id,
        addRoleIDs,
        deleteRoleIDs
      })
      !roleRes.success && this.$message.warning('分配角色失败！')
    },
    setIcon(name) {
      this.formData.icon = name
    },
    setDeskIcon(name) {
      this.formData.deskIcon = name
    },
    handleUrl(url) {
      return url
    },
    handleChangeMenu(value) {
      if (value === 'button' && this.editNode) {
        this.formData.url = this.editNode.url
      }
    },
    async findBasicDataConfigByType(type) {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
    }
  }
}
</script>
<style scoped>
.ip-text {
  font-size: 16px;
  margin-left: 10px;
}
</style>
