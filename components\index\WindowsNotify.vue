<template>
  <div/>
</template>

<script>
import Websocket from '@/lib/WebSocket'
import uuid from 'uuid'
import { sysNoticeFind } from '@/api/system'
import { post } from '@/lib/Util'

export default {
  name: 'WindowsNotify',
  data() {
    return {
      websocketIns: null,
      notifyList: [] // {id：***, notify: instance, data: data}
    }
  },
  mounted() {
    this.setWebsocket()
    this.addListener()
    this.getNotice()
  },
  destroyed() {
    this.close()
  },
  methods: {
    setWebsocket() {
      const userNo = localStorage.getItem('userId')
      if (userNo) {
        this.websocketIns = new Websocket(userNo)
        this.websocketIns.sendSock({}, this.handleMessage)
      }
    },
    addListener() {
      window.addEventListener('beforeunload', this.close)
      window.addEventListener('unload', this.close)
    },
    close() {
      this.websocketIns.reconnect = false
      this.websocketIns.close()
    },
    handleMessage(data) {
      // console.log(data)
      let jsonData = null
      try {
        jsonData = JSON.parse(data)
      } catch (e) {
        console.warn(e)
        jsonData = data
      }
      const h = this.$createElement
      const notifyObj = {
        id: uuid(),
        data: jsonData,
        notify: null
      }
      const type = notifyObj.data.alertType === 'info' ? 'info' : 'warning'
      const hasUrl = !!notifyObj.data.warningEventBody.alertUrl
      notifyObj.notify = this.$notify[type]({
        title: notifyObj.data.warningEventBody.title || '通知',
        message: h('div', [
          h('span', notifyObj.data.warningEventBody.msg || ''),
          h('br'),
          h(
            'el-button',
            {
              style: {
                display: hasUrl ? 'inline-block' : 'none'
              },
              props: {
                type: 'text'
              },
              on: {
                click: this.clickHandler(event, notifyObj.id)
              }
            },
            '去处理'
          )
        ]),
        duration: 4500,
        onClick: e => {
          this.closeNotify(notifyObj.id)
        }
      })
      this.notifyList.push(notifyObj)
    },
    /**
     * 根据id 关闭notify
     * @param id notify id
     */
    async closeNotify(id) {
      this.notifyList.find(item => item.id === id).notify.close()
    },
    /**
     * 去处理点击事件
     * @param e
     * @param id
     */
    clickHandler(e, id) {
      return () => {
        const notify = this.notifyList.find(item => (item.id = id))
        this.$bus.$emit('open-iframe', {
          url: notify.data.warningEventBody.alertUrl,
          ip: notify.data.warningEventBody.alertUrl,
          type: 'custom',
          name: notify.data.warningEventBody.title + '（处理）'
        })
      }
    },
    // 获取系统公告
    getNotice() {
      post(sysNoticeFind, {
        status: 2,
        // startTime: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
        // endTime: this.$moment().format('YYYY-MM-DD 23:59:59'),
        noticeCategory: 2,
        pageIndex: 1,
        pageSize: 1000
      }).then(res => {
        const currentTime = this.$moment().format('YYYY-MM-DD HH:mm:ss')
        const activeNotice = res.data.content.filter(
          item => currentTime >= item.startTime && currentTime < item.endTime
        )
        const h = this.$createElement
        activeNotice.forEach((item, index) => {
          setTimeout(() => {
            this.$notify['info']({
              title: '系统公告',
              customClass: 'sys-notice',
              message: h('div', [
                h('h3', item.title || ''),
                h('p', item.content || '')
              ]),
              duration: 0
            })
          }, 500)
        })
      })
    }
  }
}
</script>

<style scoped lang="less">
</style>
