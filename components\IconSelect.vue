<template>
  <el-popover
    v-model="visible"
    placement="bottom"
    trigger="click"
    width="420"
  >
    <div
      :class="type === 2 ? 'gray' : ''"
      class="icon-list" >
      <template v-if="showIcons.length">
        <IconPng
          v-for="item in showIcons"
          :key="item.id"
          :icon-name="item.id"
          @click="selectIcon"/>
      </template>
      <el-empty
        v-else
        description="暂无图标" />
    </div>
    <el-button slot="reference">选择图标</el-button>
  </el-popover>
</template>

<script>
import IconPng from '@/components/IconPng'
import { post } from '@/lib/Util'
import { listImg } from '@/api/system'

export default {
  components: { IconPng },
  props: {
    type: {
      type: Number,
      default: 1
    }
  },
  data: () => {
    return {
      searchKey: null,
      visible: false,
      showIcons: []
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      post(listImg, {}).then(res => {
        this.showIcons = (res.data || []).filter(
          item => item.iconType === this.type
        )
      })
    },
    selectIcon(name) {
      this.$emit('select', name)
      this.visible = false
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
/**/
.search-input {
  width: 80%;
  margin: auto;
  display: block;
  text-align: center;
}
.gray {
  background-color: #999;
}
.icon-list {
  height: 300px;
  overflow: auto;
  margin-top: 20px;

  .icon-svg {
    height: 3em;
    width: 3em;
    margin: 10px;
    cursor: pointer;
  }
}
</style>
