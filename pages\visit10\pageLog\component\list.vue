<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="resourceName + ' - 页面访问记录'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="800px"
      v-on="$listeners"
      @close="searchForm.orgCode = ''"
    >
      <el-form
        ref="searchForm"
        :label-width="'80px'"
        :model="searchForm"
        size="small"
        inline
        @keyup.enter.native="handleSearch(true)"
      >
        <el-form-item
          label=""
          prop="userNo"
        >
          <select-org
            v-model="searchForm.orgCode"
            :multiple="false"/>
        </el-form-item>
        <el-button
          :loading="loading"
          icon="ios-search"
          size="small"
          type="primary"
          @click="handleSearch(true)"
        >搜索
        </el-button>
      </el-form>
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        class="custom-table"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
        />
        <el-table-column
          label="员工编号"
          prop="userNo"
          width="100"
        />
        <el-table-column
          label="姓名"
          prop="userName"
          width="120"
        />
        <el-table-column
          label="部门"
          prop="orgAllName"
        />
        <el-table-column
          label="访问时间"
          prop="loginTime"
          width="160"
        />
      </el-table>
      <el-row
        align="middle"
        class="table-pagination"
        justify="end"
        type="flex"
      >
        <el-pagination
          :current-page="page.pageIndex"
          :page-size="page.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
      <div slot="footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { pageLogSecret } from '@/api/system'
import ListMixins from '@/mixins/ListMixins'
import BaseMixins from '@/mixins/BaseMixins'
import SelectOrg from '@/components/SelectOrg'

export default {
  components: { SelectOrg },
  mixins: [ListMixins, BaseMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: {
    loginTimeStart: {
      type: String,
      default: ''
    },
    loginTimeEnd: {
      type: String,
      default: ''
    },
    serviceList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      url: {
        list: pageLogSecret.findUserPageAccessListByResourceId
      },
      resourceId: '',
      resourceName: ''
    }
  },
  computed: {},
  created() {},
  methods: {
    beforeHandleSearch() {
      if (!this.loginTimeStart || !this.resourceId)
        return new Promise(resolve => false)
      this.searchForm.loginTimeStart = this.loginTimeStart
      this.searchForm.loginTimeEnd = this.loginTimeEnd
      this.searchForm.resourceId = this.resourceId
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.name === name) || {}
    }
  }
}
</script>
<style scoped>
.table-pagination {
  margin-top: 20px;
}
</style>
