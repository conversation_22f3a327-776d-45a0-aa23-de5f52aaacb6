<template>
  <div 
    :id="containerId" 
    style="height: 100%"/>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    unit: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '总数'
    },
    labelWidth: {
      type: Number,
      default: 50
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null,
      color: [
        '#0C75FF',
        '#FF7D00',
        '#00B42A',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc'
      ]
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      const options = {
        backgroundColor: '#fff',
        title: {
          show: false,
          text: '',
          subtext: '',
          x: 'center',
          y: 'center',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 16
          }
        },
        tooltip: {
          show: true,
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          itemHeight: 20,
          top: '2%'
        },
        series: [
          {
            type: 'pie',
            selectedMode: 'single',
            radius: ['40%', '65%'],
            center: ['50%', '55%'],
            label: {
              normal: {
                show: true
              }
            },
            data: this.chartData
          }
        ]
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    getNum(name) {
      const match = this.chartData.find(item => item.name === name)
      return match ? match.value : ''
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style scoped>
</style>
