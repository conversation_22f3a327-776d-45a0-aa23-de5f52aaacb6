<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start"
      >
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)">
            <el-form-item
              label="姓名"
              prop="userName">
              <el-input
                v-model="searchForm.userName"
                style="width: 160px"
                placeholder="请输入姓名"
                clearable/>
            </el-form-item>
            <el-form-item
              prop="orgName"
              label="组织机构">
              <el-input
                v-model="searchForm.orgName"
                style="width: 240px"
                placeholder="请输入组织机构"
                clearable/>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap"
        >
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
          <el-button
            icon="ios-search"
            size="small"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="工号"
            prop="userNo"
            width="150"
          />
          <el-table-column
            label="姓名"
            prop="userName"
            width="100"
          />
          <el-table-column
            label="组织机构"
            prop="orgName"
            width="210"
          />
          <el-table-column
            label="状态"
            prop="status"
            width="75"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.status == 0"
                type="danger">
                禁用
              </el-tag>
              <el-tag
                v-if="row.status == 1"
                type="success">
                启用
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            prop="extraRemark"
          />
          <el-table-column
            fixed="right"
            label="操作"
            style="white-space: nowrap"
            width="100"
          >
            <template v-slot="{ row }">
              <el-button
                v-if="row.status == 1"
                size="small"
                type="text"
                @click="handleEdit(row)"
              >编辑
              </el-button>
              <el-button
                v-if="row.status == 1"
                size="small"
                type="text"
                @click="handleUpdate0(row)"
              >禁用
              </el-button>
              <el-button
                v-if="row.status == 0"
                size="small"
                type="text"
                @click="handleUpdate1(row)"
              >启用
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Edit
      ref="modalForm"
      :status-list="statusList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import Edit from './component/edit'
import {
  eliminatedPersonnelListFind,
  eliminatedPersonnelListSave,
  eliminatedPersonnelListUpdate
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'

export default {
  layout: 'menuLayout',
  name: 'operation-eliminatedPersonnelList',
  components: {
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      description: '',
      url: {
        list: eliminatedPersonnelListFind, //分页接口地址
        save: eliminatedPersonnelListSave
      },
      moduleList: [],
      statusList: [
        {
          label: '驳回',
          type: 'info',
          value: 9
        },
        {
          label: '待审批',
          type: '',
          value: 0
        },
        {
          label: '已审批，待处理',
          type: 'warning',
          value: 1
        },
        // {
        //   label: '待处理',
        //   type: 'warning',
        //   value: 2
        // },
        {
          label: '已处理',
          type: 'success',
          value: 3
        }
      ],
      workList: ENUM.workList,
      timer: null,
      activeIndex: 0,
      editRow: {},
      userNo: '',
      page: {
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      }
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    }
  },
  async created() {
    this.userNo = localStorage.getItem('userNo')
  },
  destroyed() {},
  methods: {
    afterHandleSearch() {},
    getName: function(list, status) {
      return this[list].find(item => item.value === status) || {}
    },
    //禁用
    handleUpdate0(row) {
      post(eliminatedPersonnelListUpdate, {
        id: row.id,
        status: 0
      }).then(res => {
        if (res.success) {
          this.$message.warning('操作成功！')
          this.handleSearch()
        } else {
          this.$message.warning('操作失败！')
        }
      })
    },
    //启用
    handleUpdate1(row) {
      post(eliminatedPersonnelListUpdate, {
        id: row.id,
        status: 1
      }).then(res => {
        if (res.success) {
          this.$message.warning('操作成功！')
          this.handleSearch()
        } else {
          this.$message.warning('操作失败！')
        }
      })
    },
    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    getInputStrFn(params) {
      this.formData.liablePersonName = params
    },
    tableRowClassName({ row }) {
      return ''
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    weeks(date) {
      return date ? '第' + (this.$moment(date).week() + 1) + '周' : ''
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
