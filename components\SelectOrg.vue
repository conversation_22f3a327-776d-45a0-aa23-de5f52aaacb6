<template>
  <el-popover
    v-model="visible"
    placement="bottom"
    width="400"
    trigger="click">
    <div class="tree-box">
      <div
        v-if="multiple"
        class="tree-mode">
        选择模式
        <el-select
          v-model="mode"
          size="small"
          placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"/>
        </el-select>
        <el-button
          type="text"
          @click="handleReset"
        >重置</el-button>
        <el-button
          type="text"
          @click="visible = false"
        >关闭</el-button>
      </div>
      <el-tree
        ref="orgTree"
        :data="data"
        :props="defaultProps"
        :check-strictly="mode"
        :show-checkbox="multiple"
        node-key="orgCode"
        @node-click="nodeClick"
        @check-change="checkChange"
      />
    </div>

    <div
      slot="reference"
      class="inp-wrapper">
      <el-input
        :value="form.orgName"
        :title="form.orgName"
        readonly
        placeholder="请选择机构"
      />
      <i 
        class="icon el-icon-circle-close"
        @click.stop="clear()"/>
    </div>
  </el-popover>
</template>

<script>
import { post } from '@/lib/Util'
import { allOrgList, allOrgListByCode } from '@/api/system'
import { ENUM } from '@/lib/Constant'

export default {
  name: 'SelectOrg',
  props: {
    multiple: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      mode: false,
      options: [
        {
          value: false,
          label: '联动选择'
        },
        {
          value: true,
          label: '独立选择'
        }
      ],
      data: [], // 树状数据
      allData: [], // 全部机构数据
      defaultProps: {
        children: 'children',
        label: 'orgAllName',
        isLeaf: 'leaf'
      },
      form: {
        orgCode: null,
        orgName: null
      }
    }
  },
  watch: {
    value: {
      handler: function(newValue, oldValue) {
        this.valueChange(newValue)
      },
      immediate: true
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      // console.log(localStorage.getItem('userDetail'))
      const data = await post(allOrgList, {})
      this.allData = data.data
      this.data = this.getOrgData(data.data, 'X').filter(
        item => ENUM.orgTop.indexOf(item.orgCode) !== -1
      )
      this.value && this.valueChange(this.value)
    },
    // async loadNode(node, resolve) {
    //   const data = await this.loadData(node.data.orgCode || '*********')
    //   resolve(data)
    // },
    valueChange(newValue) {
      console.log(newValue, '编号')
      const match = this.allData.find(item => item.orgCode === newValue)
      this.form.orgName = match ? match.orgAllName : ''
    },
    checkChange(selected) {
      const checked = this.$refs.orgTree.getCheckedNodes()
      this.form.orgCode = checked.map(item => item.orgCode)
      this.form.orgName = checked.map(item => item.orgAllName).join('、')
      // 推送变化
      this.$emit('on-change', this.form.orgCode)
    },
    // 树节点点击
    nodeClick(data) {
      this.form.orgCode = data.orgCode
      this.form.orgName = data.orgAllName
      console.log('dianjile', data)
      this.visible = false
      this.$emit('on-change', this.form.orgCode)
      this.$emit('input', this.form.orgCode)
    },
    clear() {
      this.form.orgCode = ''
      this.form.orgName = ''
      this.$emit('on-change', this.form.orgCode)
      this.$emit('input', this.form.orgCode)
    },
    /**
     * 根据后端全量列表生成树状菜单结构数据
     * @param orgList 需处理菜单列表
     * @param pid  父级id
     * @returns {*[]}
     */
    getOrgData(orgList = [], pid = '') {
      const data = []
      // 本次递归第一级菜单
      for (let item of orgList) {
        if (!item.parentOrgCode) item.parentOrgCode = ''
        if (item.parentOrgCode === pid) {
          data.push(item)
        }
      }
      // 本次递归二级菜单
      for (let item of data) {
        item.children = this.getOrgData(orgList, item.orgCode)
        if (!item.children.length) {
          delete item.children
        }
      }
      // console.log(data)
      return data
    },
    handleReset() {
      this.$refs.orgTree.setCheckedKeys([])
    }
  }
}
</script>

<style scoped lang="less">
.tree-box {
  height: 300px;
  overflow: auto;
}
.tree-mode {
  margin-bottom: 8px;
}
.inp-wrapper {
  position: relative;
  &:hover {
    .icon {
      opacity: 1;
    }
  }
  .icon {
    opacity: 0;
    position: absolute;
    right: 1px;
    height: 30px;
    width: 20px;
    background-color: #fff;
    line-height: 30px;
    top: 2px;
    font-size: 16px;
    color: #999;
    cursor: pointer;
    border-radius: 4px;
  }
}
</style>
