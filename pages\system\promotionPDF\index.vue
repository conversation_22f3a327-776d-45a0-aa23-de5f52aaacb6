<template>
  <div class="page-content">
    <div
      class="page-card shadow-light">
      <el-table
        v-loading="loading"
        :data="serviceList"
        :size="size"
        class="custom-table"
        border
        style="width: 100%; background-color: #eee"
      >
        <el-table-column
          label="序号"
          type="index"
          width="180"
        />
        <el-table-column
          label="服务名称"
          prop="cname"/>
        <el-table-column
          label="服务编号"
          prop="name"/>
        <el-table-column
          label="操作手册"
          prop="name">
          <template
            v-slot="{row}"
          >
            <template v-for="item in row.videoIds">
              <i
                :key="item.id"
                :title="item.name"
                class="play-icon el-icon-reading"
                @click="showVideo(item.id)"/>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="270"
        >
          <template
            v-slot="{row}"
          >
            <span>
              <el-button
                slot="reference"
                type="text"
                @click="handleEdit(row)"
              >修改
              </el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Edit
      ref="modalForm"
      :related-id="relatedId"
      @success="handleSearch"
      @show="showVideo"
    />
    <el-dialog
      v-el-drag-dialog
      v-if="popVisible"
      :title="'预览操作手册'"
      :top="'20px'"
      :width="'1200px'"
      :visible.sync="popVisible"
      v-bind="$attrs"
      :destroy-on-close="true"
      v-on="$listeners"
    >
      <pdf-view :id="popVideoId"/>
    </el-dialog>
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import pdf from 'vue-pdf'
import {
  attachFindAllByMultiCondition,
  deleteImg,
  downloadFileById,
  findBasicDataConfigByType,
  listImg
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import uuid from 'uuid'
import { imgIp } from '@/config'
import { pdfIp } from '~/config'
import PdfView from '~/components/pdfView'

export default {
  layout: 'menuLayout',
  name: 'system-promotionPdf',
  components: {
    PdfView,
    Edit,
    pdf
  },
  mixins: [listMixins],
  data: () => {
    return {
      relatedId: '49d9a2cc-9b4a-4f7c-987a-4e3271c46884',
      popVisible: false,
      popVideoId: '',
      serviceList: []
    }
  },
  computed: {
    showSrc: function() {
      return pdfIp + 'attach/downloadFileById/' + this.popVideoId
    }
  },
  async created() {
    await this.findBasicDataConfigByType()
    this.handleSearch()
  },
  methods: {
    async handleSearch(reset = false) {
      post(attachFindAllByMultiCondition, {
        relatedId: this.relatedId
      }).then(res => {
        this.serviceList = this.serviceList.map(item => {
          const match = res.data.filter(file => file.serviceNo === item.name)
          item.videoIds = match
          return item
        })
        console.log(this.serviceList)
      })
    },
    showVideo(id) {
      this.popVideoId = id
      this.popVisible = true
    },
    async findBasicDataConfigByType() {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            itemType.videoIds = []
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.gray {
  background-color: #999;
}
.play-icon {
  font-size: 26px;
  cursor: pointer;
  margin-right: 10px;
}
.video-box {
  padding-top: 55%;
  position: relative;
  video {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
  }
}
</style>
