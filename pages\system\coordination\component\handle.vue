<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'处理' + (quesType === 1 ? '合理化建议' : '问题反馈')"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入协调事项',
              trigger: 'change'
            }
          ]"
          label="标题"
          prop="matter"
        >
          <el-input
            v-model="formData.matter"
            :rows="6"
            type="textarea"
            placeholder="请输入协调事项" />
          {{ formData.matter }}
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择事项类型',
              trigger: 'change'
            }
          ]"
          label="事项类型"
          prop="matterType"
        >
          <template
            v-for="(item) in typeList">
            <template v-if="formData.matterType === item.value"> {{ item.label }}</template>
          </template>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入提出人',
              trigger: 'blur'
            }
          ]"
          label="提出人"
          prop="presenter"
        >
          <el-input
            v-model="formData.presenter"
            placeholder="请输入提出人" />
          {{ formData.handleUnit }}
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入责任单位',
              trigger: 'blur'
            }
          ]"
          label="责任单位"
          prop="handleUnit"
        >
          {{ formData.handleUnit }}
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入责任人',
              trigger: 'blur'
            }
          ]"
          label="责任人"
          prop="handlePerson"
        >
          {{ formData.handlePerson }}
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择计划完成时间',
              trigger: 'blur'
            }
          ]"
          label="计划完成时间"
          prop="planCompleteDate"
        >
          {{ formData.planCompleteDate }}
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入处理说明',
              trigger: 'change'
            }
          ]"
          label="处理意见"
          prop="handlerDesc"
        >
          <el-input
            v-model="formData.handlerDesc"
            :rows="6"
            type="textarea"
            placeholder="请输入描述"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelSubmit"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { deleteFileByIds, saveFeedback, uploadFile } from '@/api/system'
import { post } from '@/lib/Util'
import ImgDownload from '@/components/ImgView'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    serviceList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: saveFeedback,
        add: saveFeedback,
        file: uploadFile
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    async handelUpload() {
      // 删除图片
      if (this.deleteIds.length) {
        const del = await post(deleteFileByIds, { ids: this.deleteIds })
      }
      //上传
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      const res = await post(this.url.file, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (res.success) {
        this.uploadFiles = res.data
        return Promise.resolve(true)
      }
      return Promise.reject(false)
    },
    httpRequest(params) {},
    async handelSubmit() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.loading = true
        this.handelConfirm()
      })
    },

    submitBefore() {
      if (!this.formData.attachList) this.formData.attachList = []
      this.formData.anonymous = !!this.formData.anonymous
      this.formData.quesType = this.quesType
      this.formData.handUserNo = localStorage.getItem('userId')
      this.formData.handlerStatus = 2
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
