<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label=""
              prop="errorInfo"
            >
              <el-input
                v-model="searchForm.errorInfo"
                clearable
                size="small"
                placeholder="输入问题描述"
                suffix-icon="el-icon-search"
                style="width: 200px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="责任部门"
              prop="departmentName">
              <select-org
                v-model="searchForm.departmentName"
                :multiple="false"/>
            </el-form-item>
            <el-form-item
              label=""
              prop="dutyPerson"
            >
              <el-input
                v-model="searchForm.dutyPerson"
                clearable
                size="small"
                placeholder="输入责任人"
                suffix-icon="el-icon-search"
                style="width: 160px"
                type="text"
              />
            </el-form-item>

            <!--            <el-form-item
              label="提出时间"
              prop="startTime"
            >
              <el-date-picker
                v-model="searchForm.dateRange"
                :value-format="'yyyy-MM-dd'"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 215px"
                @input="$forceUpdate()"/>
            </el-form-item>-->
            <el-form-item
              label="模块"
              prop="serviceName"
            >
              <el-select
                v-model="searchForm.serviceName"
                :style="{width: '160px'}"
                size="small"
                clearable
                placeholder="选择模块"
              >
                <el-option
                  v-for="(item, index) in serviceList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="status"
            >
              <el-select
                v-model="searchForm.status"
                :style="{width: '100px'}"
                size="small"
                clearable
                placeholder="选择状态"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>

          <!--          <el-button
            icon="ios-search"
            size="small"
            type="primary"
            @click="importDateVisible = true"
          >导出
          </el-button>-->
        </div>
      </div>

      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="模块名称"
            prop="serviceName"
            min-width="100"/>
          <el-table-column
            label="功能名称"
            prop="functionName"
            min-width="100"/>
          <el-table-column
            label="问题描述"
            prop="errorInfo"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            label="责任部门"
            prop="departmentName"
            show-overflow-tooltip
          />
          <el-table-column
            label="责任人"
            prop="dutyPerson"
            show-overflow-tooltip
          />
          <el-table-column
            label="原因分析"
            prop="causeAnalysis"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            label="处置优化措施"
            prop="handle"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            label="计划完成时间"
            prop="planFinishTime"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="实际完成时间"
            prop="actFinishTime"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="状态"
            prop="status"
            width="80px"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="getName('statusList', row.status).type"
                disable-transitions
              >{{ getName('statusList', row.status).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="80px"
          >
            <template
              v-slot="{row}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail(row)"
                >详情
                </el-button>
              </span>
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleEdit(row)"
                >编辑
                </el-button>
              </span>
              <!--              <template v-else>
                <span v-if="row.status == 0">
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handlePop(row)"
                  >处理
                  </el-button>
                </span>
              </template>-->
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :service-list="serviceList"
      :type-list="typeList"
      :ques-type="searchForm.quesType"
      @success="handleSearch"
    />
    <Edit
      ref="modalForm"
      :service-list="serviceList"
      :type-list="typeList"
      :ques-type="searchForm.quesType"
      @success="handleSearch"
    />
    <Handle
      ref="modalHandleForm"
      :service-list="serviceList"
      :type-list="typeList"
      :ques-type="searchForm.quesType"
      @success="handleSearch"
    />
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'600px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="问题反馈统计">
      <template v-slot:title>
        <div class="custom-dialog-title">
          问题反馈统计
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导出日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"
            type="daterange"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            :loading="loading"
            type="primary"
            @click="handleStatisticsExport()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlFindByDictCode,
  exportUserFeedBackExcel,
  errorRecord,
  errorRecordSave
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Handle from '@/pages/system/faultledger/component/handle'
import Detail from '@/pages/system/faultledger/component/detail'
import Timer from '@/pages/system/faultledger/component/timer'
import SelectOrg from '@/pages/system/faultledger/component/SelectOrg'

export default {
  layout: 'menuLayout',
  name: 'system-faultledger',
  components: {
    SelectOrg,
    Timer,
    Detail,
    Handle,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      importDateVisible: false,
      importDate: null,
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: errorRecord, //分页接口地址
        // delete: delFeedback, //删除接口地址
        save: errorRecordSave,
        getDict: dictionaryDtlFindByDictCode
      },
      userId: null,
      roleStatusList: ENUM.roleStatus,
      editRole: null,
      serviceList: [],
      typeList: [{ label: '建议', value: 1 }, { label: 'BUG', value: 2 }],
      statusList: [
        { label: '完成', value: 1, type: 'success' },
        { label: '未完成', value: 0, type: 'danger' }
      ],
      timer: null
    }
  },
  created() {
    this.searchForm.quesType = parseInt(this.$route.params.id)
    this.handleSearch(true)
    this.getServiceInfo()
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    afterHandleSearch(tableData) {
      this.updateDiff()
    },
    beforeHandleSearch() {
      this.searchForm.startDate = this.searchForm.dateRange
        ? this.searchForm.dateRange[0]
        : ''
      this.searchForm.endDate = this.searchForm.dateRange
        ? this.searchForm.dateRange[1]
        : ''
    },
    updateDiff() {
      this.tableData.forEach(item => {
        if (
          item.handleStatus === 1 &&
          this.$moment().format('yyyy-MM-DD') > item.planFinishDate
        ) {
          item.warning = true
        }
        const diff = this.$moment().diff(
          this.$moment(item.createDateTime),
          'seconds'
        )
        item.diff = diff
        if (item.handleTime) {
          item.handleDiff = this.$moment(item.handleTime).diff(
            this.$moment(item.createDateTime),
            'seconds'
          )
        }
      })
    },
    getName: function(list, status) {
      return this[list].find(item => item.value === status) || {}
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.value === name) || {}
    },

    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
      this.$refs.modalForm.attachList = row.attachList || []
    },

    // 详情
    handleDetail: function(row) {
      this.$refs.modalDetailForm.edit(row)
      this.$refs.modalDetailForm.visible = true
      this.$refs.modalDetailForm.attachList = row.attachList || []
    },

    // 处理
    handlePop: function(row) {
      this.$refs.modalHandleForm.edit(row)
      this.$refs.modalHandleForm.visible = true
      this.$refs.modalHandleForm.attachList = row.attachList || []
    },

    // 催办
    handleRemind: function(row) {
      this.$confirm(`是否确认催办此条记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 催办操作
        post(
          this.url.save,
          Object.assign({}, row, {
            handleStatus: 3
          })
        ).then(() => {
          this.handleSearch()
        })
      })
    },
    // 关闭
    handleClose: function(row) {
      this.$confirm(`是否确认关闭此条记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 催办操作
        post(
          this.url.save,
          Object.assign({}, row, {
            handleStatus: 4
          })
        ).then(() => {
          this.handleSearch()
        })
      })
    },

    async getServiceInfo() {
      const { data: module } = await post(this.url.getDict, {
        dictCode: 'service'
      })
      this.serviceList = module.map(item => {
        return {
          value: item.code,
          label: item.value
        }
      })
      return new Promise(resolve => resolve(true))
    },
    async handleStatisticsExport() {
      this.loading = true
      post(
        exportUserFeedBackExcel,
        Object.assign(
          {},
          {
            startDate: this.importDate[0],
            endDate: this.importDate[1]
          },
          { pageSize: 10000 }
        ),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '问题反馈统计(' +
            this.importDate[0] +
            '-' +
            this.importDate[1] +
            ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
