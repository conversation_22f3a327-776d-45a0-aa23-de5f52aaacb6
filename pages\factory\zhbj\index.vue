<template>
  <div class="container">
    <!-- 垫片：高度为48px -->
    <div style="height: 48px; background: #000;"/>
    <!-- 标题部分 -->
    <Header :background-url="require('../../../assets/desktop/zhbj/nav.png')" />
  </div>
</template>

<script>
import Header from '../common/header.vue'

export default {
  name: 'ZhbjFactoryOverview',
  components: {
    Header
  }
}
</script>

<style scoped lang="less">
.container {
  width: 100%;
  height: 100vh;
  background: #f5f5fa;
  color: #000;
  overflow-y: auto;
}
</style>
