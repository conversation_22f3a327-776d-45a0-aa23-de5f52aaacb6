<template>
  <div class="factory-overview">
    <!-- 头部标题 -->
    <div class="factory-header">
      <div class="header-bg">
        <img
          src="../../../assets/images/screen/header-bg.png"
          alt=""
          @drag.prevent
        >
        <div class="header-text">中厚板卷厂厂级总览</div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="factory-content">
      <!-- 生产概况 -->
      <div class="overview-section">
        <div class="section-title">生产概况</div>
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-title">产量</div>
            <div class="card-value">{{ productionData.output }}</div>
            <div class="card-unit">吨</div>
          </div>
          <div class="overview-card">
            <div class="card-title">设备人数</div>
            <div class="card-value">{{ productionData.equipmentPersonnel }}</div>
            <div class="card-unit">人</div>
          </div>
          <div class="overview-card">
            <div class="card-title">作业人数</div>
            <div class="card-value">{{ productionData.operationPersonnel }}</div>
            <div class="card-unit">人</div>
          </div>
          <div class="overview-card">
            <div class="card-title">工作台</div>
            <div class="card-value">{{ productionData.workstations }}</div>
            <div class="card-unit">台</div>
          </div>
        </div>
      </div>

      <!-- 生产线状态图 -->
      <div class="production-line">
        <div class="line-diagram">
          <!-- 这里可以放置生产线的可视化图表 -->
          <div class="production-visual">
            <div 
              v-for="(equipment, index) in equipmentList" 
              :key="index" 
              class="equipment-item">
              <div 
                :class="equipment.status" 
                class="equipment-icon">
                <i class="el-icon-cpu"/>
              </div>
              <div class="equipment-name">{{ equipment.name }}</div>
              <div 
                v-if="equipment.temperature" 
                class="equipment-temp">{{ equipment.temperature }}°C</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据图表区域 -->
      <div class="charts-section">
        <div class="chart-row">
          <div class="chart-item">
            <div class="chart-title">产量</div>
            <div 
              id="output-chart" 
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">原钢第一次合格率</div>
            <div 
              id="quality-chart" 
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">重点品种生产</div>
            <div 
              id="variety-chart" 
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">卷计划</div>
            <div 
              id="plan-chart" 
              class="chart-container"/>
          </div>
        </div>

        <div class="chart-row">
          <div class="chart-item">
            <div class="chart-title">成材率</div>
            <div 
              id="yield-chart" 
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">合格率</div>
            <div 
              id="pass-rate-chart" 
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">工艺命中率</div>
            <div 
              id="process-chart" 
              class="chart-container"/>
          </div>
          <div class="chart-item">
            <div class="chart-title">成份合格率</div>
            <div 
              id="composition-chart" 
              class="chart-container"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZhbjFactoryOverview',
  data() {
    return {
      productionData: {
        output: '2520367604',
        equipmentPersonnel: 11,
        operationPersonnel: 7,
        workstations: 50
      },
      equipmentList: [
        { name: '设备1', status: 'running', temperature: 96 },
        { name: '设备2', status: 'running' },
        { name: '设备3', status: 'running' },
        { name: '设备4', status: 'running' },
        { name: '设备5', status: 'running' },
        { name: '设备6', status: 'running' },
        { name: '设备7', status: 'running' },
        { name: '设备8', status: 'running' }
      ],
      charts: {}
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart && chart.dispose) {
        chart.dispose()
      }
    })
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        this.initOutputChart()
        this.initQualityChart()
        this.initVarietyChart()
        this.initPlanChart()
        this.initYieldChart()
        this.initPassRateChart()
        this.initProcessChart()
        this.initCompositionChart()
      })
    },

    initOutputChart() {
      const chartDom = document.getElementById('output-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.output = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [120, 132, 101, 134, 90, 230, 210],
            type: 'bar',
            itemStyle: { color: '#4CAF50' }
          }
        ]
      }
      chart.setOption(option)
    },

    initQualityChart() {
      const chartDom = document.getElementById('quality-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.quality = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [95, 97, 94, 96, 98, 95, 97],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#2196F3' }
          }
        ]
      }
      chart.setOption(option)
    },

    initVarietyChart() {
      const chartDom = document.getElementById('variety-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.variety = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [80, 85, 78, 82, 88, 85, 87],
            type: 'bar',
            itemStyle: { color: '#FF9800' }
          }
        ]
      }
      chart.setOption(option)
    },

    initPlanChart() {
      const chartDom = document.getElementById('plan-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.plan = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [75, 78, 76, 79, 82, 80, 83],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#9C27B0' }
          }
        ]
      }
      chart.setOption(option)
    },

    initYieldChart() {
      const chartDom = document.getElementById('yield-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.yield = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [88, 90, 87, 89, 92, 90, 91],
            type: 'bar',
            itemStyle: { color: '#4CAF50' }
          }
        ]
      }
      chart.setOption(option)
    },

    initPassRateChart() {
      const chartDom = document.getElementById('pass-rate-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.passRate = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [93, 95, 92, 94, 96, 94, 95],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#2196F3' }
          }
        ]
      }
      chart.setOption(option)
    },

    initProcessChart() {
      const chartDom = document.getElementById('process-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.process = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [85, 87, 84, 86, 89, 87, 88],
            type: 'bar',
            itemStyle: { color: '#FF5722' }
          }
        ]
      }
      chart.setOption(option)
    },

    initCompositionChart() {
      const chartDom = document.getElementById('composition-chart')
      if (!chartDom) return

      const chart = this.$echarts.init(chartDom)
      this.charts.composition = chart

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [91, 93, 90, 92, 94, 92, 93],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#607D8B' }
          }
        ]
      }
      chart.setOption(option)
    }
  }
}
</script>

<style scoped lang="less">
.factory-overview {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #fff;
  overflow-y: auto;
}

.factory-header {
  position: relative;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;

  .header-bg {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .header-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 28px;
      font-weight: bold;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
  }
}

.factory-content {
  padding: 20px;
}

.overview-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #fff;
  }

  .overview-cards {
    display: flex;
    gap: 20px;

    .overview-card {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .card-title {
        font-size: 14px;
        color: #ccc;
        margin-bottom: 10px;
      }

      .card-value {
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 5px;
      }

      .card-unit {
        font-size: 12px;
        color: #ccc;
      }
    }
  }
}

.production-line {
  margin-bottom: 30px;

  .line-diagram {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .production-visual {
      display: flex;
      justify-content: space-around;
      align-items: center;
      flex-wrap: wrap;
      gap: 15px;

      .equipment-item {
        text-align: center;

        .equipment-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 8px;
          font-size: 24px;

          &.running {
            background: #4caf50;
            color: #fff;
          }

          &.warning {
            background: #ff9800;
            color: #fff;
          }

          &.error {
            background: #f44336;
            color: #fff;
          }
        }

        .equipment-name {
          font-size: 12px;
          color: #ccc;
          margin-bottom: 4px;
        }

        .equipment-temp {
          font-size: 10px;
          color: #fff;
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 6px;
          border-radius: 10px;
          display: inline-block;
        }
      }
    }
  }
}

.charts-section {
  .chart-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .chart-item {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .chart-title {
        font-size: 14px;
        color: #fff;
        margin-bottom: 10px;
        text-align: center;
        font-weight: bold;
      }

      .chart-container {
        height: 200px;
        width: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .overview-cards {
    flex-wrap: wrap;

    .overview-card {
      flex: 1 1 calc(50% - 10px);
    }
  }

  .chart-row {
    flex-wrap: wrap;

    .chart-item {
      flex: 1 1 calc(50% - 10px);
    }
  }
}

@media (max-width: 768px) {
  .overview-cards {
    .overview-card {
      flex: 1 1 100%;
    }
  }

  .chart-row {
    .chart-item {
      flex: 1 1 100%;
    }
  }

  .factory-header .header-text {
    font-size: 20px;
  }
}
</style>
