<template>
  <div
    id="win"
    ref="box"
    class="win"
    @mousedown.stop
  >
    <template v-for="(item, index) in list">
      <div
        :key="index + item.id"
        :ref="'item' + item.id + index"
        :class="{
          'dragging': item.dragStatus,
          'drop-top': item.drop === 'top',
          'drop-bottom': item.drop === 'bottom',
          'drop-merge': item.drop === 'merge',
          'righting': rightMenuIndex.length === 1 && index === rightMenuIndex[0],
          'selected': selectedIds.includes(index)}"
        :draggable="!renameIndex.length"
        :data-id="index"
        class="app-item"
        selectable="true"
        @drop.prevent
        @dragend="dragEnd($event, item.id, index)"
        @dragenter="dragOver($event, item.id, index)"
        @dragstart="dragStart($event, item.id, index)"
        @contextmenu.prevent="openMenu($event, item.id, index)"
        @dblclick="handleDblclick(item, index)"
        @click="handleClick(item, index)"
      >
        <div
          v-if="item.children && item.children.length"
          :class="{'open': index === openDirIndex}"
          class="app-inner app-dir">
          <div
            class="app-dir-inner">
            <span
              v-for="(child, cIndex) in item.children"
              :key="child.id + cIndex"
              :class="{
              'righting': rightMenuIndex.length === 2 && index === rightMenuIndex[0] && cIndex === rightMenuIndex[1]}"
              :draggable="!renameIndex.length"
              class="inner-app-item"
              @drop.stop
              @dragend.stop="childDragEnd($event, child.id, cIndex, index)"
              @dragstart.stop="childDragStart($event, child.id, cIndex, index)"
              @click.prevent.stop="clickItem(child)"
              @dblclick.prevent.stop="openPage(child)"
              @contextmenu.prevent.stop="openMenu($event, item.id, index, cIndex)">
              <icon-png
                :key="child.deskIcon"
                :icon-name="child.deskIcon"
                class="c-svg"
              />
              <div
                v-if="index === openDirIndex"
                class="app-tit"
              >
                <input
                  v-if="renameIndex.length === 2 && index === renameIndex[0] && cIndex === renameIndex[1]"
                  :ref="index + 'inp' + cIndex"
                  v-model="child.shortcutName"
                  class="name-inp"
                  @blur="saveRename"
                  @dblclick.stop
                  @keyup.enter="saveRename">
                <template v-else>
                  {{ child.shortcutName || child.name }}
                </template>
              </div>
            </span>
            <div
              v-if="index === openDirIndex"
              class="app-dir-tit"
            >{{ item.name }}</div>
          </div>
        </div>
        <div
          v-else
          class="app-inner">
          <icon-png
            :icon-name="item.deskIcon"
            style="user-select: none"
            class="c-svg"
          />
        </div>
        <div class="app-tit">
          <input
            v-if="renameIndex.length === 1 && index === renameIndex[0]"
            :ref="index + 'inp'"
            v-model="item.shortcutName"
            class="name-inp"
            @blur="saveRename"
            @dblclick.stop
            @keyup.enter="saveRename">
          <template v-else>
            {{ item.shortcutName || item.name }}
          </template>
        </div>
      </div>
    </template>
    <!-- 右键菜单 -->
    <ul
      v-show="rightMenuIndex.length"
      ref="rightMenu"
      :style="{ left: rightMenuLeft + 'px', top: rightMenuTop + 'px' }"
      class="contextmenu"
    >
      <template v-if="selectedIds.length <= 1">
        <li
          v-if="!hasChildren"
          @click="rightOpenPage('_self')"> <i class="menu-icon el-icon-link"/>打开</li>
        <li
          v-if="!hasChildren"
          @click="rightOpenPage('_blank')">新窗口打开</li>
        <li @click="rename">重命名</li>
      </template>
      <li
        @click="delShortcut"> <i class="menu-icon el-icon-delete"/>删除</li>
        <!--<li>自定义文件夹</li>-->
    </ul>
    <select-area
      v-if="mouseKey"
      :start-point="selectProps.startPoint"
      :end-point="selectProps.endPoint"/>
  </div>
</template>

<script>
import lodash from 'lodash'
import SideBar from '@/layouts/component/SideBar'
import { offSet, selectElement } from '@/lib/Util'
import WindowsDialog from '@/components/index/WindowsDialog'
import IconPng from '@/components/IconPng'
import SelectArea from '@/components/index/SelectArea'

export default {
  name: 'WindowsAppList',
  components: { SelectArea, IconPng, WindowsDialog, SideBar },
  data() {
    return {
      dragIndex: null, // 拖拽中index
      childDragIndex: null, // 拖拽中child index
      overIndex: null, // 拖拽覆盖index
      boxRef: null, // 父级容器
      mergeTimer: null, // 合并动作定时器
      mergeTemp: null, // 合并数据缓存
      merging: false, // 是否正在合并
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuIndex: [],
      openDirIndex: null,
      renameIndex: [], // 重命名index
      list: this.$store.state.desktop.shortcutList,
      selectProps: {
        startPoint: {
          x: 0,
          y: 0
        },
        endPoint: {
          x: 0,
          y: 0
        }
      },
      mouseKey: false, // 是否监听鼠标移动（移出编辑区范围，不再监听鼠标移动事件）
      mouseComplete: false, // 鼠标移动事件是否完成（鼠标按下到抬起的流程）
      selectedIds: [],
      timer: null
    }
  },
  computed: {
    hasChildren() {
      if (this.rightMenuIndex.length === 2) {
        const child2 = this.list[this.rightMenuIndex[0]].children[
          this.rightMenuIndex[1]
        ]
        return child2.children && child2.children.length
      }
      if (this.rightMenuIndex.length === 1) {
        const child1 = this.list[this.rightMenuIndex[0]]
        return child1.children && child1.children.length
      }
      return false
    }
  },
  watch: {
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    openDirIndex(value) {
      if (value !== null) {
        document.body.addEventListener('click', this.closeDir)
      } else {
        document.body.removeEventListener('click', this.closeDir)
      }
    },
    overIndex: function(val) {
      this.list.forEach((item, index) => {
        if (index !== val) {
          item.drop = null
        }
      })
    },
    '$store.state.desktop.shortcutList': function(val) {
      this.list = val
    }
  },
  created() {},
  mounted() {
    this.subscribe()
    this.boxRef = this.$refs.box
  },
  destroyed() {
    this.$bus.$off('add-shortcuts')
  },
  methods: {
    subscribe() {
      this.windowsListener()
      const on = this.$bus.$on('add-shortcuts', data => {
        //
        // console.log(data, this.list)
        this.list.push(data)
        this.saveShortcutList()
      })
      // console.log('00000', on)
    },
    windowsListener() {
      window.addEventListener('mousedown', this.handleMouseDown)
    },
    dragStart(e, id, index) {
      this.closeMenu()
      this.closeDir()
      this.list[index].dragStatus = true
      this.dragIndex = index
    },
    dragEnd(e, id, index) {
      this.$nextTick(() => {
        this.$refs['box'].removeEventListener('mousemove', this.mouseMove)
        // 取消拖拽状态
        const temp = this.list[index]
        temp.dragStatus = false
        this.list.splice(index, 1, temp)

        if (this.dragIndex === this.overIndex) return
        if (this.overIndex === null) return
        this.list[this.overIndex].drop = null // 取消选中
        // console.log(this.overIndex, this.list[this.overIndex])
        const target = this.$refs[
          'item' + this.list[this.overIndex].id + this.overIndex
        ][0] // 拖拽覆盖目标
        const position = this.judgePosition(e, target)
        // console.log(position)
        if (position === 'top') {
          // 插入目标元素上方
          const source = this.list[this.dragIndex]
          this.list.splice(this.dragIndex, 1)
          this.list.splice(
            this.dragIndex < this.overIndex
              ? this.overIndex - 1
              : this.overIndex,
            0,
            source
          )
          // console.log('插在', this.overIndex, '上方')
        } else if (position === 'bottom') {
          // 插入目标元素下方
          const source = this.list[this.dragIndex]
          this.list.splice(this.dragIndex, 1)
          this.list.splice(
            this.dragIndex > this.overIndex
              ? this.overIndex + 1
              : this.overIndex,
            0,
            source
          )
          // console.log('插在', this.overIndex, '下方')
        } else if (position === 'merge') {
          // 合并
          this.mergeTemp = null
          this.list.splice(this.dragIndex, 1)
        }
        this.overIndex = null
        // console.log('修改后list', this.list)
      })
    },

    dragOver(e, id, index) {
      // 判断拖拽目标是否更换
      if (this.dragIndex === null) {
        return
      }
      if (this.overIndex !== null && this.overIndex !== index) {
        this.cancelMerge()
        this.list[this.overIndex].drop = null // 取消选中
      }
      this.overIndex = index
      if (this.dragIndex === index) return this.cancelMerge() // 拖拽是本身则取
      this.mouseMove(e)
    },
    mouseMove(e) {
      const over = this.list[this.overIndex]
      const target = this.$refs['item' + over.id + this.overIndex][0] // 拖拽覆盖目标
      // 合并范围 target.offsetTop - 15   ~   target.offsetTop + 15
      // 在上方则插入上方，在下方则插入下方
      const position = this.judgePosition(e, target)
      // console.log('over', index)
      if (position === 'top') {
        // console.log('top', this.list[index])
        this.list[this.overIndex].drop = 'top'
        this.list.splice(this.overIndex, 1, this.list[this.overIndex])
        this.cancelMerge()
      } else if (position === 'bottom') {
        // console.log('bottom', this.list[index])
        this.list[this.overIndex].drop = 'bottom'
        this.list.splice(this.overIndex, 1, this.list[this.overIndex])
        this.cancelMerge()
      } else {
        if (this.merging) return
        this.merging = true // 开启merge状态
        this.mergeTimer = setTimeout(() => {
          this.mergeTemp = lodash.cloneDeep(this.list[this.overIndex]) // 暂存合并前drop位置数据
          const merge = lodash.cloneDeep(this.list[this.overIndex]),
            beMerge =
              this.childDragIndex === null
                ? lodash.cloneDeep(this.list[this.dragIndex])
                : lodash.cloneDeep(
                    this.list[this.dragIndex].children[this.childDragIndex]
                  )
          if (merge.children && merge.children.length) {
            if (beMerge.children && beMerge.children.length) {
              // 多 => 多
              merge.children.push(...beMerge.children)
            } else {
              // 1 => 多
              merge.children.push(beMerge)
            }
          } else {
            if (beMerge.children && beMerge.children.length) {
              // 多 => 1
              merge.children = [lodash.cloneDeep(merge), ...beMerge.children]
            } else {
              // 1 => 1
              merge.children = [
                lodash.cloneDeep(merge),
                lodash.cloneDeep(beMerge)
              ]
            }
          }
          merge.children.forEach(item => {
            item.dragStatus = false
            item.drop = null
          })
          merge.drop = 'merge'
          this.list.splice(this.overIndex, 1, merge)
        }, 20)
      }
    },
    // 文件夹内图标拖拽
    childDragStart(e, id, cIndex, index) {
      //
      this.dragIndex = index
      this.childDragIndex = cIndex
      setTimeout(() => {
        this.closeMenu()
        this.closeDir()
      }, 300)
    },
    // 文件夹内图标拖拽
    childDragEnd(e, id, cIndex, index) {
      //
      // console.log('子结束', cIndex, index, this.overIndex)
      if (this.overIndex === this.dragIndex || this.overIndex === null)
        return this.moveOutApp(index, cIndex)
      this.list[this.overIndex].drop = null // 取消选中
      // console.log(this.overIndex, this.list[this.overIndex])
      const target = this.$refs[
        'item' + this.list[this.overIndex].id + this.overIndex
      ][0] // 拖拽覆盖目标
      const position = this.judgePosition(e, target)
      // console.log(position)
      if (position === 'top') {
        // 插入目标元素上方
        // const source = lodash.cloneDeep(this.list[index].children[cIndex])
        const source = this.list[index].children.splice(cIndex, 1)
        this.list.splice(this.overIndex, 0, source[0])

        const newIndex = this.overIndex > index ? index : index + 1 // 计算原文件夹的新位置
        if (this.list[newIndex].children.length === 1) {
          // 删除文件夹
          const source = lodash.cloneDeep(this.list[newIndex].children[0])
          this.list.splice(newIndex, 1, source)
        }
        // console.log('插在', this.overIndex, '上方')
      } else if (position === 'bottom') {
        // 插入目标元素下方
        // const source = lodash.cloneDeep(this.list[index].children[cIndex])
        const source = this.list[index].children.splice(cIndex, 1)
        this.list.splice(this.overIndex + 1, 0, source[0])
        const newIndex = this.overIndex > index ? index : index + 1 // 原文件夹的新位置
        if (this.list[newIndex].children.length === 1) {
          // 删除文件夹
          const source = lodash.cloneDeep(this.list[newIndex].children[0])
          this.list.splice(newIndex, 1, source)
        }
        // console.log('插在', this.overIndex, '下方')
      } else if (position === 'merge') {
        // 合并
        this.mergeTemp = null
        this.list[index].children.splice(cIndex, 1)
        if (this.list[index].children.length === 1) {
          // 删除文件夹
          const source = lodash.cloneDeep(this.list[index].children[0])
          this.list.splice(index, 1, source)
        }
        // console.log('=======================', '合并完成')
      }
      this.dragIndex = null
      this.childDragIndex = null
      this.saveShortcutList()
    },

    // 从文件夹出指定图标到桌面末尾
    moveOutApp(index, cIndex) {
      // const source = lodash.cloneDeep(this.list[index].children[cIndex])
      const source = this.list[index].children.splice(cIndex, 1)
      this.list.push(source[0])
      if (this.list[index].children.length === 1) {
        // 删除文件夹
        const source = lodash.cloneDeep(this.list[index].children[0])
        this.list.splice(index, 1, source)
      }
      this.dragIndex = null
      this.childDragIndex = null
      this.saveShortcutList()
    },

    cancelMerge() {
      // 取消合并，恢复缓存数据
      if (this.mergeTemp) {
        // console.log('取消', this.mergeTemp)
        this.list.splice(this.overIndex, 1, this.mergeTemp)
        this.list[this.overIndex].drop = null
        this.mergeTemp = null
      }
      this.mergeTimer && clearTimeout(this.mergeTimer)
      this.mergeTimer = null
      this.merging = false
    },
    // 右键菜单
    openMenu(e, id, index, cIndex = null) {
      this.rightMenuTop = e.clientY
      this.rightMenuLeft = e.clientX - offSet(this.$refs.box).left
      this.rightMenuIndex = []
      if (cIndex != null) {
        // 选中文件夹内容
        this.rightMenuIndex.push(index)
        this.rightMenuIndex.push(cIndex)
        this.selectedIds = []
      } else {
        // 选中桌面内容
        this.rightMenuIndex.push(index)
        if (!this.selectedIds.includes(index)) {
          this.selectedIds = [index]
        }
      }
      this.rightMenuVisible = true
      // console.log(this.selectedIds)
    },
    // 右键菜单
    closeMenu(e) {
      this.rightMenuVisible = false
      this.rightMenuIndex = []
    },
    // 处理单击事件
    handleClick(item, index) {
      this.selectedIds = [index]
    },
    // 处理双击事件
    handleDblclick(item, index) {
      if (item.children && item.children.length) {
        // 打开文件夹
        this.openDir(index)
      } else {
        // 打开页面
        this.openPage(item)
      }
    },
    // 打开文件夹
    openDir(index) {
      if (index === this.openDirIndex) return
      this.openDirIndex = index
    },
    closeDir(e) {
      if (e && this.$refs.rightMenu.contains(e.target)) {
        return
      }
      this.openDirIndex = null
    },
    openPage(data, target = '_self') {
      this.$bus.$emit('open-iframe', data, target)
    },
    rightOpenPage(target) {
      this.rightMenuIndex.length === 1 &&
        this.openPage(this.list[this.rightMenuIndex[0]], target)
      this.rightMenuIndex.length === 2 &&
        this.openPage(
          this.list[this.rightMenuIndex[0]].children[this.rightMenuIndex[1]],
          target
        )
    },
    clickItem(data) {
      // console.log(data)
    },
    /**
     * @description 根据鼠标位置、拖拽对象判断图标移动位置
     * @param e 鼠标时间
     * @param dropEle dropDom对象
     */
    judgePosition(e, dropEle) {
      if (e.clientY - offSet(this.boxRef).top < dropEle.offsetTop + 10) {
        return 'top'
      } else if (e.clientY - offSet(this.boxRef).top > dropEle.offsetTop + 80) {
        return 'bottom'
      } else {
        return 'merge'
      }
    },
    saveShortcutList() {
      this.$nextTick(() => {
        this.$store.commit('desktop/shortcutList', this.list)
        this.$emit('on-submit', this.list)
      })
    },
    delShortcut() {
      const indexs = this.rightMenuIndex
      // 删除文件夹呢快捷方式
      indexs.length === 2 && this.list[indexs[0]].children.splice(indexs[1], 1)
      // 删除桌面快捷方式
      this.list = this.list.filter(
        (item, index) => !this.selectedIds.includes(index)
      )
      // console.log(this.list)
      this.saveShortcutList()
      // 置空
      this.rightMenuIndex = []
      this.selectedIds = []
    },
    // 重命名
    rename() {
      this.renameIndex = lodash.cloneDeep(this.rightMenuIndex)
      // input获取焦点
      this.$nextTick(() => {
        const indexs = this.renameIndex
        // console.log(this.$refs[indexs[0] + 'inp'])
        indexs.length === 1 && this.$refs[indexs[0] + 'inp'][0].focus()
        indexs.length === 2 &&
          this.$refs[indexs[0] + 'inp' + indexs[1]][0].focus()
      })
    },
    saveRename() {
      this.saveShortcutList()
      this.renameIndex = []
    },

    handleMouseDown(e) {
      // close()
      // console.log('down 按下了')
      this.selectProps.startPoint.x = e.clientX
      this.selectProps.startPoint.y = e.clientY
      this.mouseKey = true
      this.mouseComplete = false
      this.closeMenu()
      this.closeDir()
      this.selectedIds = []
      window.addEventListener('mousemove', this.handleMouseMove)
      window.addEventListener('mouseup', this.handleMouseUp)
    },
    handleMouseMove(e) {
      if (this.mouseKey && !this.mouseComplete) {
        // const offset = offSet(e)
        this.selectProps.endPoint.x = e.clientX
        this.selectProps.endPoint.y = e.clientY
        const div = document.querySelector('#select-area')
        const parent = document.querySelector('.win')
        const containDiv = selectElement(parent, div, 'selectable')
        // containDiv.canCheckedElements.forEach(item => {
        //   item.style.border = 'none'
        // })
        this.selectedIds = containDiv.ids
      }
    },
    handleMouseUp: function() {
      this.mouseKey = false
      this.mouseComplete = true
      this.selectProps.startPoint.x = 0
      this.selectProps.startPoint.y = 0
      this.selectProps.endPoint.x = 0
      this.selectProps.endPoint.y = 0
      window.removeEventListener('mousemove', this.handleMouseMove)
      window.removeEventListener('mouseup', this.handleMouseUp)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.win {
  position: absolute;
  height: 100%;
  float: left;
  background: transparent;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: flex-start;
  padding: 20px;
  z-index: 999;
  user-select: none;
  .app-item {
    position: relative;
    width: 100px;
    margin-bottom: 5px;
    border: 1px dashed transparent;
    transition: all 0.4s;
    cursor: pointer;
    &.selected,
    &.righting {
      border: 1px solid #409eff;
      background: rgba(64, 158, 255, 0.19);
    }

    &.dragging {
      opacity: 0.5;
      border: 0.5px dashed #99999990;
    }

    &.drop-top:before {
      content: '';
      position: absolute;
      width: 100%;
      top: -6px;
      left: 0;
      border-bottom: 4px solid rgba(156, 196, 241, 0.65);
    }

    &.drop-bottom:after {
      content: '';
      position: absolute;
      width: 100%;
      bottom: -5px;
      left: 0;
      border-bottom: 4px solid rgba(156, 196, 241, 0.65);
    }

    &.drop-merge .app-inner {
      transform: scale(1.5);
    }

    .app-tit {
      color: #303133;
      margin-top: 3px;
      font-size: 14px;
      text-align: center;
      overflow: visible;
      height: 25px;
    }
    .app-inner {
      position: relative;
      margin: 10px auto;
      width: 70px;
      transition: transform 0.5s;
      .c-svg {
        font-size: 70px;
      }
    }

    .app-dir {
      position: relative;
      z-index: 9;
      height: 70px;
      .c-svg {
        font-size: 29px;
        transition: all 0.2s linear;
      }

      &.open {
        z-index: 99;
        .app-dir-inner {
          width: 270px;
          height: auto;
          box-shadow: 0 0 5px rgba(153, 153, 153, 0.5);
          .inner-app-item {
            margin-bottom: 12px;
            width: 33.3%;
          }
          .c-svg {
            font-size: 70px;
          }
          &:before {
            display: none;
          }
        }
      }
      .app-dir-inner {
        display: flex;
        flex-wrap: wrap;
        background: #fff;
        border-radius: 10px;
        padding: 5px;
        height: 70px;
        width: 100%;
        overflow: hidden;
        transition: all 0.2s linear;
        margin-bottom: 5px;
        .inner-app-item {
          width: 50%;
          margin: 1px 0;
          text-align: center;
        }
        .app-tit {
          color: #303133;
        }
        .app-dir-tit {
          text-align: center;
          padding: 5px;
          color: #666;
          font-size: 14px;
          width: 100%;
          border-top: 1px solid #ccc;
        }
        &:before {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }
      }
    }
    .name-inp {
      background: #fff;
      padding: 3px 5px;
      position: relative;
      z-index: 1;
    }
  }
  .contextmenu {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    background: #fff;
    font-size: 14px;
    color: #666;
    border: 1px solid #e9e9e9;
    min-width: 200px;
    box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
    li {
      padding: 7px 27px;
      cursor: pointer;
      &:hover {
        background: #f4f4f5;
      }
      .menu-icon {
        margin-left: -20px;
        margin-right: 6px;
      }
    }
    li:last-child {
      border-top: 1px solid #e9e9e9;
    }
  }
}
</style>
