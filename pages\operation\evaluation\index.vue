<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="填报单位："
              prop="inputUnit"
            >
              <el-select
                v-model="searchForm.inputUnit"
                size="small"
                clearable
                filterable
                placeholder="选择填报单位"
                style="width: 140px"
                @change="clearModel"
              >
                <el-option
                  v-for="(item, index) in inputUnitList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="模块："
              prop="serviceNo"
            >
              <el-select
                v-model="searchForm.serviceNo"
                size="small"
                clearable
                filterable
                placeholder="选择模块"
                style="width: 140px"
                @change="clearModel"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="模型："
              prop="modelNo"
            >
              <el-select
                v-model="searchForm.modelNo"
                size="small"
                clearable
                filterable
                placeholder="选择模型"
                style="width: 140px"
              >
                <el-option
                  v-for="(item, index) in filterModelNoList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="时间："
              prop="startDate"
            >
              <el-date-picker
                v-model="searchForm.week"
                :type="'week'"
                :placeholder="'选择周'"
                :append-to-body="true"
                :format="'yyyy-MM-dd'"
                style="width: 130px"/>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-download"
            @click="handleExport"
          >导出
          </el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="时间"
            prop="weekDate"
            width="150"
          >
            <template v-slot="{ row }">
              {{ row.weekDate }} ({{ weeks(row.weekDate) }})
            </template>
          </el-table-column>
          <el-table-column
            label="填报单位"
            width="150"
          >
            <template v-slot="{ row }">
              {{ getName('inputUnitList', row.inputUnit).label }}
            </template>
          </el-table-column>
          <el-table-column
            label="模块"
            prop="serviceNo"
            width="140"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                disable-transitions
              >{{ getName('moduleList', row.serviceNo).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="模型名称"
            prop="modelNos"
          >
            <template
              v-slot="{row}"
            >
              {{ row.modelNos.split(',').map(item => getName('modelNoList', item).label ).join('，') }}
            </template>
          </el-table-column>
          <el-table-column
            label="填报人"
            prop="userName"
            width="120"
          >
            <template
              v-slot="{row}"
            >
              {{ [...new Set(row.userNames.split(','))].join(',') }}
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            style="white-space: nowrap"
            width="220"
          >
            <template
              slot-scope="{row,$index}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail($index)"
                >详情
                </el-button>
              </span>
              <template>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :ques-type="searchForm.quesType"
      :detail="showDetail"
      :module-list="moduleList"
      :input-unit-list="inputUnitList"
      :model-no-list="modelNoList"
      :value-type-list="valueTypeList"
      @success="handleSearch"
      @next="next"
      @prev="prev"
    />
    <Edit
      ref="modalForm"
      :module-list="moduleList"
      :input-unit-list="inputUnitList"
      :model-no-list="modelNoList"
      :value-type-list="valueTypeList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlFindByDictCode,
  evaluationDeleteByMultiKey,
  evaluationExportPdf,
  evaluationSave,
  findWeekAppEval,
  weeklySummarySave
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Detail from './component/detail'

export default {
  layout: 'menuLayout',
  name: 'operation-evaluation',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {
        type: 2,
        modelNo: null
      },
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: findWeekAppEval, //分页接口地址
        delete: evaluationDeleteByMultiKey, //删除接口地址
        save: evaluationSave,
        getDict: dictionaryDtlFindByDictCode
      },
      moduleList: [],
      modelNoList: [],
      inputUnitList: [],
      valueTypeList: [],
      workList: ENUM.workList,
      userId: null,
      editRole: null,
      serviceList: [],
      timer: null,
      activeIndex: 0,
      mode: 'daterange',
      editRow: {}
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    },
    filterModelNoList: function() {
      if (this.searchForm.serviceNo) {
        return this.modelNoList.filter(
          item => item.label1 === this.searchForm.serviceNo
        )
      }
      return this.modelNoList
    }
  },
  async created() {
    this.loadData()
    this.handleSearch(true)
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    clearModel() {
      this.$set(this.searchForm, 'modelNo', null)
    },
    loadData() {
      post(this.url.getDict, {
        dictCode: 'serviceByEval'
      }).then(res => {
        this.moduleList = res.data.map(item => {
          return {
            value: item.code,
            label: item.value
          }
        })
      })
      post(this.url.getDict, {
        dictCode: 'inputUnit'
      }).then(res => {
        this.inputUnitList = res.data.map(item => {
          return {
            value: item.code,
            label: item.value
          }
        })
      })
      post(this.url.getDict, {
        dictCode: 'modelNo'
      }).then(res => {
        this.modelNoList = res.data.map(item => {
          return {
            value: item.code,
            label: item.value,
            label1: item.value1
          }
        })
      })
      post(this.url.getDict, {
        dictCode: 'valueType'
      }).then(res => {
        this.valueTypeList = res.data.map(item => {
          return {
            value: item.code,
            label: item.value
          }
        })
      })
    },
    beforeHandleSearch() {
      if (this.searchForm.week) {
        this.searchForm.startDate = this.$moment(this.searchForm.week)
          .add(0, 'days')
          .format('yyyy-MM-DD')
        this.searchForm.endDate = this.$moment(this.searchForm.week)
          .add(6, 'days')
          .format('yyyy-MM-DD')
      } else {
        this.searchForm.startDate = ''
        this.searchForm.endDate = ''
      }
    },

    afterHandleSearch() {
      //
      this.tableData.forEach(item => {
        if (
          item.handleStatus !== 1 &&
          this.$moment().format('yyyy-MM-DD') > item.planCompleteDate
        ) {
          item.warning = true
        }
      })
    },

    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },
    // 多条件删除
    handleDelete: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm('是否确认删除此数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除操作
        post(this.url.delete, {
          weekDate: data.weekDate,
          serviceNo: data.serviceNo
        }).then(res => {
          this.handleSearch()
        })
      })
    },
    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },

    editSort(row) {
      this.editRow = row
      this.$nextTick(() => {
        this.$refs['input' + row.id].$el.querySelector('input').focus()
      })
    },
    updateRow() {
      console.log(1)
      post(weeklySummarySave, this.editRow).then(res => {
        this.editRow = {}
        this.handleSearch()
      })
    },

    next() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex < this.tableData.length - 1) {
        this.activeIndex++
      } else if (this.page.pageIndex < this.page.totalPages) {
        console.log(this.page.pageIndex)
        this.page.pageIndex++
        this.handleSearch()
        this.activeIndex = 0
      } else {
        this.$message.info('已经是最后一条了')
      }
    },
    prev() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex > 0) {
        this.activeIndex--
      } else if (this.page.pageIndex > 1) {
        this.page.pageIndex--
        this.handleSearch()
        this.activeIndex = this.page.pageSize - 1
      } else {
        this.$message.warning('已经是第一条了')
      }
    },
    tableRowClassName({ row }) {
      if (row.matterType === 4) {
        return 'warning-row'
      }
      return ''
    },

    handleExport() {
      if (!this.searchForm.week) {
        this.$message.warning({
          message: '请选择周！',
          type: 'warning',
          duration: 1000
        })
        return
      }
      post(
        evaluationExportPdf,
        {
          week: this.$moment(this.searchForm.week).week(),
          weekPeriod: `${this.$moment(this.searchForm.week)
            .add(0, 'days')
            .format('yyyy-MM-DD')} - ${this.$moment(this.searchForm.week)
            .add(6, 'days')
            .format('yyyy-MM-DD')}`,
          startDate: this.$moment(this.searchForm.week)
            .add(0, 'days')
            .format('yyyy-MM-DD'),
          endDate: this.$moment(this.searchForm.week)
            .add(6, 'days')
            .format('yyyy-MM-DD')
        },
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '工作推进周报(' + this.weeks(this.searchForm.week) + ').doc'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.visible = false
        this.loading = false
      })
    },

    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },

    weeks(date) {
      return date ? '第' + this.$moment(date).week() + '周' : ''
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
