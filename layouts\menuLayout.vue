<template>
  <div
    class="nav">
    <transition name="loading-fade">
      <div
        v-if="loading"
        class="loading">
        <svg
          class="circular"
          viewBox="25 25 50 50">
          <circle
            class="path"
            cx="50"
            cy="50"
            r="20"
            fill="none"/>
        </svg>
        <div class="loading-text">loading</div>
      </div>
    </transition>

    <el-container
      v-if="!loading"
      direction="vertical">
      <el-header>
        <el-row
          :gutter="20"
          :align="'center'">
          <el-col :span="9">
            <div class="grid-content bg-purple company-info">
              <div class="logo">
                <img
                  src="../assets/logo.png"
                  alt="">
              </div>
              <div class="company"/>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="grid-content bg-purple">
              <div class="search">
                <!--                <el-input
                  v-model="headerInput"
                  prefix-icon="el-icon-search"
                  placeholder="请输入内容"/>-->
              </div>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="grid-content bg-purple">
              <div
                v-if="showHeader"
                class="user-info">
                <el-avatar
                  icon="el-icon-user-solid"
                  style="vertical-align: middle; margin-right: 5px"/>
                <span style="margin-right: 5px;cursor: pointer">{{ userName }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-header>
      <el-container
        :style="'height:'+(parseInt(windowHeight) - 48)+'px'">
        <el-aside
          width="auto"
          height="100%">
          <side-bar/>
        </el-aside>
        <el-container>
          <div
            :class="{'no-header': !showHeader}"
            class="main-wrapper">
            <page-tag-bar/>
            <div class="main-content">
              <el-main>
                <template v-if="!isIframeMode">
                  <nuxt
                    :keep-alive-props="{include:cacheList}"
                    class="nuxt-view"
                    keep-alive/>
                </template>
                <iframe 
                  v-else
                  :src="iframeUrl"
                  frameborder="0"
                  class="iframe-view"/>
              </el-main>
            </div>
          </div>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import SideBar from '@/layouts/component/SideBar'
import { findOneUserByUserNo } from '@/api/desktop'
import { post } from '~/lib/Util'
import { mapState } from 'vuex'
import PageTagBar from '@/layouts/component/PageTagBar'
export default {
  head() {
    return {}
  },
  components: { PageTagBar, SideBar },
  data() {
    return {
      windowHeight: '',
      headerInput: '',
      userName: '',
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId'),
      loading: true
    }
  },
  computed: {
    ...mapState('menu', [
      'showHeader',
      'pageOpenedList',
      'isIframeMode',
      'iframeUrl'
    ]),
    cacheList: function() {
      return this.pageOpenedList.map(item => {
        return item.name
      })
    }
  },
  watch: {
    $route: {
      handler: function(val, oldVal) {},
      immediate: false
    }
  },
  beforeCreate() {},
  created() {},
  mounted() {
    this.findOneUserByUserNo()
    window.addEventListener('resize', this.handleWindowHeight)
    this.handleWindowHeight()
    this.loading = false
  },
  destroyed() {
    window.removeEventListener('resize', this.handleWindowHeight)
  },
  methods: {
    handleOpenNav(key, keyPath) {
      console.log(key, keyPath)
    },
    handleCloseNav(key, keyPath) {
      console.log(key, keyPath)
    },
    handleWindowHeight() {
      this.windowHeight = document.documentElement.clientHeight
    },
    findOneUserByUserNo() {
      post(findOneUserByUserNo, {
        userNo: localStorage.getItem('userId')
      }).then(res => {
        if (res.success) {
          this.userName = res.data.userName
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
loading-fade-enter,
loading-fade-leave-active {
  opacity: 0;
}
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  text-align: center;
  padding: 15% 0;
  background: #fff;
  transition: opacity 0.5s ease-in-out;
  .loading-text {
    color: #13356f;
    font-size: 20px;
    margin-top: 10px;
  }
  .circular {
    height: 50px;
    width: 50px;
    -webkit-animation: loading-rotate 2s linear infinite;
    animation: loading-rotate 2s linear infinite;
  }
  .path {
    -webkit-animation: loading-dash 1.5s ease-in-out infinite;
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: #13356f;
    stroke-linecap: round;
  }
  @-webkit-keyframes loading-rotate {
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes loading-rotate {
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes loading-dash {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -40px;
    }
    100% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -120px;
    }
  }
}
.el-header {
  height: 48px !important;
  padding-left: 0;
  line-height: 48px;
  background: #fff;
  border-bottom: 1px solid #dcdfe6;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.04);
  .company-info {
    width: 260px;
    text-align: center;
    .logo {
      height: 48px;
      width: auto;
      padding: 10px 0;
      img {
        width: auto;
        height: 100%;
      }
    }
  }

  .search {
    height: 36px;
    width: 250px;
  }

  .user-info {
    float: right;
    font-size: 14px;
    /deep/ .el-avatar {
      width: 32px;
      height: 32px;
      line-height: 32px;
    }
  }
}
.el-aside {
  background-color: #fff;
}
.el-main {
  background-color: #f4f4f5;
  height: 100%;
}
/deep/ .el-badge__content.is-fixed {
  top: 22px;
}
.nav {
}
.main-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  &.no-header {
    top: -32px;
    position: relative;
    height: calc(100% + 32px);
    /deep/ .tag-wrapper {
      .el-tabs__nav-prev,
      .el-tabs__nav-next,
      .close-tag-content {
        border: 1px solid #e4e7ed;
      }
      .el-tabs__nav-next {
        border-right: none;
      }
      .el-tabs__nav {
        border-top: 1px solid #e4e7ed;
      }
    }
  }
  .main-content {
    flex: 1;
    overflow: auto;
    background: #f2f4f7;
  }
}
.iframe-view {
  width: 100%;
  height: 100%;
  border: none;
}

.nuxt-view {
  height: 100%;
}
</style>
