<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '调试记录'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择所属模块',
              trigger: 'change'
            }
          ]"
          label="模块"
          prop="serviceNo"
        >
          <el-select
            v-model="formData.serviceNo"
            size="small"
            clearable
            placeholder="选择模块"
          >
            <el-option
              v-for="(item, index) in serviceList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择日期',
              trigger: 'blur'
            }
          ]"
          label="调试开始时间"
          prop="startTime"
        >
          <el-date-picker
            v-model="formData.startTime"
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            type="datetime"
            placeholder="选择时间" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择日期',
              trigger: 'blur'
            }
          ]"
          label="调试结束时间"
          prop="endTime"
        >
          <el-date-picker
            v-model="formData.endTime"
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            type="datetime"
            placeholder="选择时间" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择调试类别',
              trigger: 'change'
            }
          ]"
          label="调试类别"
          prop="category"
        >
          <el-select
            v-model="formData.category"
            size="small"
            clearable
            placeholder="选择调试类别"
          >
            <el-option
              v-for="(item, index) in typeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入调试内容',
              trigger: 'change'
            }
          ]"
          label="调试内容"
          prop="debugContent"
        >
          <el-input
            v-model="formData.debugContent"
            :rows="6"
            type="textarea"
            placeholder="请输入调试内容" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入解决方案',
              trigger: 'change'
            }
          ]"
          label="解决方案"
          prop="solution"
        >
          <el-input
            v-model="formData.solution"
            :rows="6"
            type="textarea"
            placeholder="请输入解决方案" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择状态',
              trigger: 'change'
            }
          ]"
          label="调试状态"
          prop="status"
        >
          <template v-for="item in statusList">
            <el-radio
              v-model="formData.status"
              :key="item.value"
              :label="item.value">{{ item.label }}</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  delDebugRecord,
  deleteFileByIds,
  findDebugRecord,
  saveDebugRecord
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    serviceList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        list: findDebugRecord, //分页接口地址
        delete: delDebugRecord, //删除接口地址
        edit: saveDebugRecord,
        add: saveDebugRecord
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    async handelUpload() {
      // 删除图片
      if (this.deleteIds.length) {
        const del = await post(deleteFileByIds, { ids: this.deleteIds })
      }
      //上传
      if (!this.$refs.upload.uploadFiles.length) return
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      const res = await post(this.url.file, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (res.success) {
        this.uploadFiles = res.data
        return Promise.resolve(true)
      } else {
        this.$message.warning('图片上传失败！')
        this.loading = false
        return Promise.resolve(false)
      }
      return Promise.reject(false)
    },
    httpRequest(params) {},
    submitBefore() {},
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
