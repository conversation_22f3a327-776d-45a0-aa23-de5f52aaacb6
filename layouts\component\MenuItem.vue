<template>
  <div v-if="menu.isShow">
    <el-submenu
      v-if="menu.children && menu.children.length"
      :index="menu.id"
      popper-class="pop-sub"
    >
      <template slot="title">
        <IconPng
          :icon-name="menu.icon"
          class="icon-svg" />
        <span slot="title"> {{ menu.name }} </span>
      </template>
      <menu-item
        v-for="item in menu.children"
        :show-icon="false"
        :key="item.id"
        :menu="item"/>
    </el-submenu>
    <el-menu-item
      v-else
      :index="menu.url"
      :key="menu.id"
    >
      <IconPng
        :icon-name="menu.icon"
        class="icon-svg icon-child" />
      <span slot="title"> {{ menu.name }}</span>
    </el-menu-item>
  </div>

</template>

<script>
import IconPng from '@/components/IconPng'
export default {
  name: 'MenuItem',
  components: { IconPng },
  props: {
    menu: {
      type: Object,
      require: true,
      default: function() {
        return {}
      }
    },
    showIcon: {
      type: Boolean,
      default: true
    }
  },
  created() {}
}
</script>

<style scoped lang="less">
.icon-svg {
  height: 24px;
  width: 24px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 6px;
}
.icon-child {
  position: relative;
  z-index: 999;
  height: 24px;
  width: 24px;
  margin-right: 6px;
}
/deep/ .el-submenu .el-menu-item {
  height: 56px;
  line-height: 56px;
}
/deep/ .el-menu-item.is-active {
  background: #38386c !important;
}
/deep/ .el-menu .el-menu-item.is-active {
  background: transparent !important;
}
/deep/ .el-submenu__title i {
  color: #fff;
  font-size: 16px;
  right: 30px;
}
</style>
