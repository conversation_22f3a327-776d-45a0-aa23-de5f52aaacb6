<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '应用评价'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="120px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择模块',
              trigger: 'change'
            }
          ]"
          label="模块："
          prop="serviceNo"
        >
          <el-select
            v-model="formData.serviceNo"
            size="small"
            filterable
            placeholder="选择模块"
            @change="clearModel"
          >
            <el-option
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择填报单位',
              trigger: 'change'
            }
          ]"
          filterable
          label="填报单位："
          prop="inputUnit"
        >
          <el-select
            v-model="formData.inputUnit"
            size="small"
            placeholder="选择填报单位"
          >
            <el-option
              v-for="(item, index) in inputUnitList"
              :key="index"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择时间',
              trigger: 'change'
            }
          ]"
          label="时间："
          prop="weekDate"
        >
          <el-date-picker
            v-model="formData.weekDate"
            :type="date"
            :placeholder="'选择周'"
            :append-to-body="true"
            :format="'yyyy-MM-dd'"
            :picker-options="pickerOptions"
            :clearable="false"
            style="width: 140px"
          />
          <span
            style="top: 2px; bottom: 2px; left: 20px;"
          >{{ week }}</span>
        </el-form-item>
        <div
          v-for="(item, index) in formData.modelNoList"
          :key="index"
          class="model-item"
        >
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请选择模型名称',
                trigger: 'change'
              }
            ]"
            :prop="'modelNoList.' + index + '.modelNo'"
            label="模型名称："
          >
            <el-button
              v-if="formData.modelNoList.length >= 2"
              style="float: right"
              size="small"
              @click="handleDelete(item, index)"
            >删除
            </el-button>
            <el-select
              v-model="item.modelNo"
              size="small"
              filterable
              placeholder="选择模型名称"
            >
              <el-option
                v-for="(item, index) in filterModelNoList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请输入应用情况',
                trigger: 'change'
              }
            ]"
            :prop="'modelNoList.' + index + '.appDescription'"
            label="应用情况："
          >
            <el-input
              v-model="item.appDescription"
              :rows="4"
              type="textarea"
              placeholder="请输入应用情况"
            />
          </el-form-item>
          <el-form-item
            :prop="'modelNoList.' + index + '.feedBack'"
            label="问题反馈："
          >
            <el-input
              v-model="item.feedBack"
              :rows="4"
              type="textarea"
              placeholder="请输入问题反馈"
            />
          </el-form-item>
        </div>
        <div class="text-center">
          <el-button @click="addModel">添加模型</el-button>
        </div>
      </el-form>

      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  evaluationDelete,
  evaluationFind,
  evaluationSave,
  evaluationSaveAll,
  matterSave,
  saveFeedback,
  uploadFile,
  weeklySummarySave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    inputUnitList: {
      type: Array,
      default: function() {
        return []
      }
    },
    modelNoList: {
      type: Array,
      default: function() {
        return []
      }
    },
    valueTypeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        list: evaluationFind, //分页接口地址
        edit: evaluationSaveAll,
        add: evaluationSaveAll,
        delete: evaluationDelete //删除接口地址
      },
      formData: {
        modelNo: null,
        modelNoList: [{}]
      },
      pickerOptions: {
        disabledDate(time) {
          const day = new Date()
          const weekStart = new Date(day - (day.getDay() || 7) * 86400000) // 获取当前周的周一
          return time < weekStart
        }
      },
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {
    filterModelNoList: function() {
      if (this.formData.serviceNo) {
        return this.modelNoList.filter(
          item => item.label1 === this.formData.serviceNo
        )
      }
      return this.modelNoList
    },

    week: function() {
      return this.formData.weekDate
        ? `第${this.$moment(this.formData.weekDate).week()}周（${this.$moment(
            this.formData.weekDate
          )
            .startOf('isoWeek')
            .format('yyyy-MM-DD')} - ${this.$moment(this.formData.weekDate)
            .endOf('isoWeek')
            .format('yyyy-MM-DD')}）`
        : ''
    }
  },
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    addModel() {
      this.formData.modelNoList.push({})
    },
    deleteModel(index) {
      this.formData.modelNoList.splice(index, 1)
    },
    clearModel() {
      this.formData.modelNoList = [{}]
    },
    /*weekChange() {
      this.$nextTick(() => {
        this.formData.weekDate = this.$moment(this.formData.weekDate)
          .startOf('isoWeek')
          .add(4, 'days')
          .format('yyyy-MM-DD')
      })
    },*/
    /**
     * 开启编辑
     * @param editData 编辑元数据
     */
    async edit(editData) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, editData)
      // 查询模型列表
      if (!this.url || !this.url.list) {
        this.$message.warning('请设置url.list属性!')
        return
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: 1,
          pageSize: 1000,
          serviceNo: this.formData.serviceNo,
          startDate: this.formData.weekDate,
          endDate: this.formData.weekDate,
          inputUnit: this.formData.inputUnit
        })
      )
      console.log(data)
      this.formData.modelNoList = data ? data.content : [{}]
      this.loading = false
    },
    handleDelete(data, index) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      if (data.id) {
        this.$confirm('是否确认删除此数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 删除操作
          post(this.url.delete, { id: data.id }).then(res => {
            // 删除成功
            this.deleteModel(index)
          })
        })
      } else {
        this.deleteModel(index)
      }
    },
    submitBefore() {
      // 提交前操作
      this.formData.type = 2
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {
        modelNo: null,
        modelNoList: [{}]
      }
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          const params = this.formData.modelNoList.map(item => {
            item.serviceNo = this.formData.serviceNo
            item.weekDate = this.formData.weekDate
            item.inputUnit = this.formData.inputUnit
            return item
          })
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            this.submitBefore()
            post(this.url.edit, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            this.submitBefore()
            post(this.url.add, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style
  scoped
  lang="less"
>
.model-item {
  border: 1px solid #dedddd;
  padding: 20px 20px 20px 0;
  margin-bottom: 10px;
}
</style>
