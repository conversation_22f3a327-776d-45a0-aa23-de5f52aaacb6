<template>
  <div v-if="visible">
    <el-dialog
      :visible.sync="visible"
      v-bind="$attrs"
      width="400px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          label="时间："
          prop="module"
        >
          {{ currentEdit[0] }} 第{{ currentEdit[1] + 1 }}周
        </el-form-item>
        <el-form-item
          label="完成节点："
          prop="work"
        >
          <el-select
            v-model="formData.work"
            size="small"
            clearable
            placeholder="选择节点"
          >
            <el-option
              v-for="(item, index) in workList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { progressFind, progressSave, uploadFile } from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {},
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: progressSave,
        add: progressSave,
        file: uploadFile,
        list: progressFind
      },
      formData: {
        module: 1
      },
      dialogImageUrl: null,
      dialogVisible: false,
      moduleList: ENUM.moduleList,
      workList: ENUM.workList,
      menuList: [],
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: [],
      currentRow: null,
      currentEdit: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    clearForm() {
      this.formData = {}
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          this.currentRow.ganttData[this.currentEdit[0]][
            this.currentEdit[1]
          ] = this.formData.work
          const params = Object.assign(this.currentRow, {
            ganttData: JSON.stringify(this.currentRow.ganttData)
          })
          post(this.url.edit, params).then(res => {
            if (res.success) {
              this.$message.success('修改成功！')
              this.selectVisible = false
              this.loading = false
              this.close()
            } else {
              this.$message.warning('修改失败！')
              this.loading = false
            }
          })
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
