<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="search-wrapper">
        <el-form
          ref="searchForm"
          :label-width="'80px'"
          :model="searchForm"
          size="small"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label=""
            prop="userNo"
          >
            <el-input
              v-model="searchForm.userNo"
              clearable

              size="small"
              placeholder="工号"
              style="width: 200px"
              suffix-icon="el-icon-search"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="开始时间"
            prop="startTime"
          >
            <el-date-picker
              v-model="searchForm.dateRange"
              :value-format="'yyyy-MM-dd'"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @input="$forceUpdate()"/>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button
          size="small"
          @click="handleReset"
        >重置
        </el-button>
      </div>
    </div>
    <div class="page-card shadow-light">
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        class="custom-table"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"/>
        <el-table-column
          label="工号"
          prop="userNo"
          width="140"
        />
        <el-table-column
          label="姓名"
          prop="userName"
        />
        <el-table-column
          label="页面数量"
          prop="num"
          width="280"
        />
        <el-table-column
          label="访问次数"
          prop="accessNum"
          width="280"
        />
        <el-table-column
          label="操作"
          prop="percent"
          width="120"
        >
          <template v-slot="{ row }">
            <el-button
              slot="reference"
              type="text"
              @click="handleView(row)"
            >责任页面
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <list
      ref="modalView"
      :service-list="serviceList"
      :login-time-start="searchForm.loginTimeStart"
      :login-time-end="searchForm.loginTimeEnd"/>
  </div>
 
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { findBasicDataConfigByType, pageLogSecret } from '@/api/system'
import SelectOrg from '@/components/SelectOrg'
import { post } from '@/lib/Util'
import List from '@/pages/visit10/ownerLog/component/list'

export default {
  name: 'visit10-ownerLog',
  components: { List, SelectOrg },
  layout: 'menuLayout',
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {},
      serviceList: [],
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: pageLogSecret.getMainHandleUser //分页接口地址
      },
      editUserId: null,
      maxNum: 20
    }
  },
  created() {
    this.findBasicDataConfigByType()
  },
  methods: {
    //
    afterHandleSearch() {
      this.tableData = this.tableData.map(item => {
        item.percent = Number(((item.num / this.maxNum) * 100).toFixed(1))
        item.percent = item.percent > 100 ? 100 : item.percent
        return item
      })
    },
    beforeHandleSearch() {
      if (!this.searchForm.dateRange) {
        this.searchForm.dateRange = [
          this.$moment().format('yyyy-MM-DD'),
          this.$moment().format('yyyy-MM-DD')
        ]
      }
      this.searchForm.loginTimeStart = this.searchForm.dateRange[0]
      this.searchForm.loginTimeEnd = this.searchForm.dateRange[1]
    },
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data : []
      this.loading = false
      this.afterHandleSearch()
    },
    getStatus(percent) {
      if (percent >= 80) {
        return 'success'
      } else if (percent >= 50) {
        return 'warning'
      } else {
        return 'exception'
      }
    },
    handleView: function(row) {
      this.$refs.modalView.userNo = row.userNo
      this.$refs.modalView.visible = true
      this.$refs.modalView.handleSearch(true)
    },
    getServiceName: function(name) {
      return this.serviceList.find(item => item.name === name) || {}
    },
    async findBasicDataConfigByType() {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
      return Promise.resolve(true)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.progress-box {
  display: flex;
  font-size: 18px;
  font-weight: bold;
  .progress-num {
    flex: 1;
    margin-left: 5px;
    text-align: right;
  }
  &.exception {
    color: #ff2855;
  }
  &.warning {
    color: #ffb243;
  }
  &.success {
    color: #19be6b;
  }
}
</style>
