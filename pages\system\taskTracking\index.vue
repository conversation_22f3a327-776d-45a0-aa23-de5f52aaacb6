<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="模块："
              prop="serviceNo"
            >
              <el-select
                v-model="searchForm.serviceName"
                size="small"
                clearable
                filterable
                placeholder="选择模块"
                style="width: 100px"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态："
              prop="serviceNo"
            >
              <el-select
                v-model="searchForm.statusList"
                size="small"
                multiple
                collapse-tags
                clearable
                filterable
                placeholder="选择状态"
                style="width: 100px"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="任务名称"
              prop="nickname"
            >
              <el-input
                v-model="searchForm.taskName"
                clearable
                size="small"
                placeholder="任务名称"
                style="width: 100px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="任务类型"
              prop="nickname"
            >
              <el-select
                v-model="searchForm.taskTypeList"
                size="small"
                multiple
                collapse-tags
                clearable
                filterable
                placeholder="选择类型"
                style="width: 100px"
              >
                <el-option
                  v-for="(item, index) in taskTypeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="业务负责人"
              prop="serviceUser"
            >
              <el-input
                v-model="searchForm.serviceUser"
                clearable
                size="small"
                placeholder="业务负责人"
                style="width: 100px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="开发负责人"
              prop="devUser"
            >
              <el-input
                v-model="searchForm.devUser"
                clearable
                size="small"
                placeholder="开发负责人"
                style="width: 100px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="创建时间："
              prop="startDate"
            >
              <el-date-picker
                v-model="searchForm.createDate"
                :type="'daterange'"
                :placeholder="'选择时间'"
                :append-to-body="true"
                :format="'yyyy-MM-dd'"
                :value-format="'yyyy-MM-dd'"
                style="width: 220px"/>
            </el-form-item>
            <el-form-item
              label="计划完成时间："
              prop="planCompleteDate"
            >
              <el-date-picker
                v-model="searchForm.planCompleteDate "
                :type="'daterange'"
                :placeholder="'选择时间'"
                :append-to-body="true"
                :format="'yyyy-MM-dd'"
                :value-format="'yyyy-MM-dd'"
                style="width: 220px"/>
            </el-form-item>
            <el-form-item
              label="实际完成时间："
              prop="startDate"
            >
              <el-date-picker
                v-model="searchForm.actualCompleteDate"
                :type="'daterange'"
                :placeholder="'选择时间'"
                :append-to-body="true"
                :format="'yyyy-MM-dd'"
                :value-format="'yyyy-MM-dd'"
                style="width: 220px"/>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
          <el-button
            type="success"
            @click="taskScoreVisible = true"
          >得分统计
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          height="calc(100vh - 310px)"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            fixed
          />
          <el-table-column
            label="模块名称"
            prop="serviceName"
            width="72"
          />
          <el-table-column
            label="任务名称"
            prop="taskName"
            show-overflow-tooltip
            min-width="150"
          />
          <el-table-column
            label="任务类型"
            width="75"
          >
            <template v-slot="{ row }">
              {{ getName('taskTypeList', row.taskType).label }}
            </template>
          </el-table-column>
          <el-table-column
            label="北科负责人"
            width="82"
          >
            <template v-slot="{ row }">
              {{ row.serviceUser ? row.serviceUser.split(',').map(item => item.split('|')[1]).join(',') : '' }}
            </template>
          </el-table-column>
          <el-table-column
            label="板材负责人"
            width="82"
          >
            <template v-slot="{ row }">
              {{ row.devUser ? row.devUser.split(',').map(item => item.split('|')[1]).join(',') : '' }}
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            width="95"
            sortable
            prop="createDateTime"
          >
            <template v-slot="{ row }">
              {{ row.createDateTime.substring(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column
            label="计划完成时间"
            width="120"
            sortable
            prop="planCompleteDate"
          />
          <el-table-column
            label="计划延时时间"
            width="120"
            sortable
            prop="planDelayCompleteDate"
          />
          <el-table-column
            label="实际完成时间"
            width="120"
            sortable
            prop="actualCompleteDate"/>
          <el-table-column
            :label="'当前进度'"
            prop="pageDev"
            width="100"
          >
            <template v-slot="{ row }">
              <div
                class="node">
                <el-progress
                  :text-inside="true"
                  :stroke-width="15"
                  :percentage="row.currentProgress"
                  status="success"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="超期(天)"
            width="90"
            sortable
            prop="overdueDays"
          >
            <template v-slot="{ row }">
              {{ row.overdueDays }}
            </template>
          </el-table-column>
          <el-table-column
            label="延期(天)"
            width="90"
            sortable
            prop="delayDays"
          >
            <template v-slot="{ row }">
              {{ row.delayDays }}
            </template>
          </el-table-column>
          <el-table-column
            label="当前进展"
            width="150"
            show-overflow-tooltip
          >
            <template v-slot="{ row }">
              {{ (row.weekProgress || '').split('|||')[(row.weekProgress || '').split('|||').length - 1] }}
            </template>
          </el-table-column>
          <el-table-column
            label="下周计划"
            width="150"
            show-overflow-tooltip
          >
            <template v-slot="{ row }">
              {{ (row.nextWeekPlan || '').split('|||')[(row.nextWeekPlan || '').split('|||').length - 1] }}
            </template>
          </el-table-column>
          <el-table-column
            label="未完成原因"
            width="150"
            show-overflow-tooltip
          >
            <template v-slot="{ row }">
              {{ (row.unfinishedReason || '').split('|||')[(row.unfinishedReason || '').split('|||').length - 1] }}
            </template>
          </el-table-column>
          <el-table-column
            label="填报人"
            width="65"
          >
            <template v-slot="{ row }">
              {{ (row.weekSubmitUser || '').split('|||')[(row.weekSubmitUser || '').split('|||').length - 1] }}
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            fixed="right"
            prop="handleStatus"
            width="80"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="getName('statusList', row.status).type"
                disable-transitions
              >{{ getName('statusList', row.status).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            style="white-space: nowrap"
            width="230"
          >
            <template
              slot-scope="{row,$index}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail($index)"
                >详情
                </el-button>
              </span>
              <template>
                <span >
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span
                  v-command="'/system/taskTracking/progress'"
                  v-if="row.status != 3 && row.status != 4 ">
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleFill(row)"
                  >进度填报
                  </el-button>
                </span>
                <span
                  v-command="'/system/taskTracking/master'"
                  v-if="row.status === 3">
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
                <span
                  v-else>
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 50, 100, 200, 500, 1000, 5000]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <el-dialog
      v-el-drag-dialog
      :title="'任务得分统计'"
      :visible.sync="taskScoreVisible"
      v-bind="$attrs"
      :width="'1000px'"
      @open="onOpen"
      v-on="$listeners">
      <el-table
        v-loading="loading"
        :data="taskScore"
        :size="size"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
        />
        <el-table-column
          label="负责人"
          prop="serviceUser"
        >
          <template v-slot="{ row }">
            {{ row.serviceUser.split(',').map(item => item.split('|')[1]).join(',') }}
          </template>
        </el-table-column>
        <el-table-column
          label="平均得分"
          sortable
          prop="avgScore"
        />
        <el-table-column
          label="任务数"
          sortable
          prop="totalTasks"
        />
        <el-table-column
          label="按期完成数"
          sortable
          prop="scheduleCompletedTasks"
        />
        <el-table-column
          label="延期完成数"
          sortable
          prop="delayCompletedTasks"
        />
        <el-table-column
          label="延期未完成数"
          sortable
          prop="delayUncompletedTasks"
        />
        <el-table-column
          label="超期完成数"
          sortable
          prop="overdueCompletedTasks"
        />
      </el-table>
    </el-dialog>
    <Detail
      ref="modalDetailForm"
      :ques-type="searchForm.quesType"
      :detail="showDetail"
      :module-list="moduleList"
      :task-type-list="taskTypeList"
      :status-list="statusList"
      @success="handleSearch"
      @next="next"
      @prev="prev"
    />
    <Edit
      ref="modalForm"
      :module-list="moduleList"
      :task-type-list="taskTypeList"
      :status-list="statusList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlFindByDictCode,
  evaluationExportPdf,
  findTaskScoreStatistics,
  taskTrackingDelete,
  taskTrackingFind,
  taskTrackingSave
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Detail from './component/detail'
import moment from 'moment'

export default {
  layout: 'menuLayout',
  name: 'operation-evaluation',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {
        type: 2,
        modelNo: null
      },
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: taskTrackingFind, //分页接口地址
        delete: taskTrackingDelete, //删除接口地址
        save: taskTrackingSave,
        getDict: dictionaryDtlFindByDictCode,
        taskScore: findTaskScoreStatistics
      },
      moduleList: [
        '通用要求',
        '智慧生产',
        '智慧质量',
        '智慧能源',
        '智慧成本',
        '智慧运维',
        '物料跟踪',
        '数据中台',
        '协同运管',
        '数据采集',
        '宽厚板集控',
        '数字工厂'
      ],
      taskTypeList: [
        {
          value: 1,
          label: '会议要求'
        },
        {
          value: 2,
          label: '工作计划'
        },
        {
          value: 3,
          label: '攻关计划'
        },
        {
          value: 4,
          label: '新增需求'
        },
        {
          value: 5,
          label: '维保工作'
        }
      ],
      inputUnitList: [],
      valueTypeList: [],
      workList: ENUM.workList,
      userId: null,
      editRole: null,
      serviceList: [],
      statusList: [
        {
          value: 1,
          label: '进行',
          type: 'warning'
        },
        {
          value: 2,
          label: '完成',
          type: 'success'
        },
        {
          value: 3,
          label: '终止',
          type: 'info'
        },
        {
          value: 4,
          label: '延期未完成',
          type: 'danger',
          hide: true
        },
        {
          value: 5,
          label: '延期完成',
          type: '',
          hide: true
        },
        {
          value: 6,
          label: '超期未完成',
          type: 'danger',
          hide: true
        },
        {
          value: 7,
          label: '超期完成',
          type: '',
          hide: true
        }
      ],
      timer: null,
      activeIndex: 0,
      mode: 'daterange',
      editRow: {},
      taskScoreVisible: false,
      taskScore: []
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    },
    filterModelNoList: function() {
      if (this.searchForm.serviceNo) {
        return this.modelNoList.filter(
          item => item.label1 === this.searchForm.serviceNo
        )
      }
      return this.modelNoList
    }
  },
  async created() {
    this.userId = localStorage.getItem('userId')
    // this.loadData()
    this.handleSearch(true)
  },
  destroyed() {},
  methods: {
    getPlanDate(row) {
      return moment(row.planCompleteDate)
        .subtract(row.delayDays, 'days')
        .format('YYYY-MM-DD')
    },
    loadData() {
      post(this.url.getDict, {
        dictCode: 'serviceByEval'
      }).then(res => {
        this.moduleList = res.data.map(item => {
          return {
            value: item.code,
            label: item.value
          }
        })
      })
    },
    beforeHandleSearch() {
      if (
        this.searchForm.planCompleteDate &&
        this.searchForm.planCompleteDate.length
      ) {
        this.searchForm.planCompleteStartDate = this.searchForm.planCompleteDate[0]
        this.searchForm.planCompleteEndDate = this.searchForm.planCompleteDate[1]
      } else {
        this.searchForm.planCompleteStartDate = ''
        this.searchForm.planCompleteEndDate = ''
      }
      if (this.searchForm.createDate && this.searchForm.createDate.length) {
        this.searchForm.createStartDate = this.searchForm.createDate[0]
        this.searchForm.createEndDate = this.searchForm.createDate[1]
      } else {
        this.searchForm.createStartDate = ''
        this.searchForm.createEndDate = ''
      }
      if (
        this.searchForm.actualCompleteDate &&
        this.searchForm.actualCompleteDate.length
      ) {
        this.searchForm.actualCompleteStartDate = this.searchForm.actualCompleteDate[0]
        this.searchForm.actualCompleteEndDate = this.searchForm.actualCompleteDate[1]
      } else {
        this.searchForm.staralCompleteStartDate = ''
        this.searchForm.actualCompleteEndDate = ''
      }
    },

    afterHandleSearch() {
      //
      this.tableData.forEach(item => {
        item.actualCompleteDate = item.actualCompleteDate || ''
      })
    },

    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },
    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 填报
    handleFill: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.editType = 'fill'
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },

    onOpen() {
      this.beforeHandleSearch()
      post(this.url.taskScore, this.searchForm).then(res => {
        this.taskScore = res.data
      })
    },

    next() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex < this.tableData.length - 1) {
        this.activeIndex++
      } else if (this.page.pageIndex < this.page.totalPages) {
        console.log(this.page.pageIndex)
        this.page.pageIndex++
        this.handleSearch()
        this.activeIndex = 0
      } else {
        this.$message.info('已经是最后一条了')
      }
    },
    prev() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex > 0) {
        this.activeIndex--
      } else if (this.page.pageIndex > 1) {
        this.page.pageIndex--
        this.handleSearch()
        this.activeIndex = this.page.pageSize - 1
      } else {
        this.$message.warning('已经是第一条了')
      }
    },
    tableRowClassName({ row }) {
      if (row.status === 4 || row.status === 6) {
        return 'danger-row'
      } else if (row.status === 1) {
        return 'warning-row'
      }
      return ''
    },

    handleExport() {
      if (!this.searchForm.week) {
        this.$message.warning({
          message: '请选择周！',
          type: 'warning',
          duration: 1000
        })
        return
      }
      post(
        evaluationExportPdf,
        {
          week: this.$moment(this.searchForm.week).week(),
          weekPeriod: `${this.$moment(this.searchForm.week)
            .add(0, 'days')
            .format('yyyy-MM-DD')} - ${this.$moment(this.searchForm.week)
            .add(6, 'days')
            .format('yyyy-MM-DD')}`,
          startDate: this.$moment(this.searchForm.week)
            .add(0, 'days')
            .format('yyyy-MM-DD'),
          endDate: this.$moment(this.searchForm.week)
            .add(6, 'days')
            .format('yyyy-MM-DD')
        },
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '工作推进周报(' + this.weeks(this.searchForm.week) + ').doc'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.visible = false
        this.loading = false
      })
    },

    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },

    weeks(date) {
      return date ? '第' + this.$moment(date).week() + '周' : ''
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/deep/ .warning-row {
  background-color: #fffbeb;
}
/deep/ .danger-row {
  background-color: #feeeed;
}
</style>
