<template>
  <el-tag
    :type="diff > 3600 * 24 ? 'danger': 'primary'"
    disable-transitions
  >{{ showTime(diff) }}
  </el-tag>
</template>

<script>
export default {
  name: 'Timer',
  props: {
    time: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      diff: this.time,
      timer: null
    }
  },
  mounted() {
    this.init()
  },
  destroyed() {
    this.timer && clearInterval(this.timer)
  },
  methods: {
    init(time) {
      this.timer = setInterval(() => {
        this.diff++
      }, 1000)
    },
    showTime(val) {
      if (val < 60) {
        return val + '秒'
      } else {
        const min_total = Math.floor(val / 60) // 分钟
        const sec =
          Math.floor(val % 60) > 9
            ? Math.floor(val % 60)
            : '' + Math.floor(val % 60) // 余秒
        if (min_total < 60) {
          return min_total + '分' + sec + '秒'
        } else {
          const hour_total =
            Math.floor(min_total / 60) > 9
              ? Math.floor(min_total / 60)
              : '' + Math.floor(min_total / 60) // 小时数
          const min =
            Math.floor(min_total % 60) > 9
              ? Math.floor(min_total % 60)
              : '0' + Math.floor(min_total % 60) // 余分钟
          return hour_total + '小时' + min + '分' + sec + '秒'
        }
      }
    }
  }
}
</script>

<style scoped>
</style>
