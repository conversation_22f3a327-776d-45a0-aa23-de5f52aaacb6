<template>
  <div class="page-content">
    <div class="page-card shadow-light">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :label-width="'80px'"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <!--          <el-form-item
            label="角色名称"
            prop="nickname"
          >
            <el-input
              v-model="searchForm.roleName"
              clearable
              size="small"
              placeholder="请输入角色名称"
              style="width: 130px"
              type="text"
            />
          </el-form-item>-->
            <el-form-item
              label="用户编码"
              prop="nickname"
            >
              <el-input
                v-model="searchForm.userNo"
                clearable
                size="small"
                placeholder="请输入用户编码"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="菜单名称"
              prop="roleType"
            >
              <el-input
                v-model="searchForm.customName"
                clearable
                size="small"
                placeholder="请输入菜单名称"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="状态"
              prop="status"
            >
              <el-select
                v-model="searchForm.status"
                size="small"
                clearable
                style="width: 130px"
                placeholder="状态"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div>
          <el-button
            icon="ios-home"
            type="primary"
            @click="handleHomeShow"
          >首页展示
          </el-button>
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button
            icon="ios-search"
            type="success"
            @click="distributeResource"
          >菜单编辑
          </el-button>
        </div>
      </div>
      <el-row
        :gutter="30"
        justify="start"
        type="flex"
        class="row-bg full-height"
      >
        <el-col
          :span="24"
          class="full-height">
          <div class="full-height page-content overflow-auto">
            <div class="page-wrapper">
              <el-table
                v-loading="loading"
                :data="tableData"
                border
                class="custom-table"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  width="100"
                />
                <el-table-column
                  label="菜单名称"
                  prop="customName"
                  width="180"
                />
                <!--                <el-table-column
                  label="菜单ID"
                  prop="resourceID"
                  width="180"
                />-->
                <!--                <el-table-column
                  label="图标"
                  prop="icon"
                >
                  <template v-slot="{row}">
                    <icon-png
                      :icon-name="row.icon"
                      style="font-size: 30px" />
                  </template>
                </el-table-column>-->
                <el-table-column
                  label="链接"
                  prop="url"
                />
                <!--                <el-table-column
                  label="排序"
                  prop="sort"
                />-->
                <el-table-column
                  label="状态"
                  prop="status"
                >
                  <template v-slot="{ row }">
                    <el-tag :type="getDict(row.status, 'statusList').type">{{
                      getDict(row.status, 'statusList').label
                    }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  fixed="right"
                  label="操作"
                  width="150"
                >
                  <template
                    v-slot="{row}"
                  >
                    <el-button
                      size="small"
                      type="text"
                      @click="handleEdit(row)"
                    >编辑
                    </el-button>
                    <!-- <el-divider direction="vertical"/> -->
                    <el-button
                      v-show="false"
                      size="small"
                      type="text"
                      @click="handleEditStatus(row, row.status === 0 ? 1 : 0)"
                    >
                      {{ row.status === 0 ? '启用' : '禁用' }}
                    </el-button>
                    <!--                    <template
                      v-command="'/system/menu/delete'">
                      <el-divider direction="vertical" />
                      <el-button
                        slot="reference"
                        type="text"
                        @click="handleDeleteRow(row)"
                      >删除
                      </el-button>
                    </template>-->
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <Edit
      ref="modalForm"
      :edit-node="currentEdit"
      :visible.sync="visibleEdit"
      :mode="mode"
      :tree-data="data"
      :total-menu="totalData"
      @success="loadMenu"
    />
    <DistributeResource
      v-if="visibleResource"
      :role="editRole"
      :visible.sync="visibleResource"
      @refresh="loadMenu"
    />
    <DistributeResourceHomePage
      v-if="visibleResourceHomePage"
      :role="editRole"
      :visible.sync="visibleResourceHomePage"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import DistributeUser from './component/distributeUser'
import DistributeResource from './component/distributeResource'
import DistributeResourceHomePage from './component/distributeResourceHomePage'
import {
  batchUpdateResource,
  findAllRscByUserNo,
  resourceAdd,
  resourceDelete,
  changeStatus,
  resourceListNoPage,
  userList
} from '@/api/system'
import { post } from '@/lib/Util'
import { generateTree, getMenuData } from '@/lib/Menu'
import IconPng from '@/components/IconPng'
import BaseMixins from '@/mixins/BaseMixins'

export default {
  layout: 'menuLayout',
  name: 'system-menu',
  components: {
    DistributeResource,
    IconPng,
    Edit,
    DistributeUser,
    DistributeResourceHomePage
  },
  mixins: [BaseMixins],
  data: () => {
    return {
      userNo: localStorage.getItem('userId'), //localStorage.getItem('userId'),
      mode: 'menu', // menu plugin
      loading: false,
      editRole: {
        userNo: localStorage.getItem('userId') //localStorage.getItem('userId'),
      },
      visibleEdit: false,
      visibleResource: false,
      visibleResourceHomePage: false,
      searchKey: '',
      searchTimer: null,
      url: {
        list: findAllRscByUserNo //分页接口地址
      },
      searchForm: {
        // userNo: '340502199406170210', //localStorage.getItem('userId'),
        userNo: localStorage.getItem('userId'), //localStorage.getItem('userId'),
        customName: '',
        status: ''
      },
      data: [], // 树状菜单数据
      totalData: [],
      tableData: [], // 全量菜单数据
      multipleSelection: [],
      selectedMenu: null, // 当前选中菜单节点
      currentEdit: null, // 弹窗编辑/父级内容
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      statusList: [
        {
          label: '已启用',
          value: 1,
          type: 'success'
        },
        {
          label: '已禁用',
          value: 0,
          type: 'danger'
        }
      ]
    }
  },
  created() {
    this.loadMenu()
  },
  methods: {
    handleHomeShow() {
      this.visibleResourceHomePage = !this.visibleResourceHomePage
    },
    // 加载菜单
    loadMenu() {
      this.loading = true
      post(findAllRscByUserNo, this.searchForm).then(res => {
        if (res.success) {
          this.tableData = res.data
          this.loading = false
        }
      })
    },
    handleSearch() {
      this.loadMenu()
    },
    distributeResource() {
      //this.editRole.id = this.userNo
      this.visibleResource = !this.visibleResource
    },
    handleEdit(row) {
      this.$refs.modalForm.editType = 'edit'
      this.$refs.modalForm.title = '修改'
      this.currentEdit = row
      this.currentEdit.userNo = this.userNo
      this.visibleEdit = true
    },
    handleSelectedEdit() {
      if (!this.selectedMenu) {
        return this.$message.warning('请选择需要修改的菜单！')
      }
      this.$refs.modalForm.editType = 'edit'
      this.$refs.modalForm.title = '修改'
      this.currentEdit = this.selectedMenu
      this.visibleEdit = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    async handleMultipleDelete() {
      if (!this.multipleSelection.length) {
        return this.$message.warning('请选择需要删除的菜单！')
      }
      for (const item of this.multipleSelection) {
        const data = await post(resourceDelete, { id: item.id })
        if (data.success) {
          this.$refs.tree.remove(item)
        }
      }
    },
    async handleDeleteSelected() {
      if (!this.selectedMenu) {
        return this.$message.warning('请选择需要删除的菜单！')
      }
      // if (this.selectedMenu.children && this.selectedMenu.children.length) {
      //   return this.$message.warning('该菜单下包含子菜单，请先删除子菜单！')
      // }
      const data = await this.handleDelete(this.selectedMenu.id)
      if (data.success) {
        this.$refs.tree.remove(this.selectedMenu)
        this.selectedMenu = null
      }
    },
    async handleDeleteRow(row) {
      const data = await this.handleDelete(row.id)
      if (data.success) {
        this.$refs.tree.remove(row)
      }
    },
    async handleDelete(id) {
      const data = await this.$confirm(
        '是否确认删除此数据项及其子菜单?（请谨慎操作）',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => {})
      if (data) {
        const res = await post(resourceDelete, { id })
        if (res.success) {
          this.$message.success('删除成功！')
        }
        return Promise.resolve(res)
      } else {
        return Promise.resolve({ success: false })
      }
    },
    async handleEditStatus(row, status) {
      const data = await this.$confirm(
        `是否确认${status === 1 ? '启用' : '禁用'}此菜单?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => {})
      if (data) {
        let data = {}
        data.id = row.id
        data.status = row.status
        return post(changeStatus, data).then(res => {
          if (res.success) {
            this.$message.success((status === 1 ? '启用' : '禁用') + '成功!')
            row.status = status
          }
        })
      } else {
        return Promise.resolve({ success: false })
      }
    },
    // 搜索
    search() {
      if (!this.searchKey) {
        return (this.data = generateTree(this.totalData))
      }
      this.searchTimer && clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        // console.log('搜索')
        // 过滤包含搜索名称的菜单
        const filterData = this.totalData.filter(
          item => item.name.indexOf(this.searchKey) !== -1
        )
        if (!filterData.length) return (this.data = [])
        // 递归把所有菜单的父级找出来
        // console.log(filterData)
        this.handleMenu(filterData, filterData)
        this.data = generateTree(filterData)
      }, 200)
    },
    // 递归添加父级级单
    handleMenu(menuData, addMenu) {
      const addList = []
      // console.log(menuData, addMenu)
      addMenu.forEach(item => {
        if (!item.parentId) return
        if (!menuData.find(i => i.id === item.parentId)) {
          const match = this.totalData.find(i => i.id === item.parentId)
          match && menuData.push(match)
          match && addList.push(match)
        }
      })
      if (addList.length) {
        this.handleMenu(menuData, addList)
      }
    },
    handleNodeClick(data) {
      this.selectedMenu = data
      this.tableData = this.selectedMenu.children || []
    },
    updateNode(data) {
      if (!data) return
      // 根据编辑情况更新页面信息
      switch (data[0]) {
        case 'add':
          // 添加子节点
          this.currentEdit &&
            this.$refs.tree.append(data[1], this.currentEdit.id || null)
          // 添加根节点
          !this.currentEdit && this.data.push(data[1])
          break
        case 'edit':
          let oldData = this.$refs.tree.getNode(data[1].id).data
          if (oldData.parentId !== data[1].parentId) {
            this.$refs.tree.remove(this.currentEdit)
            this.$refs.tree.append(data[1], data[1].parentId || null)
          } else {
            oldData = data[1] // 更新树数据
            Object.assign(this.currentEdit, data[1])
          }
          break
        default:
          break
      }
    },
    allowDrop(draggingNode, dropNode, type) {
      // console.log(draggingNode, dropNode, type)
      return (
        draggingNode.level === dropNode.level &&
        (type === 'next' || type === 'prev')
      )
    },
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      console.log('tree drop: ', draggingNode, dropNode.label, dropType)
      // 根据被拖拽节点获取父节点
      console.log(this.$refs.tree.getNode(draggingNode.data.parentId))
      let rscList = []
      if (draggingNode.level === 1) {
        console.log(this.$refs.tree.data)
        rscList = this.$refs.tree.data.map((item, index) => {
          delete item.children
          item.sort = index + 1
          return item
        })
      } else {
        rscList = this.$refs.tree
          .getNode(draggingNode.data.parentId)
          .data.children.map((item, index) => {
            item.sort = index + 1
            return item
          })
      }
      const data = post(batchUpdateResource, { rscList })
      if (data.success) console.log('修改成功')
    },
    handleTabClick() {
      console.log(this.mode)
      this.loadMenu()
    },
    handleCopy() {
      if (!this.selectedMenu) {
        return this.$message.warning('请选择需要复制的菜单！')
      }
      const params = Object.assign({}, this.selectedMenu, {
        name: this.selectedMenu.name + '副本'
      })
      delete params.id
      post(resourceAdd, params).then(res => {})
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

/deep/ .el-tree-node {
  margin: 4px 0;
  font-size: 14px;
}

.search-wrapper {
  margin-bottom: 10px;
}

.page-content {
  display: flex;
  flex-direction: column;

  .page-card {
    flex: 1;
    overflow: auto;
  }
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}

.page-wrapper {
  overflow: auto;
  flex: 1;
}

.tree-wrapper {
  background: rgb(224, 224, 224);
}

.custom-disabled {
  color: #999;
}
</style>
