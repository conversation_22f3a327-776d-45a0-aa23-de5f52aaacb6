<template>
  <div
    class="top">
    <div class="company-info">
      <img
        class="logo"
        draggable="false"
        src="../../assets/logo-desk-replace.png"
        alt="">
    </div>
    <div class="deskInfo">
      <span class="block text-right">智慧桌面</span>
      <el-divider direction="vertical"/>
      <span class="block">
        <el-popover
          placement="bottom"
          width="200"
          trigger="hover">
          <span
            slot="reference">
            <el-icon
              v-if="activeDesktop.dpCheck === 0 "
              class="el-icon-pie-chart"/>
            <el-icon
              v-else-if="activeDesktop.dpCheck === 1 "
              class="el-icon-s-home"/>
            <el-icon
              v-else-if="activeDesktop.dpCheck === 2 "
              class="el-icon-s-platform"/>
            <el-icon
              v-else
              class="el-icon-monitor"/>
            {{ activeDesktop.name }}
          </span>
          <ul class="contextmenu">
            <li
              v-for="(item, index) in extendedDesktopList"
              :key="item.id"
              @click="switchDesktop(index, item)">
              {{ item.name }}
              <el-tag
                v-if="index === activeIndex"
                size="mini"
                type="warning"
                effect="plain">当前</el-tag>
              <i
                v-if="item.dpCheck === 3"
                class="close-btn el-icon-delete"
                @click.stop.prevent="delDesktop(index)"/>
            </li>
          </ul>
        </el-popover>
      </span>
    </div>
    <div class="user-info">
      <header-search/>
      &nbsp;
      &nbsp;
      <el-tooltip
        class="item"
        effect="light"
        content="全屏"
        placement="bottom">
        <i
          class="el-icon-full-screen op-icon"
          @click="fullscreen"/>
      </el-tooltip>
      <img
        src="../../assets/user.png"
        alt="">
      <el-dropdown
        class="user-dropdown"
        @command="handleDrop"
      >
        <span class="el-dropdown-link one-line">
          {{ userName }}
          <i class="el-icon-arrow-down el-icon--right"/>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-command="'/system/feedback/devRedirect'"
            command="redirect">开发跳转</el-dropdown-item>
          <el-dropdown-item command="addDesktop">添加桌面</el-dropdown-item>
          <el-dropdown-item command="setting">桌面设置</el-dropdown-item>
          <el-dropdown-item command="loginOut">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import {
  createDesktop,
  deleteDesktop,
  findOneUserByUserNo
} from '@/api/desktop'
import { getTokenStatus, logout } from '@/lib/Menu'
import { mapState } from 'vuex'
import { findOrgByUserNo } from '@/api/system'
import { __canvasWM, TpWatermark } from '@/lib/Watermark'
import HeaderSearch from '@/components/index/HeaderSearch'

export default {
  name: 'WindowsHeader',
  components: { HeaderSearch },
  data() {
    return {
      searchVisible: false,
      searchForm: {
        key: ''
      },
      userName: '',
      isFullscreen: false,
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId')
    }
  },
  computed: {
    ...mapState('desktop', ['desktopList', 'activeIndex', 'desktopData']),
    activeDesktop: function() {
      // 检查是否是扩展桌面列表中的项目
      if (
        this.activeIndex !== null &&
        this.extendedDesktopList[this.activeIndex]
      ) {
        return this.extendedDesktopList[this.activeIndex]
      }
      return this.activeIndex && this.desktopList[this.activeIndex]
        ? this.desktopList[this.activeIndex]
        : {}
    },
    // 扩展的桌面列表，包含工厂总览页面
    extendedDesktopList: function() {
      const factoryOverviews = [
        {
          id: 'factory-zhbj-overview',
          dpCheck: 4,
          name: '中厚板卷厂厂级总览',
          type: 'factory-overview',
          factoryType: 'zhbj'
        },
        {
          id: 'factory-zbc-overview',
          dpCheck: 5,
          name: '中板厂厂级总览',
          type: 'factory-overview',
          factoryType: 'zbc'
        }
      ]
      return [...this.desktopList, ...factoryOverviews]
    }
  },
  watch: {
    desktopData: function() {
      // console.log(this.desktopData, '========')
    }
  },
  mounted() {
    //打印desktopList
    console.warn(
      '%c desktopList',
      'color:red;',
      JSON.stringify(this.desktopList, null, 2)
    )

    this.findOneUserByUserNo()
    document.documentElement.addEventListener('fullscreenchange', e => {
      // console.log('fullscreenchange', e)
    })
  },
  methods: {
    async findOneUserByUserNo() {
      const userNo = localStorage.getItem('userId')
      const res = await post(findOneUserByUserNo, {
        userNo
      })
      if (res.success) {
        this.userName = res.data.userName
      }
      const org = await post(findOrgByUserNo, {
        userNo
      })
      if (['023958', '430281199208235010'].includes(userNo)) return
      const userNoLen = userNo.length
      const userNoShow =
        userNoLen > 14 ? userNo.substring(0, 14) + '****' : userNo
      const indexOrg = org.data.orgAllName.indexOf('车间')
      const orgAllName =
        indexOrg === -1
          ? org.data.orgAllName
          : org.data.orgAllName.substring(0, indexOrg + 2)
      const watermarkText = `${userNoShow} ${
        res.data.userName
      }<br/>${orgAllName}`
      // this.drawWatermark(watermarkText)
      // window.addEventListener('resize', () => {
      //   this.drawWatermark(watermarkText)
      // })
    },
    drawWatermark(watermarkText) {
      TpWatermark(
        watermarkText,
        '400',
        '500',
        '-20',
        'rgba(184, 184, 184)',
        '44',
        '.2'
      )
    },
    handleDrop(action) {
      switch (action) {
        case 'setting':
          this.$store.commit('desktop/showSetting', true)
          break
        case 'loginOut':
          logout(this)
          break
        case 'addDesktop':
          this.addDesktop()
          break
        case 'redirect':
          this.redirect()
          break
        default:
          break
      }
    },
    redirect() {
      this.$prompt('请输入"IP:端口"', '开发跳转', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async ({ value }) => {
          if (getTokenStatus()) {
            window.open(
              value.trim() +
                '?org=redirect&showHeader=1&token=' +
                this.token +
                '&userId=' +
                this.userId
            )
          } else {
            this.$message.warning('登录状态已过期，请重新登录')
            logout(this)
          }
        })
        .catch(() => {})
    },
    addDesktop() {
      this.$prompt('请输入桌面名称', '添加自定义桌面', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async ({ value }) => {
          if (value.length > 10) {
            return this.$message.warning('桌面命名不可超过10个字符')
          }
          const data = await post(createDesktop, {
            name: value,
            type: '1',
            userNo: localStorage.getItem('userId')
          })
          if (data.success) {
            this.$bus.$emit('add-desktop', {
              name: value,
              config: {
                shortcutList: [],
                widgetList: []
              },
              dpCheck: 3,
              id: data.data,
              type: '1'
            })
          }
        })
        .catch(() => {})
    },
    fullscreen() {
      const docElm = document.documentElement
      if (docElm.requestFullscreen) {
        docElm.requestFullscreen()
      } else if (docElm.msRequestFullscreen) {
        docElm.msRequestFullscreen()
      } else if (docElm.mozRequestFullScreen) {
        docElm.mozRequestFullScreen()
      } else if (docElm.webkitRequestFullScreen) {
        docElm.webkitRequestFullScreen()
      }
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitCancelFullScreen) {
        document.webkitCancelFullScreen()
      }
    },

    // 删除桌面
    delDesktop(index) {
      //
      this.$confirm(
        '提示',
        `确认需要删除桌面-${this.desktopList[index].name}吗？`,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        const res = await post(deleteDesktop, {
          deskID: this.desktopList[index].id
        })
        // console.log(res)
        if (res.success) {
          this.$bus.$emit('delete-desktop', index)
        } else {
          this.$message.warning('删除失败！')
        }
      })
    },

    // 切换桌面
    switchDesktop(index, item) {
      // 如果是工厂总览页面，特殊处理
      if (item && item.type === 'factory-overview') {
        this.$bus.$emit('switch-factory-overview', {
          factoryType: item.factoryType,
          name: item.name,
          index: index
        })
      } else {
        this.$bus.$emit('switch-desktop', index)
      }
    },
    onSearch() {},
    showSearch() {
      this.searchVisible = true
      this.$nextTick(() => {
        this.$refs.searchInput.focus()
      })
    }
  }
}
</script>

<style scoped lang="less">
.top {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 100%;
  height: 48px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  background-color: #13356f;
  font-size: 22px;
  letter-spacing: 1px;
  user-select: none;
  z-index: 99;
  transition: transform 0.8s;
  &.active {
    transform: translateY(-48px);
  }
  .company-info {
    font-size: 0;
    .logo {
      height: 36px;
    }
  }
  .deskInfo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .block {
      display: inline-block;
      min-width: 260px;
      vertical-align: middle;
    }
  }
  .user-info {
    display: flex;
    align-items: center;
    font-size: 16px;
    .user-dropdown {
      margin-left: 9px;
      color: #fff;
      font-size: 16px;
    }
    .op-icon {
      font-size: 24px;
      margin-right: 15px;
      cursor: pointer;
    }
  }
}
.contextmenu {
  background: #fff;
  font-size: 12px;
  line-height: 2;
  color: #666;
  box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
  li {
    position: relative;
    padding: 5px 16px;
    cursor: pointer;
    border-top: 1px solid #e9e9e9;
    border-bottom: none;
    text-align: left;
    &:hover {
      background: #f4f4f5;
    }
    i {
      margin-right: 5px;
    }
    .close-btn {
      position: absolute;
      right: 2px;
      top: 10px;
      font-size: 16px;
      &:hover {
        color: #1087da;
      }
    }
  }
  li:last-child {
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9;
  }
}

.search-wrapper {
  position: relative;
  .search-hold {
    width: 174px;
    padding: 6px;
    background: #ffffff;
    border-radius: 14px 14px 14px 14px;
    opacity: 1;
    border: 1px solid #eeeeee;
    font-size: 12px;
    font-weight: normal;
    color: #cccccc;
    line-height: 16px;
    cursor: text;
    img {
      height: 16px;
      width: 16px;
      float: right;
    }
  }
  .search-pop {
    position: absolute;
    top: 0;
    right: -30px;
    width: 560px;
    background: linear-gradient(218deg, #ff8b38 0%, #d6000f 100%);
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.08), 0px 2px 6px rgba(0, 0, 0, 0.06),
      0px 4px 8px 2px rgba(0, 0, 0, 0.04);
    border-radius: 2px;
    z-index: 99;
  }
  .search-top {
    position: relative;
    padding: 16px 70px;
    .search-close {
      position: absolute;
      top: 16px;
      right: 30px;
      color: #fff;
      line-height: 42px;
      cursor: pointer;
      font-size: 30px;
    }
    .img-fix {
      height: 21px;
      display: block;
      margin-top: 10px;
      margin-right: 10px;
    }
    .el-input__inner {
      border-radius: 30px;
    }
  }
  .search-rec {
    background: #fff;
    padding: 20px 0px;
    .rec-tit {
      font-size: 16px;
      font-weight: normal;
      color: #999999;
      line-height: 26px;
      margin-bottom: 10px;
    }
    .search-history {
      padding: 0 60px;
      margin-bottom: 10px;
      .el-tag {
        margin-bottom: 12px;
        margin-right: 12px;
        cursor: pointer;
      }
    }
    .search-recommend {
      .rec-tit {
        padding-left: 60px;
      }
      .recommend-list {
        font-size: 14px;
        font-weight: normal;
        color: #666666;
        line-height: 26px;
        li {
          padding: 6px 60px;
          cursor: pointer;
          &:hover {
            background: #f5f5f6;
            color: #ffb243;
          }
        }
      }
    }
  }
}
</style>
