<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '申请'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1000px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="0"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择用户',
              trigger: 'change'
            }
          ]"
          prop="userNo"
        >
          <p style="font-size: 20px; margin-bottom: 10px">授权人员：</p>
          <user-select v-model="formData.userNo"/>
        </el-form-item>
        <el-form-item
          label=""
          prop="remark"
        >
          <p style="font-size: 20px; margin-bottom: 10px">备注：</p>
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请输入备注" />

        </el-form-item>
      </el-form>
      <div>
        <el-row :gutter="30">
          <el-col :span="12">
            <p style="font-size: 20px; margin-bottom: 20px">已有权限</p>
            <div style="max-height: 500px; overflow: auto">
              <el-tree
                ref="tree"
                :data="data"
                :default-checked-keys="defaultSelected"
                :default-expanded-keys="defaultExpanded"
                :props="defaultProps1"
                node-key="id"
              >
                <template
                  v-slot="{ node, data }"
                >
                  {{ data.type === 'plugin' ? '(小部件) -- ' : '' }}{{ node.label }}
                </template>
              </el-tree>
            </div>
          </el-col>
          <el-col :span="12">
            <p style="font-size: 20px; margin-bottom: 20px">申请权限（请勾选）</p>
            <div style="max-height: 500px; overflow: auto">
              <el-tree
                v-if="visible"
                ref="tree1"
                :data="data1"
                :default-checked-keys="defaultSelected1"
                :default-expanded-keys="defaultExpanded1"
                :props="defaultProps"
                node-key="id"
                show-checkbox
              >
                <template
                  v-slot="{ node, data }"
                >
                  {{ data.type === 'plugin' ? '(小部件) -- ' : '' }}{{ node.label }}
                </template>

              </el-tree>
            </div>
          </el-col>
        </el-row>
      </div>

      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  menuApplySave,
  menuApplyUpdate,
  resourceListNoPage
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { generateTree } from '@/lib/Menu'
import UserSelect from '@/components/userSelect'

export default {
  components: { UserSelect, ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: menuApplyUpdate,
        add: menuApplySave
      },
      formData: {},
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultSelected: [],
      defaultExpanded: [],
      data1: [],
      defaultProps1: {
        children: 'children',
        label: 'name'
      },
      defaultSelected1: [],
      defaultExpanded1: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
    this.getOwnList()
    this.getMenuList()
  },
  methods: {
    async getOwnList() {
      const userNo = localStorage.getItem('userId')
      post(resourceListNoPage, {
        userNo
      }).then(res => {
        this.data = generateTree(res.data).sort(
          (a, b) => (a.type < b.type ? -1 : 1)
        )
      })
    },
    getMenuList() {
      post(resourceListNoPage).then(res => {
        this.data1 = generateTree(
          res.data.filter(item => item.status !== 0)
        ).sort((a, b) => (a.type < b.type ? -1 : 1))
      })
    },
    clearForm() {
      this.formData = {}
      this.defaultSelected1 = []
    },

    /**
     * 开启编辑
     * @param data 编辑元数据
     */
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign(
        {
          newRscList: ''
        },
        this.formData,
        data
      )
      this.defaultSelected1 = this.formData.newRscList.split(',')
    },

    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          this.loading = true
          const selectedList = this.$refs.tree1.getCheckedNodes()
          if (!selectedList.length) {
            return this.$message.warning('请选择权限！')
          }
          // return
          if (this.formData.id) {
            post(
              this.url.edit,
              Object.assign(this.formData, {
                newRscList: selectedList.map(item => item.id).join(',')
              })
            ).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('修改成功！')
                this.close()
              }
            })
          } else {
            post(
              this.url.add,
              Object.assign(this.formData, {
                newRscList: selectedList.map(item => item.id).join(','),
                status: 0,
                createUserNo: localStorage.getItem('userId')
              })
            ).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('新增成功！')
                this.close()
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style scoped>
</style>
