<template>
  <span>
    <template v-if="!handleDiff">
      <el-tag
        :type="diff > 3600 * 24 ? 'danger': 'primary'"
        disable-transitions
      >{{ showTime(diff) }}
      </el-tag>
    </template>
    <template v-else>
      {{ showTime(handleDiff) }}
    </template>
  </span>
</template>

<script>
export default {
  name: 'Timer',
  props: {
    time: {
      type: Number,
      default: 0
    },
    handleDiff: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      diff: this.time,
      timer: null
    }
  },
  watch: {
    time: function(newValue) {
      this.diff = newValue
    }
  },
  mounted() {
    setTimeout(() => this.init(), 1000)
  },
  destroyed() {
    this.timer && clearInterval(this.timer)
  },
  methods: {
    init() {
      this.timer = setInterval(() => {
        this.diff++
      }, 1000)
    },
    showTime(val) {
      if (val < 60) {
        return val + '秒'
      } else {
        const min_total = Math.floor(val / 60) // 分钟
        const sec =
          Math.floor(val % 60) > 9
            ? Math.floor(val % 60)
            : '' + Math.floor(val % 60) // 余秒
        if (min_total < 60) {
          return min_total + '分' + sec + '秒'
        } else {
          const day_total = Math.floor(val / 60 / 60 / 24)
          // console.log(min_total, day_total)
          const lastMin = min_total - day_total * 60 * 24
          const hour_total =
            Math.floor(lastMin / 60) > 9
              ? Math.floor(lastMin / 60)
              : '' + Math.floor(lastMin / 60) // 小时数
          const min =
            Math.floor(lastMin % 60) > 9
              ? Math.floor(lastMin % 60)
              : '0' + Math.floor(lastMin % 60) // 余分钟
          return (
            (day_total ? day_total + '天' : '') +
            hour_total +
            '小时' +
            min +
            '分' +
            sec +
            '秒'
          )
        }
      }
    }
  }
}
</script>

<style scoped>
</style>
