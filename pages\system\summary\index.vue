<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="模块"
              prop="module"
            >
              <el-select
                v-model="searchForm.module"
                size="small"
                placeholder="选择模块"
                style="width: 100px"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :prop="'projectTeamLeader'"
              label="项目负责人"
            >
              <el-input
                v-model="searchForm.projectTeamLeader"
                style="width: 100px"
                placeholder="项目负责人" />
            </el-form-item>
            <el-form-item
              :prop="'sheetMaterialLeader'"
              label="板材负责人"
            >
              <el-input
                v-model="searchForm.sheetMaterialLeader"
                style="width: 100px"
                placeholder="板材负责人" />
            </el-form-item>
            <el-form-item
              :prop="'responsibleUnit'"
              label="负责单位"
            >
              <el-input
                v-model="searchForm.responsibleUnit"
                style="width: 100px"
                placeholder="负责单位" />
            </el-form-item>
            <el-form-item
              :label="'查询模式'"
            >
              <el-select
                v-model="searchForm.mode"
                :style="{width: '100px'}"
                :clearable="false"
                size="small"
                placeholder="选择模式"
              >
                <el-option
                  :label="'按日期'"
                  :value="'daterange'"
                />
                <el-option
                  :label="'按周'"
                  :value="'week'"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="计划完成日期"
              prop="quesType"
            >
              <template>
                <el-date-picker
                  v-show="searchForm.mode === 'week'"
                  v-model="searchForm.week"
                  :type="'week'"
                  :placeholder="'选择周'"
                  :append-to-body="true"
                  :format="'yyyy-MM-dd'"
                  style="width: 130px"/>
                <span
                  style="top: 2px; bottom: 2px; left: 20px;">{{ week }}</span>
              </template>
              <el-date-picker
                v-show="searchForm.mode !== 'week'"
                v-model="searchForm.date"
                :type="'daterange'"
                :append-to-body="true"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"/>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleExport"
          >导出
          </el-button>

        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="模块"
            prop="module"
            width="140"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                disable-transitions
              >{{ getName('moduleList', row.module).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="工作计划"
            prop="currWeekSummary"
          >
            <template v-slot="{ row }">
              <el-tooltip 
                class="item" 
                effect="dark"
                placement="top">
                <div class="one-line">
                  {{ row.currWeekSummary }}
                </div>
                <div
                  slot="content"
                  v-html="formatText(row.currWeekSummary)"/>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="项目组负责人"
            prop="projectTeamLeader"
          />
          <el-table-column
            label="板材负责人"
            prop="sheetMaterialLeader"
          /><el-table-column
            label="负责单位"
            prop="responsibleUnit"
          />
          <el-table-column
            label="工作安排日期"
            prop="workArrangeDate"
            width="95"
          /><el-table-column
            label="计划完成日期"
            prop="plannCompleteDate"
            width="95"
          />
          <el-table-column
            label="是否完成"
            prop="completed"
            width="80"
          >
            <template v-slot="{ row }">
              <el-tag
                v-if="row.completed"
                :type="getName('statusList', row.completed).type"
                disable-transitions
              >
                {{ getName('statusList', row.completed).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="未完成情况说明"
            prop="explanation"
          />
          <el-table-column
            fixed="right"
            label="操作"
            style="white-space: nowrap"
            width="120"
          >
            <template
              slot-scope="{row,$index}"
            >
              <!--              <span>-->
              <!--                <el-button-->
              <!--                  size="small"-->
              <!--                  type="text"-->
              <!--                  @click="handleDetail($index)"-->
              <!--                >详情-->
              <!--                </el-button>-->
              <!--              </span>-->
              <template>
                <span>
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :ques-type="searchForm.quesType"
      :detail="showDetail"
      :module-list="moduleList"
      :status-list="statusList"
      @success="handleSearch"
      @next="next"
      @prev="prev"
    />
    <Edit
      ref="modalForm"
      :module-list="moduleList"
      :status-list="statusList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlFindByDictCode,
  matterTop,
  weeklySummaryDelete,
  weeklySummaryFind,
  weeklySummarySave,
  weeklySummaryExportExcel
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Detail from './component/detail'

export default {
  layout: 'menuLayout',
  name: 'system-summary',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {
        mode: 'daterange',
        type: 2
      },
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: weeklySummaryFind, //分页接口地址
        delete: weeklySummaryDelete, //删除接口地址
        save: weeklySummarySave,
        getDict: dictionaryDtlFindByDictCode
      },
      moduleList: [],
      statusList: [
        {
          label: '未完成',
          type: 'warning',
          value: '0'
        },
        {
          label: '已完成',
          type: 'success',
          value: '1'
        }
      ],
      workList: ENUM.workList,
      userId: null,
      editRole: null,
      serviceList: [],
      timer: null,
      activeIndex: 0,
      mode: 'daterange',
      editRow: {}
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    },
    week: function() {
      return this.searchForm.week
        ? `第${this.$moment(this.searchForm.week).week() + 1}周（${this.$moment(
            this.searchForm.week
          )
            .add(0, 'days')
            .format('yyyy-MM-DD')} - ${this.$moment(this.searchForm.week)
            .add(6, 'days')
            .format('yyyy-MM-DD')}）`
        : ''
    }
  },
  async created() {
    const { data: module } = await post(this.url.getDict, {
      dictCode: 'module'
    })
    this.moduleList = module.map(item => {
      return {
        value: Number(item.code),
        label: item.value
      }
    })
    this.handleSearch(true)
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    beforeHandleSearch() {
      if (
        this.searchForm.mode === 'daterange' &&
        this.searchForm.date &&
        this.searchForm.date.length
      ) {
        this.searchForm.plannCompleteStartDate = this.$moment(
          this.searchForm.date[0]
        ).format('yyyy-MM-DD')
        this.searchForm.plannCompleteEndDate = this.$moment(
          this.searchForm.date[1]
        ).format('yyyy-MM-DD')
      } else if (this.searchForm.mode === 'week' && this.searchForm.week) {
        this.searchForm.plannCompleteStartDate = this.$moment(
          this.searchForm.week
        )
          .add(0, 'days')
          .format('yyyy-MM-DD')
        this.searchForm.plannCompleteEndDate = this.$moment(
          this.searchForm.week
        )
          .add(6, 'days')
          .format('yyyy-MM-DD')
      } else {
        this.searchForm.plannCompleteStartDate = ''
        this.searchForm.plannCompleteEndDate = ''
      }
    },

    afterHandleSearch() {
      //
      this.tableData.forEach(item => {
        if (
          item.handleStatus !== 1 &&
          this.$moment().format('yyyy-MM-DD') > item.planCompleteDate
        ) {
          item.warning = true
        }
      })
    },

    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },

    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },

    editSort(row) {
      this.editRow = row
      this.$nextTick(() => {
        this.$refs['input' + row.id].$el.querySelector('input').focus()
      })
    },
    updateRow() {
      console.log(1)
      post(weeklySummarySave, this.editRow).then(res => {
        this.editRow = {}
        this.handleSearch()
      })
    },

    next() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex < this.tableData.length - 1) {
        this.activeIndex++
      } else if (this.page.pageIndex < this.page.totalPages) {
        console.log(this.page.pageIndex)
        this.page.pageIndex++
        this.handleSearch()
        this.activeIndex = 0
      } else {
        this.$message.info('已经是最后一条了')
      }
    },
    prev() {
      if (this.activeIndex > 0) {
        this.activeIndex--
      } else if (this.page.pageIndex > 1) {
        this.page.pageIndex--
        this.handleSearch()
        this.activeIndex = this.page.pageSize - 1
      } else {
        this.$message.warning('已经是第一条了')
      }
    },
    tableRowClassName({ row }) {
      if (row.matterType === 4) {
        return 'warning-row'
      }
      return ''
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },

    weeks(date) {
      return date ? '第' + (this.$moment(date).week() + 1) + '周' : ''
    },
    handleExport() {
      this.loading = true
      post(
        weeklySummaryExportExcel,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        if (
          this.searchForm.plannCompleteStartDate &&
          this.searchForm.plannCompleteEndDate
        ) {
          link.setAttribute(
            'download',
            '周总结(' +
              this.$moment(this.searchForm.plannCompleteStartDate).format(
                'yyyy-MM-DD'
              ) +
              '-' +
              this.$moment(this.searchForm.plannCompleteEndDate).format(
                'yyyy-MM-DD'
              ) +
              ').xls'
          )
        } else {
          link.setAttribute('download', '周总结.xls')
        }
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        // this.importDateVisible = false
        this.loading = false
      })
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
