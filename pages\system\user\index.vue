<template>
  <div class="page-content">
    <el-row
      :gutter="20"
      class="row-bg full-height"
      justify="start"
      type="flex"
    >
      <el-col
        :span="7"
        class="full-height"
      >
        <div class="tree-left shadow-light full-height">
          <div class="tree-tit">
            当前选择：{{ searchForm.orgName || '' }}
          </div>
          <div class="tree-box">
            <el-tree
              :data="data"
              :load="loadNode"
              :props="defaultProps"
              highlight-current
              lazy
              node-key="id"
              @node-click="handleNodeClick"
            />
          </div>
        </div>
      </el-col>
      <el-col
        :span="17"
        class="full-height overflow-auto"
      >
        <div class="page-operate">
          <div class="search-wrapper">
            <el-form
              ref="searchForm"
              :label-width="'80px'"
              :model="searchForm"
              size="mini"
              inline
              @keyup.enter.native="handleSearch(true)"
            >
              <el-form-item
                label=""
                prop="userNo"
              >
                <el-input
                  v-model="searchForm.userNo"
                  clearable
                  size="small"
                  placeholder="请输入员工编号"
                  style="width: 120px"
                  suffix-icon="el-icon-search"
                  type="text"
                />
              </el-form-item>
              <el-form-item
                label="员工姓名"
                prop="userName"
              >
                <el-input
                  v-model="searchForm.userName"
                  clearable
                  size="small"
                  placeholder="请输入员工名称"
                  style="width: 120px"
                  type="text"
                />
              </el-form-item>
              <el-form-item
                v-if="searchForm.orgName"
                label="当前机构"
                prop="userName"
              >
                {{ searchForm.orgName || '' }}
                <el-button
                  size="small"
                  @click="searchForm.orgName = null; searchForm.orgCode = []; handleSearch(true)">
                  取消选择
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div>
            <el-button
              icon="ios-search"
              size="small"
              type="primary"
              @click="handleSearch"
            >搜索
            </el-button>
            <el-button
              size="small"
              @click="handleReset"
            >重置
            </el-button>
            <el-button
              v-command="'/system/user/add'"
              icon="el-icon-circle-plus-outline"
              size="small"
              type="success"
              @click="handleAdd"
            >新增
            </el-button>
          </div>
        </div>
        <div class="page-card shadow-light">
          <el-table
            v-loading="loading"
            :data="tableData"
            :size="size"
            border
            class="custom-table"
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              type="index"
              width="100"
            />
            <el-table-column
              label="员工编号"
              prop="userNo"
            />
            <el-table-column
              label="员工姓名"
              prop="userName"
            />
            <el-table-column
              label="部门"
              prop="spareField"
              show-overflow-tooltip
            />
            <el-table-column
              fixed="right"
              label="操作"
              width="220"
            >
              <template
                v-slot="{row}"
              >
                <span v-command="'/system/user/role'">
                  <el-button
                    size="small"
                    type="text"
                    @click="distributeRole(row)"
                  >分配角色
                  </el-button>
                  <el-divider direction="vertical" />
                </span> <span v-command="'/system/user/edit'">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                  <el-divider
                    v-command="'/system/user/edit'"
                    direction="vertical"
                  />
                </span>
                <el-button
                  v-command="'/system/user/delete'"
                  slot="reference"
                  type="text"
                  @click="handleDelete(row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-row
            align="middle"
            class="table-pagination"
            justify="end"
            type="flex"
          >
            <el-pagination
              :current-page="page.pageIndex"
              :page-size="page.pageSize"
              :page-sizes="[10, 20, 30, 40]"
              :total="page.total"
              background
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </el-row>
        </div>
      </el-col>
    </el-row>
    <Edit
      ref="modalForm"
      @success="handleSearch"
    />
    <DistributeRole
      v-if="visibleDistribute"
      :id="editUserId"
      :visible-distribute.sync="visibleDistribute"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import DistributeRole from './component/distributeRole'
import listMixins from '@/mixins/ListMixins'
import { orgListByCode, userDelete, userList } from '@/api/system'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'

export default {
  layout: 'menuLayout',
  name: 'system-user',
  components: {
    Edit,
    DistributeRole
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: userList, //分页接口地址
        delete: userDelete //删除接口地址
      },
      searchForm: { orgName: '' },
      editUserId: null,
      data: [], // 树状数据
      defaultProps: {
        children: 'children',
        label: 'orgAllName',
        isLeaf: 'leaf'
      }
    }
  },
  methods: {
    // 分配角色
    distributeRole(data) {
      this.editUserId = data.id
      this.visibleDistribute = !this.visibleDistribute
    },
    async loadData(orgCode) {
      const data = await post(orgListByCode, { orgCode: orgCode })
      data.data.forEach(item => {
        item.leaf = !item.hasChildren
      })
      return Promise.resolve(data.success ? data.data : [])
    },
    async loadNode(node, resolve) {
      const parentCode = node.data.orgCode || 'X'
      let data = await this.loadData(parentCode)
      if (parentCode === 'X') {
        data = data.filter(item => ENUM.orgTop.indexOf(item.orgCode) !== -1)
      }
      resolve(data)
    },
    async handleNodeClick(data) {
      this.searchForm.orgCode = [data.orgCode]
      this.searchForm.orgName = data.orgAllName
      this.handleSearch(true)
      //
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.tree-left {
  overflow: auto;
  padding: 24px;
  background: #fff;
  border: 1px solid #eee;

  .tree-box {
    height: calc(100% - 55px);
    overflow: auto;
  }

  .tree-tit {
    margin-bottom: 20px;
    font-size: 18px;
    line-height: 1.5;
  }
}

.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
</style>
