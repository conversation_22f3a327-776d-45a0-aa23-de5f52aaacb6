<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'应用评价详情'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1200px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-row
        :gutter="2"
        type="flex"
        align="middle">
        <el-col :span="2">
          <div
            class="btn-arrow"
            title="上一条"
            @click="prev">
            <i class="el-icon-caret-left" />
          </div>
        </el-col>
        <el-col :span="20">
          <el-form
            v-if="visible"
            ref="form"
            :model="formData"
            label-width="140px"
            size="medium"
          >
            <el-form-item
              label="模块："
              prop="serviceNo"
            >
              <el-tag
                disable-transitions
              >{{ getName('moduleList', formData.serviceNo).label }}
              </el-tag>
            </el-form-item>
            <el-form-item
              label="填报单位："
              prop="serviceNo"
            >
              {{ getName('inputUnitList', formData.inputUnit).label }}
            </el-form-item>
            <el-form-item
              label="时间："
              prop="weekDate"
            >
              <span>{{ week }}</span>
            </el-form-item>
            <div 
              v-for="(item, index) in modelList"
              :key="index"
              class="model-item">
              <el-form-item
                label="模型名称："
                prop="modelNo"
              >
                {{ getName('modelNoList', item.modelNo).label }}
              </el-form-item>
              <el-form-item
                label="应用情况："
                prop="appDescription"
              >
                <span
                  style="font-size: 22px"
                  v-html="formatText(item.appDescription)"/>
              </el-form-item>
              <el-form-item
                label="问题反馈："
                prop="feedBack"
              >
                <span
                  style="font-size: 22px"
                  v-html="formatText(item.feedBack)"/>
              </el-form-item>
            </div>
          </el-form>
        </el-col>
        <el-col :span="2">
          <div
            class="btn-arrow"
            title="下一条"
            @click="next">
            <i class="el-icon-caret-right" />
          </div>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  dictionaryDtlFindByDictCode,
  evaluationFind,
  saveFeedback,
  uploadFile
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    },
    inputUnitList: {
      type: Array,
      default: function() {
        return []
      }
    },
    modelNoList: {
      type: Array,
      default: function() {
        return []
      }
    },
    valueTypeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    },
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        list: evaluationFind, //分页接口地址
        edit: saveFeedback,
        add: saveFeedback,
        file: uploadFile,
        getDict: dictionaryDtlFindByDictCode
      },
      modelList: [],
      formData: {
        modelNoList: []
      }
    }
  },
  computed: {
    week: function() {
      return this.formData.weekDate
        ? `第${this.$moment(this.formData.weekDate).week()}周（${this.$moment(
            this.formData.weekDate
          )
            .startOf('isoWeek')
            .format('yyyy-MM-DD')} - ${this.$moment(this.formData.weekDate)
            .endOf('isoWeek')
            .format('yyyy-MM-DD')}）`
        : ''
    }
  },
  watch: {
    detail: {
      deep: true,
      handler: function() {
        this.formData = this.detail
        this.searchList()
      }
    }
  },
  async created() {
    // console.log('')
  },
  methods: {
    onOpen() {
      this.formData = this.detail
    },
    async searchList() {
      // 查询模型列表
      if (!this.url || !this.url.list) {
        this.$message.warning('请设置url.list属性!')
        return
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: 1,
          pageSize: 1000,
          inputUnit: this.formData.inputUnit,
          serviceNo: this.formData.serviceNo,
          startDate: this.formData.weekDate,
          endDate: this.formData.weekDate
        })
      )
      this.modelList = data ? data.content : [{}]
      console.log(this.modelList)
      this.loading = false
    },
    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },
    clearForm() {
      this.formData = {
        modelNoList: []
      }
    },
    next() {
      this.$emit('next')
    },
    prev() {
      this.$emit('prev')
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  &:hover {
    background: #eee;
    color: #fff;
  }
}
.model-item {
  border: 1px solid #dedddd;
  padding: 20px 20px 20px 0;
  margin-bottom: 10px;
}
</style>
