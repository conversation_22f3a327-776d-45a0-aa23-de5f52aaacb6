<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'维保记录详情'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
        @keyup.enter.native="handelSubmit"
      >
        <el-form-item
          label="模块名称:"
          prop="serviceName"
        >
          {{ formData.serviceName }}
        </el-form-item>
        <el-form-item
          label="功能名称:"
          prop="functionName"
        >
          {{ formData.functionName }}
        </el-form-item>
        <el-form-item
          label="问题描述:"
          prop="errorInfo"
        >
          {{ formData.errorInfo }}
        </el-form-item>
        <el-form-item
          label="责任部门:"
          prop="departmentName"
        >
          {{ formData.departmentName }}
        </el-form-item>
        <el-form-item
          label="责任人:"
          prop="dutyPerson"
        >
          {{ formData.dutyPerson }}
        </el-form-item>
        <el-form-item
          label="原因分析:"
          prop="causeAnalysis"
        >
          {{ formData.causeAnalysis }}
        </el-form-item>
        <el-form-item
          label="处置优化措施:"
          prop="handle"
        >
          {{ formData.handle }}
        </el-form-item>
        <el-form-item
          label="计划完成时间"
          prop="planFinishTime"
        >
          {{ formData.planFinishTime }}
        </el-form-item>
        <el-form-item
          label="实际完成时间"
          prop="actFinishTime"
        >
          {{ formData.actFinishTime }}
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { deleteFileByIds, saveFeedback, uploadFile } from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    serviceList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: saveFeedback,
        add: saveFeedback,
        file: uploadFile
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    async handelUpload() {
      // 删除图片
      if (this.deleteIds.length) {
        const del = await post(deleteFileByIds, { ids: this.deleteIds })
      }
      //上传
      if (!this.$refs.upload.uploadFiles.length) return
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      const res = await post(this.url.file, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (res.success) {
        this.uploadFiles = res.data
        return Promise.resolve(true)
      } else {
        this.$message.warning('图片上传失败！')
        this.loading = false
        return Promise.resolve(false)
      }
      return Promise.reject(false)
    },
    httpRequest(params) {},
    async handelSubmit() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.loading = true
        const file = await this.handelUpload()
        this.handelConfirm()
      })
    },

    submitBefore() {
      if (!this.formData.attachList) this.formData.attachList = []
      this.formData.attachList.push(...this.uploadFiles, ...this.attachList)
      this.formData.anonymous = !!this.formData.anonymous
      this.formData.quesType = this.quesType
      this.formData.quesUserNo = localStorage.getItem('userId')
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
