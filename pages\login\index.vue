<template>
  <div class="content">
    <div class="content-left">
      <div class="text">
        <span>欢迎来到
          <img
            src="../../assets/images/login-icon.svg"
            alt=""
          >
        </span>
        南钢板材全流程<br>智能制造平台
      </div>
    </div>
    <div class="form-con">
      <login-form @on-success-valid="handleSubmit"/>
    </div>
  </div>
</template>
<script>
import LoginForm from './components/login-form'
import { mapActions } from 'vuex'

export default {
  layout: 'logoLayout',
  name: 'Login',
  components: {
    LoginForm
  },
  filters: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {
    console.log('login 创建了')
  },
  mounted() {
    // this.$Spin.hide()
  },
  methods: {
    // ...mapActions(['handleLogin']),
    // 提交
    handleSubmit(params) {
      // this.handleLogin(params).then(res => {
      //   this.$router.push({
      //     // name: this.$config.homeName
      //   })
      // })
    }
  }
}
</script>

<style lang="less">
.center {
  text-align: center;
}
.content {
  width: 800px;
  display: flex;
  align-items: center;
  .form-con {
    display: flex;
    flex-direction: column;
    //align-items: center;
    justify-content: center;
    margin: 32px 0;
    width: 46%;
    height: 400px;
    background: #fff;
    padding: 32px 40px;
  }
  .content-left {
    background: url('../../assets/images/login-text-bg.png') no-repeat;
    background-size: cover;
    width: 54%;
    height: 400px;
    padding: 40px 80px 40px 40px;
    font-size: 32px;
    color: #fff;
    letter-spacing: 8px;
    font-weight: 300;
    line-height: 1.5;
    display: flex;
    flex-direction: column;
    //align-items: center;
    justify-content: center;
    .text {
      margin-bottom: 30px;
    }
    span {
      font-size: 40px;
      display: block;
      border-bottom: 1px solid #fff;
      padding-bottom: 25px;
      margin-bottom: 20px;
      letter-spacing: 0.2em;
      font-weight: 300;
      img {
        height: 40px;
        vertical-align: middle;
      }
    }
  }
}
</style>
