<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="role.roleName || '' + '-- 权限管理'"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        label-width="120px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="角色名称："
          prop="roleName"
        >
          {{ role.roleName || '' }}
        </el-form-item>
        <el-form-item
          label="角色权限："
          prop="org"
        >
          <el-tree
            ref="tree"
            :data="data"
            :default-checked-keys="defaultSelected"
            :default-expanded-keys="defaultExpanded"
            :props="defaultProps"
            node-key="id"
            show-checkbox
          >
            <template
              v-slot="{ node, data }"
            >
              {{ data.type === 'plugin' ? '(小部件) -- ' : '' }}{{ node.label }}
            </template>

          </el-tree>

        </el-form-item>

      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  findSelectResourceByUserNo,
  saveOrUpdate,
  resourceListNoPage,
  roleById,
  filterResourceSelectedByRole
} from '@/api/system'
import { post } from '@/lib/Util'
import { generateTree, getMenuData } from '@/lib/Menu'

export default {
  components: {},
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'role'],
  data() {
    return {
      loading: false,
      url: {},
      data: [],
      totalData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: data => this.requiredIds.includes(data.id)
      },
      defaultSelected: [],
      defaultExpanded: [],
      // 默认选中
      requiredIds: [
        '9c274f7b-d55f-45f7-9ce9-8b6b9d7c34a1', //首页
        '7ff9e369-a5be-40c5-b7c5-4ab9bd3e1702', //菜单配置
        'a52f52a9-d63e-4a75-bf38-428cf0abf945', //报警规则配置
        '96fbe14b-24e2-4dc3-9fae-029fbddbaaff', //消息推送
        'ad4ca590-414d-4605-ab37-02a4cc34933a' //消息历史记录
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set() {}
    }
  },
  watch: {},
  async created() {
    await this.loadData()
    await this.loadFilterResource()
  },
  methods: {
    loadData() {
      post(resourceListNoPage, {}).then(res => {
        if (res.success) {
          this.totalData = res.data
          this.data = generateTree(res.data)
            .map(node => this.processNode(node))
            .sort((a, b) => (a.type < b.type ? -1 : 1))
        }
      })
    },

    processNode(node) {
      if (node.children) {
        node.children = node.children.map(child => this.processNode(child))
      }
      if (this.requiredIds.includes(node.id)) {
        return { ...node, disabled: true }
      }
      return node
    },

    loadFilterResource() {
      post(filterResourceSelectedByRole, {
        userNo: this.role.userNo,
        resourceName: '',
        status: 1
      }).then(res => {
        if (Array.isArray(res.data) && res.data.length > 0) {
          this.defaultSelected = [
            ...new Set([
              ...res.data.map(item => item.resourceID),
              ...this.requiredIds
            ])
          ]
        }
      })
    },
    handelConfirm() {
      if (!this.$refs.tree) {
        console.error('树组件未找到')
        return
      }

      const selectedList = this.$refs.tree.getCheckedNodes()

      const selectIDs = [
        ...new Set([...selectedList.map(node => node.id), ...this.requiredIds])
      ]
      // if (!selectIDs.length) {
      //   this.$message.warning('请选择资源')
      //   return
      // }

      this.handleMenu(selectIDs, selectIDs)
      const unSelectIDs = this.totalData
        .filter(
          item =>
            !selectIDs.includes(item.id) && !this.requiredIds.includes(item.id)
        )
        .map(item => item.id)

      this.loading = true

      post(saveOrUpdate, {
        userNo: this.role.userNo,
        selectIDs: selectIDs,
        unSelectIDs: unSelectIDs
      }).then(res => {
        this.loading = false
        if (res.success) {
          this.$message.success('分配成功！')
          this.close()
          this.$emit('refresh')
          this.$root.$emit('menu-permission-updated')
        } else {
          this.$message.error('分配失败！', res.message)
        }
      })
    },

    handleMenu(menuData, addMenu) {
      const addList = []
      addMenu.forEach(item => {
        if (!item.parentId) return
        if (
          !menuData.find(i => {
            return i.id && i.id === item.parentId
          })
        ) {
          const match = this.totalData.find(i => i.id === item.parentId)
          match && menuData.push(match)
          match && addList.push(match)
        }
      })
      if (addList.length) {
        this.handleMenu(menuData, addList)
      }
    },

    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style scoped>
</style>
