<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            inline
          >
            <el-form-item
              label="状态"
              prop="status"
            >
              <el-select
                v-model="form.status"
                size="small"
                clearable
                style="width: 130px"
                @change="getAssistanceInfo(true)"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="getAssistanceInfo()"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          :data="tableData"
          size="small"
          border
          style="width: 100%"
        >
          <el-table-column
            label="帮扶内容"
            prop="alertContent"
            min-width="100"
          />
          <el-table-column
            label="发生时间"
            prop="createDateTime"
            width="140"
          />
          <el-table-column
            label="备注"
            prop="remark"
            min-width="100"
          />
          <el-table-column
            label="状态"
            prop="status"
            width="80"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.status == 1"
                type="danger">未完成</el-tag>
              <el-tag
                v-if="row.status == 2"
                type="success">已完成</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            prop="createDateTime"
            width="144"
          >
            <template v-slot="{ row }">
              <el-button
                type="text"
                @click="handleEdit(row)">编辑</el-button>
              <el-button
                type="text"
                @click="AssistanceInfoDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
      </el-row></div>
      <Edit
        ref="modalForm"
        @success="getAssistanceInfo"
      />
    </div>
  </div>
</template>

<script>
import {
  AssistanceInfoDelete, //删除
  AssistanceInfo, //按多条件查找分页
  AssistanceInfoFinish //完成
} from '@/api/system'
import { post } from '@/lib/Util'
import Edit from './component/editAssistanceInfo'
export default {
  name: 'AssistanceInfo',
  layout: 'menuLayout',
  components: {
    Edit
  },
  data() {
    return {
      //表单
      form: {
        status: ''
      },
      //分页
      page: {
        align: 'right',
        pageIndex: 1,
        pageSize: 10,
        total: 0
      },
      tableData: [
        {
          alertAdvice: '请核实现场情况，除尘风机电流显示，下午2点才停运',
          alertAnalysis: '信号显示除尘风机下午两点才停电，报警正确',
          alertContent:
            '轧机-除尘【除尘风机：停电：预计停机20分钟以上】在2024-04-09 09:01:53未执行停机计划',
          alertLevel: 2,
          alertTypeName: '经济运行制度报警',
          areaName: '轧机',
          classes: '2',
          createDateTime: '2024-04-09 20:08:19',
          createUserNo: '017553',
          dealTime: 1712664499000,
          dealUser: '李亚飞',
          deviceName: '轧机-除尘',
          falseFeedBack: '请核实现场情况，除尘风机电流显示，下午2点才停运',
          id: '1777502156585177089',
          isConfirm: 0,
          isConfirmFalse: 1,
          isFalse: 0,
          moduleCode: 'ems',
          moduleName: '智慧能源',
          msgID: '1777502156585177089',
          remark: '',
          roleID: '0f3ef77b-3ec7-4b95-8488-32d41d2e1876',
          status: 2,
          team: '3',
          updateDateTime: '2024-04-13 10:40:31',
          updateUserNo: '340502199406170210',
          warningRuleID: '1561956219738918914',
          warningType: 4
        }
      ],
      statusList: [
        {
          value: 1,
          name: '未完成'
        },
        {
          value: 2,
          name: '已完成'
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.getAssistanceInfo()
  },
  methods: {
    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    //删除
    AssistanceInfoDelete(item) {
      this.$confirm('是否确认继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.AssistanceInfoDelete1(item)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    AssistanceInfoDelete1(item) {
      console.log('删除', item)
      post(AssistanceInfoDelete, {
        id: item.id
      }).then(res => {
        if (res.success == true) {
          this.getAssistanceInfo()
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    // 表单数据
    getAssistanceInfo(reset = false) {
      if (reset) {
        this.page.pageIndex = 1
      }
      post(AssistanceInfo, {
        pageIndex: this.page.pageIndex,
        pageSize: this.page.pageSize,
        status: this.form.status
      }).then(res => {
        this.tableData = res.data.content
        this.page.total = res.data.totalElements
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.pageSize = val
      this.getAssistanceInfo()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.pageIndex = val
      this.getAssistanceInfo()
    },
    handleReset() {
      this.form = {}
      this.getAssistanceInfo(true)
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.widget-list {
  flex: 1;
  overflow: auto;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
