<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '用户'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="150px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="所属组织"
          prop="orgCode"
        >
          <!--          <el-input-->
          <!--            v-model="formData.orgCode"-->
          <!--            :style="{width: '100%'}"-->
          <!--            clearable-->
          <!--            placeholder="请输入所属组织"-->
          <!--          />-->
          <select-org 
            v-model="formData.orgCode" 
            :multiple="false"/>
        </el-form-item>
        <el-form-item
          label="人员编号"
          prop="userNo"
        >
          <el-input
            v-model="formData.userNo"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入人员编号"
          />
        </el-form-item>
        <el-form-item
          label="人员姓名"
          prop="userName"
        >
          <el-input
            v-model="formData.userName"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入人员姓名"
          />
        </el-form-item>
        <el-form-item
          label="性别"
          prop="sex"
        >
          <el-select
            v-model="formData.sex"
            :style="{width: '100%'}"
            clearable
            placeholder="请选择性别"
          >
            <el-option
              v-for="(item, index) in sexOptions"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="手机号"
          prop="mobPhone"
        >
          <el-input
            v-model="formData.mobPhone"
            :maxlength="11"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入手机号"
            prefix-icon="el-icon-mobile"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          label="证件类型"
          prop="cerType"
        >
          <el-select
            v-model="formData.cerType"
            :style="{width: '100%'}"
            clearable
            placeholder="请选择证件类型"
          >
            <el-option
              v-for="(item, index) in cerTypeOptions"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="证件号码"
          prop="idNum"
        >
          <el-input
            v-model="formData.idNum"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入证件号码"
          />
        </el-form-item>
        <el-form-item
          label="账号"
          prop="loginId"
        >
          <el-input
            v-model="formData.loginId"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入账号"
          />
        </el-form-item>
        <el-form-item
          label="出生日期"
          prop="birthDay"
        >
          <el-date-picker
            v-model="formData.birthDay"
            :style="{width: '100%'}"
            clearable
            format="yyyy-MM-dd"
            placeholder="请选择出生日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item
          label="员工邮箱"
          prop="email"
        >
          <el-input
            v-model="formData.email"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入员工邮箱"
          />
        </el-form-item>
        <el-form-item
          label="国籍"
          prop="nationality"
        >
          <el-input
            v-model="formData.nationality"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入国籍"
          />
        </el-form-item>
        <el-form-item
          label="民族"
          prop="folk"
        >
          <el-input
            v-model="formData.folk"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入民族"
          />
        </el-form-item>
        <el-form-item
          label="政治面貌"
          prop="politicsName"
        >
          <el-input
            v-model="formData.politicsName"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入政治面貌"
          />
        </el-form-item>
        <el-form-item
          label="文化程度"
          prop="academicDeg"
        >
          <el-input
            v-model="formData.academicDeg"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入文化程度"
          />
        </el-form-item>
        <el-form-item
          label="微信号"
          prop="wechatNo"
        >
          <el-input
            v-model="formData.wechatNo"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入微信号"
          />
        </el-form-item>
        <el-form-item
          label="固定电话"
          prop="tel"
        >
          <el-input
            v-model="formData.tel"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入固定电话"
          />
        </el-form-item>
        <el-form-item
          label="工作状态"
          prop="workStatus"
        >
          <el-select
            v-model="formData.workStatus"
            :style="{width: '100%'}"
            clearable
            placeholder="请选择工作状态"
          >
            <el-option
              v-for="(item, index) in workStatusOptions"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="工号"
          prop="jobNo"
        >
          <el-input
            v-model="formData.jobNo"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入工号"
          />
        </el-form-item>
        <el-form-item
          label="发薪公司别"
          prop="payCompId"
        >
          <el-input
            v-model="formData.payCompId"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入发薪公司别"
          />
        </el-form-item>
        <el-form-item
          label="职工类别"
          prop="empCategory"
        >
          <el-select
            v-model="formData.empCategory"
            :style="{width: '100%'}"
            clearable
            placeholder="请选择职工类别"
          >
            <el-option
              v-for="(item, index) in empCategoryOptions"
              :key="index"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="判重项目值域"
          prop="repeatField"
        >
          <el-input
            v-model="formData.repeatField"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入判重项目值域"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remarks"
        >
          <el-input
            v-model="formData.remarks"
            :style="{width: '100%'}"
            clearable
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { roleAdd, roleEdit, userAdd, userEdit } from '@/api/system'
import SelectOrg from '@/components/SelectOrg'

export default {
  components: { SelectOrg },
  mixins: [EditMixins],
  inheritAttrs: false,
  // eslint-disable-next-line vue/require-prop-types
  data() {
    return {
      visible: false,
      url: {
        edit: userEdit,
        add: userAdd
      },
      formData: {
        orgCode: undefined,
        userNo: undefined,
        userName: undefined,
        sex: undefined,
        mobPhone: undefined,
        cerType: undefined,
        idNum: undefined,
        loginId: undefined,
        birthDay: undefined,
        email: undefined,
        nationality: undefined,
        folk: undefined,
        politicsName: undefined,
        academicDeg: undefined,
        wechatNo: undefined,
        tel: undefined,
        workStatus: undefined,
        jobNo: undefined,
        payCompId: undefined,
        empCategory: undefined,
        repeatField: undefined,
        remarks: undefined
      },
      rules: {
        orgCode: [
          {
            required: true,
            message: '请输入所属组织',
            trigger: 'blur'
          }
        ],
        userNo: [
          {
            required: true,
            message: '请输入人员编号',
            trigger: 'blur'
          }
        ],
        userName: [
          {
            required: true,
            message: '请输入人员姓名',
            trigger: 'blur'
          }
        ],
        sex: [
          {
            required: true,
            message: '请选择性别',
            trigger: 'change'
          }
        ],
        mobPhone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          },
          {
            pattern: /^1(3|4|5|7|8|9)\d{9}$/,
            message: '手机号格式错误',
            trigger: 'blur'
          }
        ],
        cerType: [
          {
            required: true,
            message: '请选择证件类型',
            trigger: 'change'
          }
        ],
        idNum: [
          {
            required: true,
            message: '请输入证件号码',
            trigger: 'blur'
          }
        ],
        loginId: [
          {
            required: true,
            message: '请输入账号',
            trigger: 'blur'
          }
        ],
        birthDay: [
          {
            required: true,
            message: '请选择出生日期',
            trigger: 'change'
          }
        ],
        // email: [
        //   {
        //     required: true,
        //     message: '请输入员工邮箱',
        //     trigger: 'blur'
        //   }
        // ],
        nationality: [
          {
            required: true,
            message: '请输入国籍',
            trigger: 'blur'
          }
        ],
        folk: [
          {
            required: true,
            message: '请输入民族',
            trigger: 'blur'
          }
        ],
        politicsName: [
          {
            required: true,
            message: '请输入政治面貌',
            trigger: 'blur'
          }
        ],
        academicDeg: [
          {
            required: true,
            message: '请输入文化程度',
            trigger: 'blur'
          }
        ],
        // wechatNo: [
        //   {
        //     required: true,
        //     message: '请输入微信号',
        //     trigger: 'blur'
        //   }
        // ],
        // tel: [
        //   {
        //     required: true,
        //     message: '请输入固定电话',
        //     trigger: 'blur'
        //   }
        // ],
        workStatus: [
          {
            required: true,
            message: '请选择工作状态',
            trigger: 'change'
          }
        ],
        // jobNo: [
        //   {
        //     required: true,
        //     message: '请输入工号',
        //     trigger: 'blur'
        //   }
        // ],
        payCompId: [
          {
            required: true,
            message: '请输入发薪公司别',
            trigger: 'blur'
          }
        ],
        empCategory: [
          {
            required: true,
            message: '请选择职工类别',
            trigger: 'change'
          }
        ],
        repeatField: [
          {
            required: true,
            message: '请输入判重项目值域',
            trigger: 'blur'
          }
        ]
      },
      sexOptions: [
        {
          label: '男',
          value: '1'
        },
        {
          label: '女',
          value: '2'
        }
      ],
      cerTypeOptions: [
        {
          label: '身份证',
          value: '1'
        },
        {
          label: '护照',
          value: '2'
        },
        {
          label: '台胞证',
          value: '3'
        },
        {
          label: '其他证件',
          value: '4'
        }
      ],
      workStatusOptions: [
        {
          label: '在职',
          value: '1'
        },
        {
          label: '休假',
          value: '2'
        }
      ],
      empCategoryOptions: [
        {
          label: '正式工',
          value: '1'
        },
        {
          label: '顶岗实习生',
          value: '2'
        },
        {
          label: '劳务派遣工',
          value: '3'
        },
        {
          label: '返聘工',
          value: '4'
        },
        {
          label: '钟点工',
          value: '5'
        }
      ]
    }
  },
  computed: {},
  watch: {},
  created() {
    console.log('编辑页面')
  },
  methods: {}
}
</script>
<style scoped>
</style>
