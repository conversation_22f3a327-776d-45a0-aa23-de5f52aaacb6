<template>
  <div
    slot="content"
    class="content">
    <div
      id="ems-chart"
      style="height: 300px;width: 100%"/>
  </div>
</template>

<script>
export default {
  name: 'WidgetSmartEnergy',
  layout: 'staticPage',
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    setTimeout(() => {
      this.init()
    }, 0)
  },
  methods: {
    init() {
      //
      this.chart = this.$echarts.init(
        document.getElementById('ems-chart'),
        'light'
      )
      this.chart.setOption({
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: '电消耗',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: '加热耗电' },
              { value: 735, name: '轧制耗电' },
              { value: 580, name: '供辅耗电' }
            ]
          }
        ]
      })
    }
  }
}
</script>

<style scoped>
.content {
  height: 100%;
  background: #fff;
}
</style>
