<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="search-wrapper">
        <el-form
          ref="searchForm"
          :label-width="'80px'"
          :model="searchForm"
          size="small"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label=""
            prop="userNo"
          >
            <el-input
              v-model="searchForm.userNo"
              clearable

              size="small"
              placeholder="请输入员工编号"
              style="width: 200px"
              suffix-icon="el-icon-search"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label="开始时间"
            prop="startTime"
          >
            <el-date-picker
              v-model="searchForm.startTime"
              type="datetime"
              placeholder="选择日期时间"/>
          </el-form-item>
          <el-form-item
            label="结束时间"
            prop="endTime"
          >
            <el-date-picker
              v-model="searchForm.endTime"
              type="datetime"
              placeholder="选择日期时间"/>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button
          size="small"
          @click="handleReset"
        >重置
        </el-button>
      </div>
    </div>
    <div class="page-card shadow-light">
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        class="custom-table"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
        />
        <el-table-column
          label="员工编号"
          prop="userNo"
        />
        <el-table-column
          label="IP"
          prop="ipAddress"
        />
        <el-table-column
          label="服务名称"
          prop="serviceNo"
        />
      </el-table>
      <el-row
        align="middle"
        class="table-pagination"
        justify="end"
        type="flex"
      >
        <el-pagination
          :current-page="page.pageIndex"
          :page-size="page.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </div>
  </div>
 
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { pageLogSecret } from '@/api/system'

export default {
  layout: 'menuLayout',
  name: 'visit10-visitLog',
  mixins: [listMixins],
  data: () => {
    return {
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: pageLogSecret.appLogMultiCondition //分页接口地址
      },
      editUserId: null
    }
  },
  methods: {
    //
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
</style>
