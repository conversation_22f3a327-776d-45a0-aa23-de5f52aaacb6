<template>
  <div>
    <el-form
      ref="loginForm"
      :model="formData"
      :rules="rules"
      @keydown.enter.native="login">
      <el-form-item
        class="form-item"
        prop="loginName">
        <el-input
          v-model="formData.loginName"
          placeholder="请输入用户名"
          size="large"
        >
          <template slot="prepend">
            <i class="el-icon-user"/>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        class="form-item"
        prop="loginPwd">
        <el-input
          v-model="formData.loginPwd"
          placeholder="请输入密码"
          size="large"
          type="password">
          <template slot="prepend">
            <i class="el-icon-lock"/>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="btnLoading"
          class="width-full"
          long
          type="primary"
          size="large"
          style="width: 100%"
          @click="login">登录</el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="btnLoading"
          class="third-btn"
          long
          type="text"
          size="large"
          style="width: 100%"
          @click="redirectLogin">
          <img
            src="../../../assets/images/icon-click.svg"
            alt=""
          >
        <em>南钢统一认证登陆</em></el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { post } from '@/lib/Util'
import { login } from '@/api/system'
import MD5 from 'md5'
import { getMenus, redirectLogin } from '@/lib/Menu'

export default {
  name: 'LoginForm',
  props: {
    // 登录名规则
    loginNameRules: {
      type: Array,
      default: () => {
        return [{ required: true, message: '账号不能为空', trigger: 'blur' }]
      }
    },
    // 密码规则
    loginPwdRules: {
      type: Array,
      default: () => {
        return [{ required: true, message: '密码不能为空', trigger: 'blur' }]
      }
    }
  },
  data() {
    return {
      // 防止重复提交 按钮加载状态
      btnLoading: false,
      formData: {
        loginName: '',
        loginPwd: ''
      },
      codeUrl: ''
    }
  },
  computed: {
    rules() {
      return {
        loginName: this.loginNameRules,
        loginPwd: this.loginPwdRules
      }
    }
  },
  mounted() {},
  methods: {
    // 登录
    login() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.btnLoading = true
          post(login, {
            userNo: this.formData.loginName,
            password: MD5(this.formData.loginPwd)
          }).then(res => {
            this.btnLoading = false
            console.log(res)
            if (res.success) {
              localStorage.getItem('userId') !== this.formData.loginName &&
                this.$store.commit('desktop/miniAppList', [])
              localStorage.setItem('userId', this.formData.loginName)
              localStorage.setItem('token', res.data.token)
              getMenus(this.$store)
              if (res.data.isModifiedPwd === 'N') {
                // 密码码没修改过跳转修改密码
                this.$router.replace('/password')
              } else {
                // 缓存用户名  token
                this.$router.replace('/')
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      })
    },
    // 南钢登陆
    redirectLogin() {
      redirectLogin()
    }
  }
}
</script>
<style scoped lang="less">
/deep/ .el-input-group__prepend {
  font-size: 26px;
  padding: 0 10px;
  background: #c7c9cc;
  color: #fff;
}
/deep/ .el-input__inner {
  height: 50px;
}
/deep/ .el-button {
  height: 50px;
  font-size: 16px;
}
.form-item {
  margin-bottom: 25px;
}
.third-btn {
  img {
    vertical-align: middle;
    width: 30px;
    animation: flick 1000ms infinite;
    -webkit-animation: flick 1000ms infinite;
  }
  em {
    vertical-align: middle;
  }
}
@keyframes flick {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  to {
    opacity: 1;
  }
}
</style>
