<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="模块："
              prop="module"
            >
              <el-select
                v-model="searchForm.module"
                size="small"
                placeholder="选择模块"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="时间"
              prop="quesType"
            >
              <template>
                <el-date-picker
                  v-model="searchForm.week"
                  :placeholder="'选择日期'"
                  :append-to-body="true"
                  :format="'yyyy-MM-dd'"
                  :value-format="'yyyy-MM-dd'"
                  style="width: 130px"/>
              </template>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="时间"
            prop="weekDate"
            show-overflow-tooltip
            width="120"
          >
            <template v-slot="{ row }">
              {{ row.weekDate }}
            </template>
          </el-table-column>
          <el-table-column
            label="模块"
            prop="module"
            width="140"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                disable-transitions
              >{{ getName('moduleList', row.module).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="本日总结"
            prop="currWeekSummary"
          >
            <template v-slot="{ row }">
              <el-tooltip 
                class="item" 
                effect="dark"
                placement="top">
                <div class="one-line">
                  <span v-if="row.warning">
                    <el-tag
                      :type="'warning'"
                      disable-transitions
                    >
                      <i class="el-icon-warning-outline" />
                    </el-tag>
                  </span>
                  {{ row.currWeekSummary }}
                </div>
                <div
                  slot="content"
                  v-html="formatText(row.currWeekSummary)"/>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="明日计划"
            prop="nextWeekPlan"
          >
            <template
              v-slot="{row}"
            >
              <el-tooltip
                class="item"
                effect="dark"
                placement="top">
                <div class="one-line">
                  {{ row.nextWeekPlan }}
                </div>
                <div
                  slot="content"
                  v-html="formatText(row.nextWeekPlan)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="220"
            style="white-space: nowrap"
          >
            <template
              slot-scope="{row,$index}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail($index)"
                >详情
                </el-button>
              </span>
              <template>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :ques-type="searchForm.quesType"
      :detail="showDetail"
      :module-list="moduleList"
      @success="handleSearch"
      @next="next"
      @prev="prev"
    />
    <Edit
      ref="modalForm"
      :module-list="moduleList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlFindByDictCode,
  matterTop,
  weeklySummaryDelete,
  weeklySummaryFind,
  weeklySummarySave
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Detail from './component/detail'

export default {
  layout: 'menuLayout',
  name: 'system-summary',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {
        mode: 'daterange',
        type: 1
      },
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: weeklySummaryFind, //分页接口地址
        delete: weeklySummaryDelete, //删除接口地址
        save: weeklySummarySave,
        getDict: dictionaryDtlFindByDictCode
      },
      moduleList: [],
      workList: ENUM.workList,
      userId: null,
      editRole: null,
      serviceList: [],
      timer: null,
      activeIndex: 0,
      mode: 'daterange'
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    },
    week: function() {
      return this.searchForm.week
        ? `第${this.$moment(this.searchForm.week).week() + 1}周（${this.$moment(
            this.searchForm.week
          )
            .add(0, 'days')
            .format('yyyy-MM-DD')} - ${this.$moment(this.searchForm.week)
            .add(6, 'days')
            .format('yyyy-MM-DD')}）`
        : ''
    }
  },
  async created() {
    const { data: module } = await post(this.url.getDict, {
      dictCode: 'module'
    })
    this.moduleList = module.map(item => {
      return {
        value: item.code,
        label: item.value
      }
    })
    this.handleSearch(true)
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    beforeHandleSearch() {
      if (this.searchForm.week) {
        this.searchForm.startDate = this.$moment(this.searchForm.week).format(
          'yyyy-MM-DD'
        )
        this.searchForm.endDate = this.$moment(this.searchForm.week).format(
          'yyyy-MM-DD'
        )
      } else {
        this.searchForm.startDate = ''
        this.searchForm.endDate = ''
      }
    },

    afterHandleSearch() {
      //
      this.tableData.forEach(item => {
        if (
          item.handleStatus !== 1 &&
          this.$moment().format('yyyy-MM-DD') > item.planCompleteDate
        ) {
          item.warning = true
        }
      })
    },

    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },

    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },

    next() {
      console.log(this.activeIndex, this.page.pageIndex)
      if (this.activeIndex < this.tableData.length - 1) {
        this.activeIndex++
      } else if (this.page.pageIndex < this.page.totalPages) {
        console.log(this.page.pageIndex)
        this.page.pageIndex++
        this.handleSearch()
        this.activeIndex = 0
      } else {
        this.$message.info('已经是最后一条了')
      }
    },
    prev() {
      if (this.activeIndex > 0) {
        this.activeIndex--
      } else if (this.page.pageIndex > 1) {
        this.page.pageIndex--
        this.handleSearch()
        this.activeIndex = this.page.pageSize - 1
      } else {
        this.$message.warning('已经是第一条了')
      }
    },
    tableRowClassName({ row }) {
      if (row.matterType === 4) {
        return 'warning-row'
      }
      return ''
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },

    weeks(date) {
      return date ? '第' + (this.$moment(date).week() + 1) + '周' : ''
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
