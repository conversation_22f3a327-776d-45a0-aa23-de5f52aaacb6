<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '工作计划'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1000px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择模块',
              trigger: 'change'
            }
          ]"
          label="模块："
          prop="module"
        >
          <el-select
            v-model="formData.module"
            size="small"
            filterable
            placeholder="选择模块"
            @change="clearModel"
          >
            <el-option
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <div
          v-for="(item, index) in formData.planList"
          :key="index"
          class="model-item"
        >
          <el-form-item
            :rules="[
              {
                required: true,
                message: '请输入工作计划',
                trigger: 'change'
              }
            ]"
            :prop="'planList.' + index + '.currWeekSummary'"
            label="工作计划："
          >
            <el-button
              v-if="formData.planList.length >= 2"
              style="float: right"
              size="small"
              @click="handleDelete(item, index)"
            >删除
            </el-button>
            <el-input
              v-model="item.currWeekSummary"
              :rows="4"
              type="textarea"
              style="width: calc(100% - 80px)"
              placeholder="请输入工作计划"
            />
          </el-form-item>
          <el-row
            :gutter="20"
          >
            <el-col
              :span="12"
            >
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入项目组负责人',
                    trigger: 'blur'
                  }
                ]"
                :prop="'planList.' + index + '.projectTeamLeader'"
                label="项目组负责人"
              >
                <el-input
                  v-model="item.projectTeamLeader"
                  placeholder="请输入项目组负责人" />
              </el-form-item>
            </el-col>
            <el-col
              :span="12"
            >
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入板材负责人',
                    trigger: 'blur'
                  }
                ]"
                :prop="'planList.' + index + '.sheetMaterialLeader'"
                label="板材负责人"
              >
                <el-input
                  v-model="item.sheetMaterialLeader"
                  placeholder="请输入板材负责人" />
              </el-form-item>

            </el-col>
          </el-row>
          <el-row
            :gutter="20"
          >
            <el-col
              :span="12"
            >
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入负责单位',
                    trigger: 'blur'
                  }
                ]"
                :prop="'planList.' + index + '.responsibleUnit'"
                label="负责单位"
              >
                <el-input
                  v-model="item.responsibleUnit"
                  placeholder="请输入负责单位" />
              </el-form-item>
            </el-col>
            <el-col
              :span="12"
            />
          </el-row>

          <el-row
            :gutter="20"
          >
            <el-col
              :span="12"
            >
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请选择日期',
                    trigger: 'blur'
                  }
                ]"
                :prop="'planList.' + index + '.workArrangeDate'"
                label="工作安排日期"
              >
                <el-date-picker
                  v-model="item.workArrangeDate"
                  :value-format="'yyyy-MM-dd'"
                  type="date"
                  style="width: 100%"
                  placeholder="选择时间" />
              </el-form-item>
            </el-col>
            <el-col
              :span="12"
            >
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请选择日期',
                    trigger: 'blur'
                  }
                ]"
                :prop="'planList.' + index + '.plannCompleteDate'"
                label="计划完成日期"
              >
                <el-date-picker
                  v-model="item.plannCompleteDate"
                  :value-format="'yyyy-MM-dd'"
                  type="date"
                  style="width: 100%"
                  placeholder="选择时间" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item
            :prop="'planList.' + index + '.completed'"
            label="处理状态"
          >
            <template v-for="sitem in statusList">
              <el-radio
                v-model="item.completed"
                :key="sitem.value"
                :label="sitem.value">{{ sitem.label }}</el-radio>
            </template>
          </el-form-item>
          <el-form-item
            :prop="'planList.' + index + '.explanation'"
            label="未完成情况说明"
          >
            <el-input
              v-model="item.explanation"
              :rows="2"
              type="textarea"
              placeholder="请输入未完成情况说明" />
          </el-form-item>
        </div>
        <div class="text-center">
          <el-button @click="addModel">添加计划</el-button>
        </div>
      </el-form>

      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  evaluationDelete,
  evaluationFind,
  evaluationSave,
  evaluationSaveAll,
  matterSave,
  saveFeedback,
  uploadFile,
  weeklySummaryDelete,
  weeklySummaryFind,
  weeklySummarySave,
  weeklySummarySaveList
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import * as _ from 'lodash'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    inputUnitList: {
      type: Array,
      default: function() {
        return []
      }
    },
    planList: {
      type: Array,
      default: function() {
        return []
      }
    },
    valueTypeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: weeklySummarySaveList,
        add: weeklySummarySaveList,
        list: weeklySummaryFind,
        delete: weeklySummaryDelete
      },
      formData: {
        module: null,
        planList: [
          {
            completed: '0'
          }
        ]
      },
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {
    week: function() {
      return this.formData.weekDate
        ? `第${this.$moment(this.formData.weekDate).week()}周（${this.$moment(
            this.formData.weekDate
          )
            .startOf('isoWeek')
            .format('yyyy-MM-DD')} - ${this.$moment(this.formData.weekDate)
            .endOf('isoWeek')
            .format('yyyy-MM-DD')}）`
        : ''
    }
  },
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    addModel() {
      this.formData.planList.push(
        Object.assign(
          _.cloneDeep(
            this.formData.planList[this.formData.planList.length - 1]
          ),
          {
            currWeekSummary: '',
            completed: '0',
            explanation: '',
            id: ''
          }
        )
      )
    },
    deleteModel(index) {
      this.formData.planList.splice(index, 1)
    },
    clearModel() {},
    weekChange() {
      this.$nextTick(() => {
        this.formData.weekDate = this.$moment(this.formData.weekDate)
          .startOf('isoWeek')
          .add(4, 'days')
          .format('yyyy-MM-DD')
      })
    },
    /**
     * 开启编辑
     * @param editData 编辑元数据
     */
    async edit(editData) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, editData)
      // 查询模型列表
      if (!this.url || !this.url.list) {
        this.$message.warning('请设置url.list属性!')
        return
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: 1,
          pageSize: 1000,
          module: this.formData.module,
          plannCompleteStartDate: this.formData.plannCompleteDate,
          plannCompleteEndDate: this.formData.plannCompleteDate
        })
      )
      this.formData.planList = data ? data.content : [{}]
      this.loading = false
    },
    handleDelete(data, index) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      if (data.id) {
        this.$confirm('是否确认删除此数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 删除操作
          post(this.url.delete, { id: data.id }).then(res => {
            // 删除成功
            this.deleteModel(index)
          })
        })
      } else {
        this.deleteModel(index)
      }
    },
    submitBefore() {
      // 提交前操作
      this.formData.type = 2
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {
        module: null,
        planList: [
          {
            completed: '0'
          }
        ]
      }
    },
    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          const params = {
            data: this.formData.planList.map(item => {
              item.module = this.formData.module
              item.type = 2
              return item
            })
          }
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            this.submitBefore()
            post(this.url.edit, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            this.submitBefore()
            post(this.url.add, params).then(res => {
              this.loading = false
              if (res.success) {
                this.close()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    }
  }
}
</script>
<style
  scoped
  lang="less"
>
.model-item {
  border: 1px solid #dedddd;
  padding: 20px 20px 20px 0;
  margin-bottom: 10px;
}
</style>
