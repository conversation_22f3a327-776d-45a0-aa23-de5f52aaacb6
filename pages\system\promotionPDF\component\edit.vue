<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '操作手册'"
      :width="'600px'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        ref="form"
        :model="formData"
        label-width="160px"
        size="medium"
        @keyup.enter.native="handelConfirm"
      >
        <el-form-item
          label="服务名称"
          prop="cname"
        >
          {{ formData.cname }}
        </el-form-item>
        <el-form-item
          label="服务编号"
          prop="name"
        >
          {{ formData.name }}
        </el-form-item>
        <el-form-item
          label="上传文件"
          prop="roleCode"
        >
          <div
            v-if="formData.videoIds && formData.videoIds.length"
            class="video-list">
            <template v-for="item in formData.videoIds">
              <p
                :key="item.id"
                class="video-item">
                <span>

                  <a :href="url.download + item.id">
                    <i
                      :title="item.name"
                      class="play-icon el-icon-reading"/>
                  </a>
                  <em @click="showVideo(item.id)">{{ item.name }}</em>
                </span>
                <i
                  title="删除"
                  class="delete-icon el-icon-delete"
                  @click="deleteVideo(item.id)"/>
              </p>
            </template>
          </div>
          <el-upload
            ref="upload"
            :auto-upload="false"
            :http-request="httpRequest"
            class="upload-demo"
            action="#">
            <el-button
              slot="trigger"
              size="small"
              type="primary">选取文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  downloadFileById,
  roleAdd,
  roleEdit,
  uploadFile,
  uploadImg,
  userAdd,
  userEdit
} from '@/api/system'
import { post } from '@/lib/Util'
import { imgIp } from '@/config'

export default {
  components: {},
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    relatedId: {
      type: String,
      default: ''
    },
    typeList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      url: {
        upload: uploadFile,
        download: imgIp + downloadFileById
      },
      formData: {},
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('角色编辑页面')
  },
  methods: {
    handelConfirm() {
      const files = this.$refs.upload.uploadFiles
      if (!files.length) {
        return this.$message.warning('请选择视频')
      }
      console.log(files)
      if (!files.every(item => item.name.endsWith('pdf'))) {
        return this.$message.warning('只能上传PDF文件')
      }
      this.loading = true
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      formData.append('relatedId', this.relatedId)
      formData.append('serviceNo', this.formData.name)
      post(this.url.upload, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        console.log(res)
        this.loading = false
        if (res.success) {
          this.$refs.upload.clearFiles()
          this.close()
        }
      })
    },
    showVideo(id) {
      this.$emit('show', id)
    },
    deleteVideo(id) {
      post(deleteFileByIds, { ids: [id] }).then(res => {
        this.formData.videoIds = this.formData.videoIds.filter(
          item => item.id !== id
        )
        this.$message.success('删除成功')
      })
    },
    httpRequest(params) {}
  }
}
</script>
<style scoped lang="less">
.video-list {
  border: 1px solid #eee;
  margin-bottom: 5px;
}
.video-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #eee;
  &:last-child {
    border: none;
  }
  .play-icon {
    font-size: 26px;
    cursor: pointer;
    vertical-align: middle;
  }
  .delete-icon {
    font-size: 18px;
    cursor: pointer;
  }
  em {
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
