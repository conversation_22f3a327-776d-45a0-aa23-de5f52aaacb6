<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start"
      >
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="工号"
              prop="module"
            >
              <el-input
                v-model="searchForm.userNo"
                style="width: 100px"
                placeholder="工号"
              />
            </el-form-item>
            <el-form-item
              :prop="'projectTeamLeader'"
              label="姓名"
            >
              <el-input
                v-model="searchForm.userName"
                style="width: 100px"
                placeholder="姓名"
              />
            </el-form-item>
            <el-form-item
              label="时间范围"
              prop="quesType"
            >
              <el-date-picker
                v-model="searchForm.dateTime"
                :type="'daterange'"
                :append-to-body="true"
                :format="'yyyy-MM-dd'"
                :clearable="false"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 340px"
              />
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap"
        >
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="申请人"
            prop="createUserName"
            width="140"
          />
          <!--          <el-table-column-->
          <!--            label="申请权限"-->
          <!--            show-overflow-tooltip-->
          <!--            prop="newRscList"-->
          <!--          />-->
          <el-table-column
            label="申请时间"
            prop="createDateTime"
          />
          <el-table-column
            label="审批人"
            prop="approveUserName"
          />
          <el-table-column
            label="状态"
            prop="status"
          >
            <template v-slot="{ row }">
              <el-tag
                :type="getName('statusList', row.status).type"
                disable-transitions
              >
                {{ getName('statusList', row.status).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            style="white-space: nowrap"
            width="270"
          >
            <template
              slot-scope="{row,$index}"
            >

              <span>
                <el-button
                  slot="reference"
                  type="text"
                  @click="handleDetail($index)"
                >详情
                </el-button>
                <el-divider direction="vertical" />
              </span>
              <template>
                <span v-if="row.status === 0">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                  <el-divider direction="vertical" />
                </span>
                <template v-if="row.approveUserNo === userNo">
                  <span
                    v-command="'/operation/permissionApply/approve'"
                    v-if="row.status === 0"
                  >
                    <el-button
                      size="small"
                      type="text"
                      @click="handleApprove(row, 1)"
                    >审批通过
                    </el-button>
                    <el-divider direction="vertical" />
                  </span> <span
                    v-command="'/operation/permissionApply/refuse'"
                    v-if="row.status === 0"
                  >
                    <el-button
                      size="small"
                      type="text"
                      @click="handleApprove(row, 9)"
                    >驳回
                    </el-button>
                    <el-divider direction="vertical" />
                  </span>
                </template>
                <span
                  v-command="'/operation/permissionApply/handle'"
                  v-if="row.status === 1"
                >
                  <el-button
                    size="small"
                    type="text"
                    @click="handleApprove(row, 3)"
                  >处理
                  </el-button>
                  <el-divider direction="vertical" />
                </span> <span>
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleApprove(row, -1)"
                  >删除
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :detail="showDetail"
      :status-list="statusList"
      @success="handleSearch"
    />
    <Edit
      ref="modalForm"
      :status-list="statusList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  menuApplyDelete,
  menuApplyFind,
  menuApplySave,
  menuApplyUpdate
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import Detail from './component/detail'
import { post } from '@/lib/Util'

export default {
  layout: 'menuLayout',
  name: 'operation-permissionApply',
  components: {
    Detail,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {},
      description: '',
      url: {
        list: menuApplyFind, //分页接口地址
        delete: menuApplyDelete, //删除接口地址
        save: menuApplyUpdate
      },
      moduleList: [],
      statusList: [
        {
          label: '驳回',
          type: 'info',
          value: 9
        },
        {
          label: '待审批',
          type: '',
          value: 0
        },
        {
          label: '已审批，待处理',
          type: 'warning',
          value: 1
        },
        // {
        //   label: '待处理',
        //   type: 'warning',
        //   value: 2
        // },
        {
          label: '已处理',
          type: 'success',
          value: 3
        }
      ],
      workList: ENUM.workList,
      timer: null,
      activeIndex: 0,
      editRow: {},
      userNo: ''
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    }
  },
  async created() {
    this.userNo = localStorage.getItem('userId')
    this.handleSearch(true)
  },
  destroyed() {},
  methods: {
    beforeHandleSearch() {
      if (this.searchForm.date && this.searchForm.date.length) {
        this.searchForm.startTime = this.searchForm.date[0]
        this.searchForm.endTime = this.searchForm.date[1]
      }
    },
    afterHandleSearch() {},
    getName: function(list, status) {
      return this[list].find(item => item.value === status) || {}
    },
    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },
    tableRowClassName({ row }) {
      return ''
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    },
    handleApprove(row, status) {
      let text = null
      if (status === 1) {
        text = '审批通过'
      }
      if (status === 3) {
        text = '已处理'
      }
      if (status === 9) {
        text = '驳回'
      }
      if (status === -1) {
        text = '删除'
      }
      this.$confirm('是否确认' + text + '此申请?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 操作
        post(this.url.save, Object.assign({}, row, { status })).then(res => {
          // 成功
          this.handleSearch()
        })
      })
    },
    weeks(date) {
      return date ? '第' + (this.$moment(date).week() + 1) + '周' : ''
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
