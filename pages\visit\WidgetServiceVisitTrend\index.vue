<template>
  <div class="page-content">
    <div class="page-content-wrapper">
      <div class="page-card full-height shadow-light">
        <div class="form">
          <div class="form-left">
            排名方式：
            <el-radio-group v-model="type">
              <el-radio-button label="app">应用</el-radio-button>
              <el-radio-button label="user">用户</el-radio-button>
              <el-radio-button label="org">单位</el-radio-button>
            </el-radio-group>
            <div
              v-if="type === 'org'"
              class="org-radio">
              <el-radio
                v-model="org"
                label="org-1">厂处</el-radio>
              <el-radio
                v-model="org"
                label="org-2">科室</el-radio>
              <el-radio
                v-model="org"
                label="org-3">班组</el-radio>
            </div>
          </div>
          <div class="form-right">
            <el-tooltip
              effect="dark"
              content="导出"
              placement="top-center">
              <i
                title="导出"
                class="refresh el-icon-download"
                @click="handleVisible"/>
            </el-tooltip>
            <el-tooltip
              effect="dark"
              content="刷新"
              placement="top-center">
              <i
                title="刷新"
                class="refresh el-icon-refresh"
                @click="getData"/>
            </el-tooltip>
            <i
              title="排序"
              class="refresh el-icon-sort"
              @click="sortChart"/>
            <el-radio-group v-model="mode">
              <el-radio-button label="today">今日</el-radio-button>
              <el-radio-button label="month">当月</el-radio-button>
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
            <span
              v-if="mode === 'custom'"
              class="custom">
              {{ formData.loginTimeStart }} - {{ formData.loginTimeEnd }}
              <el-button
                type="text"
                @click="customVisible = true">
                更改
              </el-button>
            </span>
          </div>
        </div>
        <div
          id="trend-chart"
          class="chart1"/>
      </div>
      <el-dialog
        :visible.sync="visible"
        :title="'报表导出'"
        :width="'450px'">
        <el-form
          v-if="visible"
          ref="exportForm"
          :model="formData"
          label-width="100px"
          size="medium"
          @keyup.enter.native="handelExport"
        >
          <el-form-item
            :label="'模式：'"
            prop="title"
          >
            <el-radio
              v-model="exportMode"
              label="date">按日期</el-radio>
            <el-radio
              v-model="exportMode"
              label="month">按月份</el-radio>
            <el-radio
              v-model="exportMode"
              label="year">按年份</el-radio>
            <el-radio
              v-model="exportMode"
              label="week">周报</el-radio>
          </el-form-item>
          <el-form-item
            v-if="exportMode === 'week'"
            :label="'选择时间：'"
            :rules="[
              {
                required: true,
                message: '请选择周',
                trigger: 'change'
              }
            ]"
            prop="week">
            <el-date-picker
              v-model="formData.week"
              :type="exportMode"
              :clearable="false"
              :value-format="valueFormat"
              :placeholder="'选择周'"
              format="yyyy 第 WW 周"/>
          </el-form-item>
          <template v-else>
            <el-form-item
              :label="'开始时间：'"
              :rules="[
                {
                  required: true,
                  message: '请选择时间',
                  trigger: 'change'
                }
              ]"
              prop="loginTimeStart"
            >
              <el-date-picker
                v-model="formData.loginTimeStart"
                :type="exportMode"
                :clearable="false"
                :value-format="valueFormat"
                :placeholder="'选择时间'"/>
            </el-form-item>
            <el-form-item
              :label="'结束时间：'"
              :rules="[
                {
                  required: true,
                  message: '请选择时间',
                  trigger: 'change'
                }
              ]"
              prop="loginTimeEnd"
            >
              <el-date-picker
                v-model="formData.loginTimeEnd"
                :type="exportMode"
                :clearable="false"
                :value-format="valueFormat"
                :placeholder="'选择时间'" />
            </el-form-item>
          </template>
        </el-form>
        <div slot="footer">
          <el-button
            :loading="loading"
            type="primary"
            @click="handelExport"
          >确定
          </el-button>
        </div>
      </el-dialog>
      <el-dialog
        :visible.sync="customVisible"
        :title="'自定义时间选择'"
        :width="'380px'">
        <el-form
          v-if="customVisible"
          ref="customForm"
          :model="formData"
          label-width="100px"
          size="medium"
          @keyup.enter.native="handelCustom"
        >
          <el-form-item
            :label="'模式：'"
            prop="title"
          >
            <el-radio
              v-model="exportMode"
              label="date">按日期</el-radio>
            <el-radio
              v-model="exportMode"
              label="month">按月份</el-radio>
            <el-radio
              v-model="exportMode"
              label="year">按年份</el-radio>
          </el-form-item>
          <el-form-item
            :label="'开始时间：'"
            :rules="[
              {
                required: true,
                message: '请选择时间',
                trigger: 'change'
              }
            ]"
            prop="loginTimeStart"
          >
            <el-date-picker
              v-model="formData.loginTimeStart"
              :type="exportMode"
              :value-format="valueFormat"
              :placeholder="'选择时间'" />
          </el-form-item>
          <el-form-item
            :label="'结束时间：'"
            :rules="[
              {
                required: true,
                message: '请选择时间',
                trigger: 'change'
              }
            ]"
            prop="loginTimeEnd"
          >
            <el-date-picker
              v-model="formData.loginTimeEnd"
              :type="exportMode"
              :value-format="valueFormat"
              :placeholder="'选择时间'" />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button
            :loading="loading"
            type="primary"
            @click="handelCustom"
          >确定
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { post } from '@/lib/Util'
import listMixins from '@/mixins/ListMixins'
import {
  exportExcelByLoginTime,
  exportWordByLoginTime,
  findAlertInfo,
  findBasicDataConfigByType,
  findServiceVisitNumByLoginTime
} from '@/api/system'
import * as _ from 'lodash'
import JSZipUtils from 'jszip-utils'
import docxtemplater from 'docxtemplater'
import { saveAs } from 'file-saver'
import PizZip from 'pizzip'
import moment from 'moment'

export default {
  name: 'visit-WidgetServiceVisitTrend',
  layout: 'menuLayout',
  data() {
    return {
      chart: null,
      mode: 'today',
      type: 'app',
      org: 'org-1',
      serviceList: [],
      showData: [],
      sort: 'desc',
      visible: false,
      formData: {
        loginTimeStart: this.$moment().format('YYYY-MM-DD'),
        loginTimeEnd: this.$moment().format('YYYY-MM-DD'),
        week: null
      },
      loading: false,
      exportMode: 'date', // 导出模式

      customVisible: false,
      customMode: 'date' // 自定义模式
    }
  },
  computed: {
    valueFormat: function() {
      let format = ''
      switch (this.exportMode) {
        case 'year':
          format = 'yyyy'
          break
        case 'month':
          format = 'yyyy-MM'
          break
        case 'date':
          format = 'yyyy-MM-dd'
          break
        default:
          format = 'yyyy-MM-dd'
      }
      return format
    }
  },
  watch: {
    mode(value) {
      if (value === 'custom') {
        this.customVisible = true
      } else {
        this.getData()
      }
    },
    type(value) {
      this.getData()
    },
    org(value) {
      this.getData()
    },
    exportMode() {
      let format = ''
      switch (this.exportMode) {
        case 'date':
          format = 'YYYY-MM-DD'
          break
        case 'month':
          format = 'YYYY-MM'
          break
        case 'year':
          format = 'YYYY'
          break
        default:
          format = 'YYYY-MM-DD'
          break
      }
      this.formData.loginTimeStart = this.$moment(
        this.formData.loginTimeStart
      ).format(format)
      this.formData.loginTimeEnd = this.$moment(
        this.formData.loginTimeEnd
      ).format(format)
    }
  },
  mounted() {
    this.drawChart()
    this.getData()
  },
  methods: {
    // 排序
    async sortChart() {
      //
      this.showData = _.sortBy(this.showData, 'num')
      if (this.sort === 'desc') {
        this.showData.reverse()
        this.sort = 'asc'
      } else {
        this.sort = 'desc'
      }
      this.updateChart()
    },
    // 获取参数
    async getData() {
      let date = ''
      switch (this.mode) {
        case 'today':
          date = this.$moment().format('YYYY-MM-DD')
          this.getTrendData(date, date)
          break
        case 'month':
          date = this.$moment().format('YYYY-MM')
          this.getTrendData(date, date)
          break
        case 'all':
          date = this.$moment().format('YYYY')
          this.getTrendData(date, date)
          break
        case 'custom':
          this.getTrendData(
            this.formData.loginTimeStart,
            this.formData.loginTimeEnd
          )
          break
        default:
          break
      }
    },
    // 请求接口
    async getTrendData(start, end) {
      this.chart.showLoading()
      const { data, success } =
        (await post(findServiceVisitNumByLoginTime, {
          loginTimeStart: start,
          loginTimeEnd: end,
          type: this.type === 'org' ? this.org : this.type
        })) || []
      if (!success) return this.chart.hideLoading()
      this.sort = 'desc'
      this.showData = data
      this.updateChart()
    },
    // 自定义确认
    handelCustom() {
      this.customVisible = false
      this.getTrendData(
        this.formData.loginTimeStart,
        this.formData.loginTimeEnd
      )
    },
    // 更新图表
    updateChart() {
      this.chart.setOption({
        xAxis: {
          type: 'category',
          data: this.showData.map(item => item.name)
        },
        series: [
          {
            data: this.showData.map(item => {
              item.value = item.num
              return item
            }),
            type: 'bar'
          }
        ],
        dataZoom: [
          {
            show: true,
            start: 0,
            end:
              this.showData.length > 20
                ? (20 / this.showData.length) * 100
                : 100
          }
        ]
      })
      this.chart.resize()
      this.chart.hideLoading()
    },
    // 绘制图表
    async drawChart() {
      this.chart = this.$echarts.init(
        document.getElementById('trend-chart'),
        'light'
      )
      this.chart.setOption({
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '访问量'
        },
        dataZoom: [
          {
            show: true,
            start: 0,
            end: 0
          }
        ],
        tooltip: {
          trigger: 'axis',
          formatter: params => {
            console.log(params)
            return (
              params[0].data.name +
              '<br>' +
              (params[0].data.orgName
                ? `单位：${params[0].data.orgName}</br>`
                : ``) +
              '访问次数：' +
              params[0].data.num
            )
          },
          axisPointer: {
            type: 'shadow',
            label: {
              show: true
            }
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '50',
          containLabel: true
        },
        series: [
          {
            data: [],
            type: 'bar',
            label: {
              show: true,
              position: 'top',
              valueAnimation: true
            }
          }
        ]
      })
      // 监听窗口改动
      window.onresize = () => {
        this.chart.resize()
      }
    },
    // 获取服务信息
    async getServiceInfo() {
      const serviceData = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (serviceData.content) {
        dataContent = JSON.parse(serviceData.content)
        serviceData.content = dataContent
        if (serviceData.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          serviceData.content = dataTypeContent
        }
      }
      this.serviceList = serviceData.content
      return new Promise(resolve => resolve(true))
    },
    // 导出
    async handelExport() {
      this.$refs['exportForm'].validate(async valid => {
        if (!valid) return
        this.loading = true
        if (this.exportMode === 'week') {
          // 导出周报
          return this.exportWord()
        }
        post(exportExcelByLoginTime, this.formData, false, {
          responseType: 'blob'
        }).then(res => {
          const url = window.URL.createObjectURL(res)
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute(
            'download',
            '系统访问量排行(' +
              this.formData.loginTimeStart +
              '-' +
              this.formData.loginTimeEnd +
              ').xls'
          )
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          // 关闭弹窗
          this.visible = false
          this.loading = false
        })
      })
    },
    // 显示弹窗
    handleVisible() {
      this.visible = true
    },
    async exportWord() {
      const { data } = await post(exportWordByLoginTime, {
        loginTimeStart: this.$moment(this.formData.week)
          .add(-3, 'days')
          .format('yyyy-MM-DD'),
        loginTimeEnd: this.$moment(this.formData.week)
          .add(3, 'days')
          .format('yyyy-MM-DD')
      })
      // return console.log(data)
      // 读取并获得模板文件的二进制内容
      JSZipUtils.getBinaryContent('/template.docx', (error, content) => {
        // 抛出异常
        if (error) {
          throw error
        }

        // 创建一个PizZip实例，内容为模板的内容
        let zip = new PizZip(content)
        // 创建并加载docxtemplater实例对象
        let doc = new docxtemplater().loadZip(zip)
        // 去除未定义值所显示的undefined
        doc.setOptions({
          nullGetter: function() {
            return ''
          }
        }) // 设置角度解析器
        // 设置模板变量的值，对象的键需要和模板上的变量名一致，值就是你要放在模板上的值

        doc.setData({
          week: this.$moment(this.formData.week).week(),
          dateRange:
            this.$moment(this.formData.week)
              .add(-3, 'days')
              .format('yyyy年MM月DD日') +
            '-' +
            this.$moment(this.formData.week)
              .add(3, 'days')
              .format('yyyy年MM月DD日'),
          num: data['summary'][0].num,
          useNum: data['summary'][0].useNum,
          date: this.$moment().format('yyyy 年 MM 月 DD 日'),
          app: this.setIndex(data.app || []),
          // 厂级前十名
          orgOneTop: this.setIndex(data['org-1'].slice(0, 10)),
          // 科室前五名
          orgTwoTop: this.setIndex(data['org-2'].slice(0, 5)),
          // 科室后五名
          orgTwoLast: this.setIndex(
            data['org-2'].slice(
              data['org-2'].length <= 10 ? 5 : data['org-2'].length - 5,
              data['org-2'].length
            )
          ),
          // 班组前10名
          orgThreeTop: this.setIndex(data['org-3'].slice(0, 10)),
          // 班组后10名
          orgThreeLast: this.setIndex(
            data['org-3'].slice(
              data['org-3'].length <= 20 ? 10 : data['org-3'].length - 10,
              data['org-3'].length
            )
          ),
          // 用户前10名
          user: this.setIndex(data.user.slice(0, 10) || []),
          // feedBack-recom 合理化建议
          // feedBack-bug  问题反馈
          recomHandledNum: data['feedBack-recom'].handledNum,
          recomTotalNum: data['feedBack-recom'].totalNum,
          recomUnHandledNum: data['feedBack-recom'].unHandledNum,
          bugHandledNum: data['feedBack-bug'].handledNum,
          bugTotalNum: data['feedBack-bug'].totalNum,
          bugUnHandledNum: data['feedBack-bug'].unHandledNum
        })

        try {
          // 用模板变量的值替换所有模板变量
          doc.render()
        } catch (error) {
          // 抛出异常
          let e = {
            message: error.message,
            name: error.name,
            stack: error.stack,
            properties: error.properties
          }
          console.log(JSON.stringify({ error: e }))
          throw error
        }

        // 生成一个代表docxtemplater对象的zip文件（不是一个真实的文件，而是在内存中的表示）
        let out = doc.getZip().generate({
          type: 'blob',
          mimeType:
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })
        // 将目标文件对象保存为目标类型的文件，并命名
        saveAs(
          out,
          `南钢板材事业部全流程智能制造协同运营平台使用统计周报表（第${moment(
            this.formData.week
          ).week()}周）`
        )
      })
      this.loading = false
    },
    // 设置index
    setIndex(list) {
      return list.map((item, index) => {
        item.index = index + 1
        return item
      })
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.content {
  background: #fff;
  height: 100%;
}
.form {
  padding: 25px 20px 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .refresh {
    font-size: 24px;
    vertical-align: middle;
    margin-right: 10px;
    cursor: pointer;
  }
  .form-left {
    position: relative;
    .org-radio {
      position: absolute;
      right: 0;
      top: 115%;
      z-index: 9;
    }
  }
  .form-right {
    position: relative;
    .custom {
      position: absolute;
      top: 100%;
      right: 0;
      z-index: 9;
    }
  }
}
.chart1 {
  flex-grow: 1;
  height: calc(100% - 65px);
}
/deep/ .el-radio {
  margin-right: 10px;
}
.page-content {
  display: flex;
  flex-direction: column;
  .page-content-wrapper {
    margin: -10px;
    padding: 10px;
    flex: 1;
    overflow: auto;
  }
  .page-left {
    display: flex;
    flex-direction: column;
    .page-item {
      flex: 1;
      margin: 0 -10px;
    }
  }
  .divider-hold {
    height: 25px;
  }
  .page-card {
    display: flex;
    flex-direction: column;
    .page-inner {
      flex: 1;
    }
    .operate {
      text-align: right;
      margin-top: 10px;
    }
  }
}
//#trend-chart {
//  flex: 1;
//}
</style>
