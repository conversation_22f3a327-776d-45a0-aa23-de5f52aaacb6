<template>
  <div
    ref="box"
    class="win"
  >
    <windows-dialog/>
    <template v-for="(item, index) in list">
      <div
        v-if="item.children && item.children.length"
        :key="index"
        :ref="'item' + item.id"
        :class="{
          'dragging': item.dragStatus,
          'drop-top': item.drop === 'top',
          'drop-bottom': item.drop === 'bottom',
          'drop-merge': item.drop === 'merge',
          'righting': index === rightMenuIndex}"
        class="app-item"
        draggable="true"
        @dragend="dragEnd($event, item.id, index)"
        @dragover="dragOver($event, item.id, index)"
        @dragstart="dragStart($event, item.id, index)"
        @contextmenu.prevent="openMenu($event, item.id, index)"
      >
        <div
          :class="{'open': index === openDirIndex}"
          class="app-inner app-dir">
          <div
            class="app-dir-inner"
            @dblclick="openDir(index)">
            <span
              v-for="(child, cIndex) in item.children"
              :key="index + cIndex"
              class="inner-app-item"
              draggable="true"
              @dragend.stop="childDragEnd($event, child.id, cIndex, index)"
              @dragstart.stop="childDragStart($event, child.id, cIndex, index)"
              @click.prevent.stop="clickItem(child)"
              @dblclick.prevent.stop="openPage(child)">
              <icon-svg
                :key="cIndex"
                :icon-name="child.icon"
                class="c-svg"
              />
              <div
                v-if="index === openDirIndex"
                class="app-tit"
              >{{ child.name }}</div>
            </span>

            <div
              v-if="index === openDirIndex"
              class="app-dir-tit"
            >{{ item.name }}</div>
          </div>
        </div>
        <div class="app-tit">{{ item.name }}</div>
      </div>
      <div
        v-else
        :key="index"
        :ref="'item' + item.id"
        :class="{
          'dragging': !item.status,
          'drop-top': item.drop === 'top',
          'drop-bottom': item.drop === 'bottom',
          'drop-merge': item.drop === 'merge',
          'righting': index === rightMenuIndex}"
        class="app-item"
        draggable="true"
        @dragend="dragEnd($event, item.id, index)"
        @dragover="dragOver($event, item.id, index)"
        @dragstart="dragStart($event, item.id, index)"
        @contextmenu.prevent="openMenu($event, item.id, index)"
      >
        <div
          class="app-inner"
          @dblclick.prevent.stop="openPage(item)">
          <icon-svg
            :icon-name="item.icon"
            style="user-select: none"
            class="c-svg"
          />
          <div class="app-tit">{{ item.name }}</div>
        </div>

      </div>
    </template>
    <!-- 右键菜单 -->
    <ul
      v-show="rightMenuVisible"
      ref="rightMenu"
      :style="{ left: rightMenuLeft + 'px', top: rightMenuTop + 'px' }"
      class="contextmenu"
    >
      <li>重命名</li>
      <li>删除</li>
      <li>自定义文件夹</li>
    </ul>
  </div>
</template>

<script>
import lodash from 'lodash'
import SideBar from '@/layouts/component/SideBar'
import { post } from '@/lib/Util'
import { findRootResource } from '@/api/desktop'
import WindowsDialog from '@/components/index/WindowsDialog'

export default {
  name: 'Windows',
  components: { WindowsDialog, SideBar },
  data() {
    return {
      dragIndex: null, // 拖拽中index
      childDragIndex: null, // 拖拽中chid index
      overIndex: null, // 拖拽覆盖index
      boxRef: null, // 父级容器
      mergeTimer: null, // 合并动作定时器
      mergeTemp: null, // 合并数据缓存
      merging: false, // 是否正在合并
      rightMenuVisible: false,
      rightMenuLeft: 0,
      rightMenuTop: 0,
      rightMenuIndex: null,
      openDirIndex: null,
      list: []
    }
  },
  watch: {
    rightMenuVisible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    openDirIndex(value) {
      if (value !== null) {
        document.body.addEventListener('click', this.closeDir)
      } else {
        document.body.removeEventListener('click', this.closeDir)
      }
    },
    overIndex: function(val) {
      this.list.forEach((item, index) => {
        if (index !== val) {
          item.drop = null
        }
      })
    }
  },
  created() {
    this.getRootResource()
  },
  mounted() {
    this.boxRef = this.$refs.box
  },
  methods: {
    getRootResource() {
      post(findRootResource, {
        userNo: localStorage.getItem('userId'), //传入
        // userNo: '021179', //传入
        type: 'menu',
        serviceName: ''
      }).then(res => {
        console.log(res)
        this.list = res.data.map(item => {
          item.drop = null
          return item
        })
      })
    },
    dragStart(e, id, index) {
      // console.log('开始t拖拽', e)
      this.closeMenu()
      this.closeDir()
      this.list[index].dragStatus = true
      this.dragIndex = index
      // this.list.splice(this.dragIndex, 1)
    },
    dragEnd(e, id, index) {
      // console.log('结束', e, index)
      this.list[index].dragStatus = false // 取消拖拽状态
      if (this.dragIndex === this.overIndex) return
      if (this.overIndex === null) return
      this.list[this.overIndex].drop = null // 取消选中
      // console.log(this.overIndex, this.list[this.overIndex])
      const target = this.$refs['item' + this.list[this.overIndex].id][0] // 拖拽覆盖目标
      if (e.clientY - this.boxRef.offsetTop < target.offsetTop - 10) {
        // 插入目标元素上方
        const source = this.list[this.dragIndex]
        this.list.splice(this.dragIndex, 1)
        this.list.splice(
          this.dragIndex < this.overIndex ? this.overIndex - 1 : this.overIndex,
          0,
          source
        )
        // console.log('插在', this.overIndex, '上方')
      } else if (e.clientY - this.boxRef.offsetTop > target.offsetTop + 40) {
        // 插入目标元素下方
        const source = this.list[this.dragIndex]
        this.list.splice(this.dragIndex, 1)
        this.list.splice(
          this.dragIndex > this.overIndex ? this.overIndex + 1 : this.overIndex,
          0,
          source
        )
        // console.log('插在', this.overIndex, '下方')
      } else {
        // 合并
        this.mergeTemp = null
        this.list.splice(this.dragIndex, 1)
        // console.log('=======================', '合并完成')
      }
      this.overIndex = null
    },
    dragOver(e, id, index) {
      // console.log('over', index)
      if (this.overIndex !== index) {
        // 更换拖拽目标
        this.cancelMerge()
      }
      if (this.dragIndex === index) return this.cancelMerge() // 拖拽是本身则取消
      this.overIndex = index
      const target = this.$refs['item' + id][0] // 拖拽覆盖目标
      // 合并范围 target.offsetTop - 15   ~   target.offsetTop + 15
      // 在上方则插入上方，在下方则插入下方
      if (e.clientY - this.boxRef.offsetTop < target.offsetTop - 10) {
        console.log('top', this.list[index])
        this.list[index].drop = 'top'
        this.cancelMerge()
      } else if (e.clientY - this.boxRef.offsetTop > target.offsetTop + 40) {
        console.log('bottom', this.list[index])
        this.list[index].drop = 'bottom'
        this.cancelMerge()
      } else {
        if (this.merging) return
        this.list[index].drop = null
        this.merging = true // 开启merge状态
        this.mergeTimer = setTimeout(() => {
          console.log('开启合并')
          this.mergeTemp = lodash.cloneDeep(this.list[index]) // 暂存合并前drop位置数据
          const merge = this.list[index],
            beMerge =
              this.childDragIndex === null
                ? this.list[this.dragIndex]
                : this.list[this.dragIndex].children[this.childDragIndex]
          if (merge.children && merge.children.length) {
            if (beMerge.children && beMerge.children.length) {
              // 多 => 多
              merge.children.push(...beMerge.children)
            } else {
              // 1 => 多
              merge.children.push(beMerge)
            }
          } else {
            if (beMerge.children && beMerge.children.length) {
              // 多 => 1
              merge.children = [lodash.cloneDeep(merge), ...beMerge.children]
            } else {
              // 1 => 1
              merge.children = [
                lodash.cloneDeep(merge),
                lodash.cloneDeep(beMerge)
              ]
              merge.children.forEach(item => (item.dragStatus = false))
            }
          }
          this.list[index].drop = 'merge'
        }, 40)
      }
    },

    // 文件夹内图标拖拽
    childDragStart(e, id, cIndex, index) {
      //
      this.dragIndex = index
      this.childDragIndex = cIndex
      setTimeout(() => {
        this.closeMenu()
        this.closeDir()
      }, 300)
    },

    // 文件夹内图标拖拽
    childDragEnd(e, id, cIndex, index) {
      //
      console.log('子结束', cIndex, index, this.childDragIndex)
      if (this.overIndex === this.dragIndex) return
      if (this.overIndex === null) return
      this.list[this.overIndex].drop = null // 取消选中
      // console.log(this.overIndex, this.list[this.overIndex])
      const target = this.$refs['item' + this.list[this.overIndex].id][0] // 拖拽覆盖目标
      if (e.clientY - this.boxRef.offsetTop < target.offsetTop - 10) {
        // 插入目标元素上方
        const source = this.list[index].children[cIndex]
        this.list[index].children.splice(cIndex, 1)
        this.list.splice(this.overIndex, 0, source)
        console.log('插在', this.overIndex, '上方')
        const newIndex = this.overIndex > index ? index : index + 1 // 原文件夹的新位置
        if (this.list[newIndex].children.length === 1) {
          // 删除文件夹
          const source = lodash.cloneDeep(this.list[newIndex].children[0])
          this.list.splice(newIndex, 1, source)
        }
      } else if (e.clientY - this.boxRef.offsetTop > target.offsetTop + 40) {
        // 插入目标元素下方
        const source = this.list[index].children[cIndex]
        this.list[index].children.splice(cIndex, 1)
        this.list.splice(this.overIndex + 1, 0, source)
        console.log('插在', this.overIndex, '下方')

        const newIndex = this.overIndex > index ? index : index + 1 // 原文件夹的新位置
        if (this.list[newIndex].children.length === 1) {
          // 删除文件夹
          const source = lodash.cloneDeep(this.list[newIndex].children[0])
          this.list.splice(newIndex, 1, source)
        }
      } else {
        // 合并
        this.mergeTemp = null
        this.list[index].children.splice(cIndex, 1)
        console.log(this.list[index].children)
        if (this.list[index].children.length === 1) {
          // 删除文件夹
          const source = lodash.cloneDeep(this.list[index].children[0])
          this.list.splice(index, 1, source)
        }
        console.log('=======================', '合并完成')
      }
      this.dragIndex = null
      this.childDragIndex = null
      console.log(this.list)
    },

    cancelMerge() {
      // 取消合并，恢复缓存数据
      if (this.mergeTemp) {
        // console.log('取消', this.mergeTemp)
        this.list[this.overIndex] = this.mergeTemp
        this.list[this.overIndex].drop = null
        this.mergeTemp = null
        // console.log(this.list[this.overIndex])
      }
      this.mergeTimer && clearTimeout(this.mergeTimer)
      this.merging = false
    },

    // 右键菜单
    openMenu(e, id, index) {
      this.rightMenuIndex = index
      if (id) {
        // this.chooseCaseFolder(item, path);
      }
      const x = e.pageX
      const y = e.pageY
      this.rightMenuTop = y
      this.rightMenuLeft = x
      this.rightMenuVisible = true
    },
    closeMenu() {
      this.rightMenuVisible = false
      this.rightMenuIndex = null
    },
    openDir(index) {
      if (index === this.openDirIndex) return
      this.openDirIndex = index
    },
    closeDir(index) {
      this.openDirIndex = null
    },
    openPage(data) {
      console.log(data)
      // this.$message.success('打开页面')
      this.$bus.$emit(
        'open-iframe',
        data.name,
        'http://' +
          data.ip +
          ':' +
          data.port +
          data.url +
          '?org=redirect&token=' +
          localStorage.getItem('token') +
          '&userId=' +
          localStorage.getItem('userId')
      )
    },
    clickItem(data) {
      // console.log(data)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.win {
  height: 80vh;
  background: #f1f1f1;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: flex-start;
  padding: 20px;

  .app-item {
    position: relative;
    width: 90px;
    margin-bottom: 5px;
    transition: transform 0.5s;
    border: 1px dashed transparent;
    &.righting {
      border: 1px solid #409eff;
      background: rgba(64, 158, 255, 0.19);
    }

    &.dragging {
      opacity: 0.5;
      border: 0.5px dashed #99999990;
    }

    &.drop-top:before {
      content: '';
      position: absolute;
      width: 100%;
      top: -2px;
      left: 0;
      border-bottom: 4px solid rgba(47, 133, 231, 0.65);
    }

    &.drop-bottom:after {
      content: '';
      position: absolute;
      width: 100%;
      bottom: -2px;
      left: 0;
      border-bottom: 4px solid rgba(47, 133, 231, 0.6);
    }

    &.drop-merge .app-inner {
      transform: scale(1.5);
    }

    .app-tit {
      color: #666;
      margin-top: 3px;
      font-size: 14px;
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .app-inner {
      position: relative;
      margin: 10px;
      width: 70px;
      transition: transform 0.5s;
      .c-svg {
        font-size: 70px;
      }
    }

    .app-dir {
      position: relative;
      z-index: 9;
      height: 70px;
      .c-svg {
        font-size: 29px;
        transition: all 0.2s linear;
      }

      &.open {
        z-index: 99;
        .app-dir-inner {
          width: 180px;
          height: auto;
          box-shadow: 0 0 5px rgba(153, 153, 153, 0.5);
          .inner-app-item {
            margin-bottom: 8px;
          }
          .c-svg {
            font-size: 70px;
          }
          &:before {
            display: none;
          }
        }
      }
      .app-dir-inner {
        display: flex;
        flex-wrap: wrap;
        background: #fff;
        border-radius: 10px;
        padding: 5px;
        height: 70px;
        width: 100%;
        overflow: hidden;
        transition: all 0.2s linear;
        margin-bottom: 5px;
        .inner-app-item {
          width: 50%;
          text-align: center;
        }
        .app-dir-tit {
          text-align: center;
          padding: 5px;
          color: #666;
          font-size: 14px;
          width: 100%;
          border-top: 1px solid #ccc;
        }
        &:before {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }
      }
    }
  }
  .contextmenu {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    background: #fff;
    font-size: 14px;
    color: #666;
    border: 1px solid #e9e9e9;
    min-width: 250px;
    box-shadow: 0 0 5px rgba(153, 153, 153, 0.1);
    li {
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #f4f4f5;
      }
    }
    li:last-child {
      border-top: 1px solid #e9e9e9;
    }
  }
}
</style>
