<template>
  <div>
    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            inline
            @keyup.enter.native="getQuotaInfo(true)"
          >
            <el-form-item
              label="模块"
              prop="moduleName"
            >
              <el-select
                v-model="form.moduleCode"
                size="small"
                clearable
                style="width: 130px"
                placeholder="选择模块"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.Name"
                  :value="item.ID"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="报警级别"
              prop="alertLevel"
            >
              <el-select
                v-model="form.alertLevel"
                size="small"
                clearable
                style="width: 130px"
                placeholder="报警级别"
              >
                <el-option
                  v-for="(item, index) in alertLevelList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="status"
            >
              <el-select
                v-model="form.status"
                size="small"
                clearable
                style="width: 130px"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="text-right">
          <el-button
            icon="ios-search"
            type="primary"
            @click="getQuotaInfo()"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          :data="quotaTableData"
          size="small"
          border
          style="width: 100%"
        >
          <el-table-column
            label="模块名称"
            prop="moduleName"
            width="100"
          />
          <el-table-column
            label="规则名称"
            prop="ruleName"
            min-width="100"
          />
          <el-table-column
            label="规则描述"
            prop="ruleDesc"
            min-width="100"
          />
          <el-table-column
            label="责任人"
            prop="liablePersonName"
            width="100"
          />
          <el-table-column
            label="创建时间"
            prop="createDateTime"
            width="140"
          />
          <el-table-column
            label="本周工作"
            prop="workDesc"
            min-width="100"
          />
          <el-table-column
            label="下周计划"
            prop="plan"
            min-width="100"
          />
          <el-table-column
            label="报警等级"
            prop="alertLevel"
            width="72"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.alertLevel == 1"
                type="danger">一级</el-tag>
              <el-tag
                v-if="row.alertLevel == 2"
                type="warning">二级</el-tag>
              <el-tag
                v-if="row.alertLevel == 3">三级</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="状态"
            prop="status"
            width="80"
          >
            <template v-slot="{row}">
              <el-tag
                v-if="row.status == -1"
                type="info">已删除</el-tag>
              <el-tag
                v-if="row.status == 0"
                type="danger">未确认</el-tag>
              <el-tag
                v-if="row.status == 1"
                type="warning">已确认</el-tag>
              <el-tag
                v-if="row.status == 2"
                type="success">已完成</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            prop="createDateTime"
            width="144"
          >
            <template v-slot="{ row }">
              <el-button
                v-if="row.status == 0"
                type="text"
                @click="confirmQuotaInfo(row)">确认</el-button>
              <el-button
                v-if="row.status == 1"
                type="text"
                @click="finishInfo(row)">完成</el-button>
              <el-button
                type="text"
                @click="handleEdit(row)">编辑</el-button>
              <el-button
                type="text"
                @click="deleteInfo(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
      </el-row></div>
      <Edit
        ref="modalForm"
        @success="getQuotaInfo"
      />
    </div>
  </div>
</template>

<script>
import Edit from './component/edit'
import {
  checkQuotaInfo, //转为指标专项攻关
  confirmQuotaInfo, //确认
  deleteInfo, //删除
  findModuleInfoList, //模块
  findQuotaInfoList, //按多条件查找分页
  finishInfo //完成
} from '@/api/system'
import { post } from '@/lib/Util'
export default {
  name: 'specialTacklingOfIndicators',
  layout: 'menuLayout',
  components: {
    Edit
  },
  data() {
    return {
      //表单
      form: {
        moduleCode: '',
        alertLevel: '',
        status: ''
      },
      //分页
      page: {
        align: 'right',
        pageIndex: 1,
        pageSize: 10,
        total: 0
      },
      ruleTypeList: [
        // 0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测，6-趋势校验报,7-故障诊断
        {
          value: 0,
          name: '生产保供'
        },
        {
          value: 1,
          name: '仪表跳变'
        },
        {
          value: 2,
          name: '通信异常'
        },
        {
          value: 3,
          name: '超标报警'
        },
        {
          value: 4,
          name: '经济运行制度报警'
        },
        {
          value: 5,
          name: '生产过程监测'
        },
        {
          value: 6,
          name: '趋势校验报警'
        },
        {
          value: 7,
          name: '故障诊断'
        }
      ],
      moduleList: [],
      quotaTableData: [
        {
          alertLevel: '3',
          areaID: '4',
          areaName: '一炼钢',
          createDateTime: '2024-04-18 09:34:42',
          createUserNo: '023958',
          deviceID: '5',
          deviceName: '2#连铸机',
          id: 'd94a017c-78db-4b62-9573-e89b71a43476',
          isConfirm: 0,
          moduleCode: 'vbt',
          moduleName: '振动监测',
          pointID: '5-2',
          pointName: '2#风机2#轴承(CH2通道)',
          productionLineName: '第一炼钢厂',
          pushMode: 1,
          pushRoleID: '765e029b-f416-4b46-93be-8622363fd660',
          pushRoleName: '一炼钢2#连铸岗位',
          ruleDesc: '一炼钢-2#连铸机-2#风机2#轴承(CH2通道)-阈值报警',
          ruleName: '一炼钢-2#连铸机-2#风机2#轴承(CH2通道)-阈值报警',
          ruleType: 3,
          ruleValue: '4.6',
          status: 0
        },
        {
          alertLevel: '3',
          areaID: '4',
          areaName: '一炼钢',
          createDateTime: '2024-04-18 09:34:42',
          createUserNo: '023958',
          deviceID: '5',
          deviceName: '2#连铸机',
          id: 'd94a017c-78db-4b62-9573-e89b71a43476',
          isConfirm: 0,
          moduleCode: 'vbt',
          moduleName: '振动监测',
          pointID: '5-2',
          pointName: '2#风机2#轴承(CH2通道)',
          productionLineName: '第一炼钢厂',
          pushMode: 1,
          pushRoleID: '765e029b-f416-4b46-93be-8622363fd660',
          pushRoleName: '一炼钢2#连铸岗位',
          ruleDesc: '一炼钢-2#连铸机-2#风机2#轴承(CH2通道)-阈值报警',
          ruleName: '一炼钢-2#连铸机-2#风机2#轴承(CH2通道)-阈值报警',
          ruleType: 3,
          ruleValue: '4.6',
          status: 0
        }
      ],
      alertLevelList: [
        // 1-一级，2-二级，3-三级，0-未评级
        {
          value: 1,
          name: '一级'
        },
        {
          value: 2,
          name: '二级'
        },
        {
          value: 3,
          name: '三级'
        }
      ],
      statusList: [
        {
          value: 0,
          name: '未确认'
        },
        {
          value: 1,
          name: '已确认'
        },
        {
          value: 2,
          name: '已完成'
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.getQuotaInfo()
    this.loadData()
  },
  methods: {
    loadData() {
      post(findModuleInfoList, {}).then(res => {
        this.moduleList = res.data || []
      })
    },
    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    confirmQuotaInfo(item) {
      post(confirmQuotaInfo, {
        id: item.id
      }).then(res => {
        if (res.success == true) {
          this.$message({
            message: '已确认',
            type: 'success'
          })
          this.getQuotaInfo()
        } else {
          this.$message.error('确认失败')
        }
      })
    },
    deleteInfo(item) {
      this.$confirm('是否确认继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteInfo1(item)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    deleteInfo1(item) {
      console.log('删除', item)
      post(deleteInfo, {
        id: item.id
      }).then(res => {
        if (res.success == true) {
          this.getQuotaInfo()
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    finishInfo(item) {
      post(finishInfo, {
        id: item.id
      }).then(res => {
        if (res.success == true) {
          this.$message({
            message: '已完成',
            type: 'success'
          })
          this.getQuotaInfo()
        } else {
          this.$message.error('完成失败')
        }
      })
    },
    // 表单数据
    getQuotaInfo() {
      post(findQuotaInfoList, {
        pageIndex: this.page.pageIndex,
        pageSize: this.page.pageSize,
        moduleCode: this.form.moduleCode,
        alertLevel: this.form.alertLevel,
        status: this.form.status
      }).then(res => {
        this.quotaTableData = res.data.content
        this.page.total = res.data.totalElements
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.pageSize = val
      this.getQuotaInfo()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.pageIndex = val
      this.getQuotaInfo()
    },
    handleReset() {
      this.form = {}
      this.getQuotaInfo(true)
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-tabs__header {
  margin-bottom: 0;
}
.widget-list {
  flex: 1;
  overflow: auto;
}
.table-pagination {
  text-align: center;
  padding: 10px 0;
}
</style>
