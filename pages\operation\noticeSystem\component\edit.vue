<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '公告'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入标题',
              trigger: 'change'
            }
          ]"
          label="标题"
          prop="title"
        >
          <el-input
            v-model="formData.title"
            placeholder="请输入标题" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择时间',
              trigger: 'change'
            }
          ]"
          label="公告时间"
          prop="dateTime"
        >
          <el-date-picker
            v-model="formData.dateTime"
            :type="'datetimerange'"
            :append-to-body="true"
            :clearable="false"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="timeChange "/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入内容',
              trigger: 'change'
            }
          ]"
          label="公告内容"
          prop="content"
        >
          <el-input
            v-model="formData.content"
            :rows="6"
            type="textarea"
            placeholder="请输入内容" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择状态',
              trigger: 'change'
            }
          ]"
          label="状态"
          prop="status"
        >
          <template v-for="item in statusList">
            <el-radio
              v-model="formData.status"
              :key="item.value"
              :label="item.value">{{ item.label }}</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  matterSave,
  saveFeedback,
  sysNoticeSave,
  uploadFile,
  weeklySummarySave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: sysNoticeSave,
        add: sysNoticeSave,
        file: uploadFile
      },
      formData: {
        noticeCategory: 2
      },
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    timeChange() {
      this.$nextTick(() => {
        this.formData.startTime = this.formData.dateTime[0]
        this.formData.endTime = this.formData.dateTime[1]
      })
    },
    httpRequest(params) {},
    submitBefore() {
      // 提交前操作
      this.formData.noticeCategory = 2
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {
        dateTime: null,
        noticeCategory: 2
      }
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    /**
     * 开启编辑
     * @param data 编辑元数据
     */
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, data, {
        dateTime: [data.startTime, data.endTime]
      })
    }
  }
}
</script>
<style scoped>
</style>
