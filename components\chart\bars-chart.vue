<template>
  <div 
    :id="containerId" 
    :style="{ height: '100%' }"/>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => {
        return []
      }
    },
    chartData2: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#2772F0', '#F5B544', '#51DF81', '#FFE638', '#D45454']
      }
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    barBackground: {
      type: Boolean,
      default: false
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    labelRotate: {
      type: Number,
      default: 0
    },
    showToolbox: {
      type: Boolean,
      default: true
    },
    transverse: {
      // 是否横向
      type: Boolean,
      default: false
    },
    dataZoom: {
      // 是否横向
      type: Boolean,
      default: false
    },
    dataZoomSize: {
      // 是否横向
      type: Number,
      default: 10
    },
    barWidth: {
      type: Number,
      default: 0
    }
  },
  data: () => {
    return {
      containerId: '',
      myChart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler: function() {
        this.initChart()
      }
    }
  },
  mounted() {
    this.containerId = 'a' + this.guid()
    this.$nextTick(() => this.initChart())
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(
          document.querySelector('#' + this.containerId)
        )
        this.myChart.on('selectchanged', params => {
          this.$emit('selected', params)
        })
        window.addEventListener('resize', this.resizeChart)
      }
      if (!this.chartData.length) return
      const options = {
        tooltip: {
          show: this.showToolbox,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        color: this.color,
        legend: {
          show: this.showLegend,
          align: 'left',
          top: 15,
          right: 20,
          padding: 0,
          icon: 'circle',
          textStyle: {
            color: '#000',
            fontSize: 12
          },
          itemHeight: 10, // 修改icon图形大小
          itemWidth: 10, // 修改icon图形大小
          itemGap: 3, // 修改间距
          itemStyle: {
            borderWidth: 0,
            padding: 0
          }
        },
        grid: {
          top: this.showLegend ? '10%' : '20px',
          left: '20',
          right: '20',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [
          {
            type: this.transverse ? 'value' : 'category',
            axisTick: { show: false },
            data: this.xData,
            axisLabel: {
              color: '#1d1f25',
              fontSize: 13,
              interval: 0,
              rotate: this.labelRotate || 0
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#EAEBF0',
                type: 'dashed'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#ccc',
                type: 'dashed'
              }
            }
          }
        ],
        yAxis: [
          {
            type: this.transverse ? 'category' : 'value',
            data: this.xData,
            axisLine: { show: false },
            axisLabel: {
              color: '#8590B3',
              fontSize: 12
            },
            axisTick: {
              show: false
            }
          },
          {
            name: '%',
            type: 'value',
            yAxisIndex: 1,
            nameTextStyle: {
              color: '#fff'
            },
            axisLabel: {
              color: '#666',
              fontSize: 12,
              align: 'left'
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#2e4262'
              }
            }
          }
        ],
        series: this.chartData
          .map(item => {
            return {
              name: item.name,
              type: 'bar',
              barGap: 0,
              barWidth: this.barWidth || 60 / this.chartData.length + '%',
              barMaxWidth: this.barWidth || 20,
              showBackground: this.barBackground,
              backgroundStyle: {
                color: 'rgba(232, 236, 239, 0.3)'
              },
              markPoint: {
                symbolSize: 5
              },
              label: {
                show: this.showLabel,
                color: '#334681FF',
                position: 'top',
                fontSize: 14,
                offset: [0, 0]
              },
              data: item.data
            }
          })
          .concat(
            this.chartData2.map(item => {
              return {
                name: item.name,
                type: 'bar',
                barGap: 0,
                yAxisIndex: 1,
                barWidth: this.barWidth || 60 / this.chartData.length + '%',
                barMaxWidth: this.barWidth || 20,
                showBackground: this.barBackground,
                backgroundStyle: {
                  color: 'rgba(232, 236, 239, 0.3)'
                },
                markPoint: {
                  symbolSize: 5
                },
                label: {
                  show: this.showLabel,
                  color: '#334681FF',
                  position: 'top',
                  fontSize: 14,
                  offset: [0, 0]
                },
                data: item.data
              }
            })
          )
      }
      if (this.dataZoom && this.chartData[0].data.length) {
        // 计算dataZoom范围
        const length = this.chartData[0].data.length
        const zoomStart = this.transverse
          ? length - this.dataZoomSize <= 0
            ? 0
            : ((length - this.dataZoomSize + 1) / length) * 100
          : 0
        const zoomEnd = this.transverse
          ? 100
          : length > this.dataZoomSize
            ? ((this.dataZoomSize + 1) / length) * 100
            : 100
        console.log(zoomStart, zoomEnd)
        options.dataZoom = [
          {
            type: 'slider',
            start: zoomStart,
            end: zoomEnd,
            yAxisIndex: this.transverse ? [0] : null,
            xAxisIndex: this.transverse ? null : [0]
          }
        ]
      }
      this.myChart.setOption(options)
    },
    resizeChart() {
      this.myChart && this.myChart.resize()
    },
    guid() {
      return 'xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0,
          v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.legend {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;

  .legend-item {
    position: relative;
    font-size: 9px;
    color: #8590b3;
    line-height: 13px;
    margin-left: 15px;
    margin-right: 10px;

    span {
      position: absolute;
      left: -12px;
      top: 4px;
      width: 7px;
      height: 7px;
      background-color: #eee;
    }

    .span1 {
      height: 2px;
      top: 6px;
      width: 10px;
      left: -15px;
    }
  }
}
</style>
