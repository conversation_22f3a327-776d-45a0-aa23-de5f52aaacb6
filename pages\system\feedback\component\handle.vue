<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'处理' + (quesType === 1 ? '合理化建议' : '问题反馈')"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入标题',
              trigger: 'change'
            }
          ]"
          label="标题"
          prop="title"
        >
          <el-input
            v-model="formData.title"
            disabled
            placeholder="请输入标题" />

        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择所属应用',
              trigger: 'change'
            }
          ]"
          label="所属应用"
          prop="serviceNo"
        >
          <el-select
            v-model="formData.serviceNo"
            size="small"
            clearable
            disabled
            placeholder="选择应用"
          >
            <el-option
              v-for="(item, index) in serviceList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 是否为数据问题下拉框 -->
        <el-form-item
          label="是否为数据问题"
          prop="isDataCause"
        >
          <el-select
            v-model="formData.isDataCause"
            size="small"
            clearable
            disabled
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in isDataCauseList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择计划完成时间',
              trigger: 'change'
            }
          ]"
          label="计划完成时间"
          prop="planFinishDate"
        >
          <el-date-picker
            v-model="formData.planFinishDate"
            placeholder="请选择时间"/>
        </el-form-item>
        <el-form-item
          label="图片"
          prop="attachList"
        >
          <ul class="el-upload-list el-upload-list--picture-card">
            <li
              v-for="(item, index) in attachList"
              :key="index"
              class="el-upload-list__item is-ready">
              <img-view
                :key="item.id"
                :id="item.id"
                @img-preview="handleImgPreview"
                @img-delete="handleImgDelete"
              />
            </li>
          </ul>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入描述',
              trigger: 'change'
            }
          ]"
          label="描述"
          prop="description"
        >
          <el-input
            v-model="formData.description"
            :rows="6"
            disabled
            type="textarea"
            placeholder="请输入描述"/>

        </el-form-item>
        <el-form-item
          label="处理人联系方式"
          prop="handleContact"
        >
          <el-input
            v-model="formData.handleContact"
            placeholder="请输入处理人联系方式"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择状态',
              trigger: 'change'
            }
          ]"
          label="处理状态"
          prop="handleStatus"
        >
          <template v-for="item in statusList">
            <el-radio
              v-model="formData.handleStatus"
              :key="item.value"
              :label="item.value">{{ item.label }}</el-radio>
          </template>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入处理说明',
              trigger: 'change'
            }
          ]"
          label="处理意见"
          prop="handleDesc"
        >
          <el-input
            v-model="formData.handleDesc"
            :rows="6"
            type="textarea"
            placeholder="请输入描述"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelSubmit"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { deleteFileByIds, saveFeedback, uploadFile } from '@/api/system'
import { post } from '@/lib/Util'
import ImgDownload from '@/components/ImgView'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    serviceList: {
      type: Array,
      default: function() {
        return []
      }
    },
    isDataCauseList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: saveFeedback,
        add: saveFeedback,
        file: uploadFile
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: [],
      statusList: [
        { label: '处理中', value: 5, type: '' },
        { label: '已处理', value: 2, type: 'success' },
        { label: '退回', value: 6, type: 'info' }
      ]
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    async handelUpload() {
      // 删除图片
      if (this.deleteIds.length) {
        const del = await post(deleteFileByIds, { ids: this.deleteIds })
      }
      //上传
      const files = this.$refs.upload.uploadFiles
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      const res = await post(this.url.file, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (res.success) {
        this.uploadFiles = res.data
        return Promise.resolve(true)
      }
      return Promise.reject(false)
    },
    httpRequest(params) {},
    async handelSubmit() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.loading = true
        this.handelConfirm()
      })
    },

    submitBefore() {
      if (!this.formData.attachList) this.formData.attachList = []
      this.formData.anonymous = !!this.formData.anonymous
      this.formData.quesType = this.quesType
      this.formData.handleUserNo = localStorage.getItem('userId')
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
