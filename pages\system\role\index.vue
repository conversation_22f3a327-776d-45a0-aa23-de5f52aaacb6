<template>
  <div>

    <div class="page-content">
      <div class="page-operate">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :label-width="'80px'"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label="角色名称"
              prop="nickname"
            >
              <el-input
                v-model="searchForm.roleName"
                clearable
                size="small"
                placeholder="请输入角色名称"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="角色编码"
              prop="nickname"
            >
              <el-input
                v-model="searchForm.roleCode"
                clearable
                size="small"
                placeholder="请输入角色编码"
                style="width: 130px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="角色类型"
              prop="roleType"
            >
              <el-select
                v-model="searchForm.roleType"
                size="small"
                placeholder="选择角色类型"
                style="width: 130px"
              >
                <el-option
                  v-for="(item, index) in roleTypeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="所属机构"
              prop="orgID"
            >
              <el-select
                v-model="searchForm.orgID"
                size="small"
                placeholder="选择所属机构"
                style="width: 140px"
              >
                <el-option
                  v-for="(item, index) in orgList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="模块"
              prop="moduleCode"
            >
              <el-select
                v-model="searchForm.moduleCode"
                size="small"
                clearable
                filterable
                placeholder="选择模块"
                style="width: 140px"
              >
                <el-option
                  v-for="(item, index) in moduleList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div>
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            v-command="'/system/role/edit'"
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
        </div>
      </div>

      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            width="50"
          />
          <el-table-column
            label="角色编码"
            prop="roleCode"
            width="100"
          />
          <el-table-column
            label="角色名称"
            prop="roleName"
          />
          <el-table-column
            label="角色类型"
            prop="roleType"
          >
            <template v-slot="{ row }">
              {{ getDict(row.roleType, 'roleTypeList').label || '' }}
            </template>
          </el-table-column>
          <el-table-column
            label="所属机构"
            prop="orgID"
          >
            <template v-slot="{ row }">
              {{ getDicts(row.orgID, 'orgList') || '' }}
            </template>
          </el-table-column>
          <el-table-column
            label="模块名称"
            prop="moduleCode"
          >
            <template
              v-slot="{row}"
            >
              {{ getDicts(row.moduleCode, 'moduleList') }}
            </template>
          </el-table-column>
          <el-table-column
            label="
                描述"
            prop="desc"
          />
          <el-table-column
            label="状态"
            prop="status"
            width="65"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="roleStatusName(row.status).type"
                disable-transitions
              >{{ roleStatusName(row.status).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="
                创建时间"
            prop="createDateTime"
          />
          <el-table-column
            label="
                更新时间"
            prop="updateDateTime"
          />
          <el-table-column
            fixed="right"
            label="操作"
            width="270"
          >
            <template
              v-slot="{row}"
            >
              <span v-if="row.status === 1">
                <template
                  v-command="'/system/role/distributeUser'"
                >
                  <el-button
                    size="small"
                    type="text"
                    @click="distributeUser(row)"
                  >分配用户
                  </el-button>
                  <el-divider direction="vertical" />
                </template>
              </span> <span v-if="row.status === 1">
                <template
                  v-command="'/system/role/distributeResource'"
                >
                  <el-button
                    size="small"
                    type="text"
                    @click="distributeResource(row)"
                  >权限管理
                  </el-button>
                  <el-divider direction="vertical" />
                </template>
              </span> <span v-if="row.status === 1">
                <template
                  v-command="'/system/role/edit'"
                >
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                  <el-divider direction="vertical" />
                </template>
              </span> <span>
                <el-button
                  v-command="'/system/role/edit'"
                  slot="reference"
                  type="text"
                  @click="handleChangeStatus(row)"
                >{{ row.status === 1 ? '禁用' : '启用' }}
                </el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Edit
      ref="modalForm"
      :org-list="orgList"
      :module-list="moduleList"
      :role-type-list="roleTypeList"
      @success="handleSearch"
    />
    <DistributeUser
      v-if="visibleDistribute"
      :role="editRole"
      :visible.sync="visibleDistribute"
    />
    <DistributeResource
      v-if="visibleResource"
      :role="editRole"
      :visible.sync="visibleResource"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import DistributeUser from './component/distributeUser'
import DistributeResource from './component/distributeResource'
import listMixins from '@/mixins/ListMixins'
import {
  dictionaryDtlFindByDictCode,
  orgListByCode,
  roleDelete,
  roleList
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'

export default {
  layout: 'menuLayout',
  name: 'system-role',
  components: {
    Edit,
    DistributeUser,
    DistributeResource
  },
  mixins: [listMixins],
  data: () => {
    return {
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: roleList, //分页接口地址
        delete: roleDelete, //删除接口地址
        getDict: dictionaryDtlFindByDictCode
      },
      roleStatusList: ENUM.roleStatus,
      editRole: null,
      orgList: [],
      moduleList: [],
      roleTypeList: [
        {
          value: 1,
          label: '页面访问角色'
        },
        {
          value: 2,
          label: '报警推送角色'
        },
        {
          value: 3,
          label: '页面编辑角色'
        },
        {
          value: 4,
          label: '消息推送角色'
        }
      ]
    }
  },
  created() {
    this.loadData()
    this.getOrgData()
  },
  methods: {
    loadData() {
      // 获取模型信息
      post(this.url.getDict, {
        dictCode: 'service'
      }).then(res => {
        this.moduleList = res.data.map(item => {
          return {
            value: item.code,
            label: item.value
          }
        })
      })
    },
    getOrgData() {
      post(orgListByCode, { orgCode: 'X50000000' }).then(res => {
        this.orgList = res.data.map(item => {
          return {
            label: item.orgAllName,
            value: item.orgCode
          }
        })
      })
    },
    distributeUser(data) {
      this.editRole = data
      this.visibleDistribute = !this.visibleDistribute
    },
    distributeResource(data) {
      this.editRole = data
      this.visibleResource = !this.visibleResource
    },
    roleStatusName: function(status) {
      return this.roleStatusList.find(item => item.value === status)
    },
    handleChangeStatus: function(data) {
      this.$confirm(
        `是否确认${data.status === 1 ? '禁用' : '启用'}此角色?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 删除操作
        post(this.url.delete, {
          id: data.id,
          status: data.status === 1 ? 0 : 1
        }).then(() => {
          this.handleSearch()
        })
      })
    },
    getDicts(str, listName) {
      console.log('str', str)
      if (!str) {
        return ''
      }
      return str
        .split(',')
        .map(item => {
          return this.getDict(item, listName).label
        })
        .join('，')
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
</style>
