<template>
  <div
    :style="{backgroundImage: backgroundImage }"
    :class="theme"
    class="home">
    <windows-header/>
    <windows-setting/>
    <nuxt class="nuxt-view"/>
    <windows-dock/>
    <windows-dialog/>
    <windows-notify/>
    <windows-copyright/>
  </div>
</template>

<script>
import hmiDialog from '@/components/index/HmiDialog'
import WindowsDialog from '@/components/index/WindowsDialog'
import WindowsDock from '@/components/index/WindowsDock'
import Websocket from '@/lib/WebSocket'
import { DesktopConfig } from '@/lib/Constant'
import WindowsHeader from '@/components/index/WindowsHeader'
import WindowsSetting from '@/components/index/WindowsSetting'
import WindowsNotify from '@/components/index/WindowsNotify'
import WindowsCopyright from '@/components/index/WindowsCopyright'
export default {
  head() {
    return {}
  },
  components: {
    WindowsCopyright,
    WindowsNotify,
    WindowsSetting,
    WindowsHeader,
    WindowsDock,
    WindowsDialog,
    hmiDialog
  },
  data() {
    return {
      loading: true
    }
  },
  computed: {
    backgroundImage: function() {
      const match = DesktopConfig.desktopWallpaper.find(
        item => item.name === this.$store.state.desktop.desktopTheme
      )
      return match ? 'url(' + match.img + ')' : null
    },
    theme: function() {
      const match = DesktopConfig.desktopWallpaper.find(
        item => item.name === this.$store.state.desktop.desktopTheme
      )
      return match ? match.theme : null
    }
  },
  beforeCreate() {},
  created() {},
  mounted() {
    this.loading = false
  },
  methods: {}
}
</script>
<style scoped lang="less">
loading-fade-enter,
loading-fade-leave-active {
  opacity: 0;
}
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  text-align: center;
  padding: 15% 0;
  background: #fff;
  transition: opacity 0.5s ease-in-out;
  .loading-text {
    color: #13356f;
    font-size: 20px;
    margin-top: 10px;
  }
  .circular {
    height: 50px;
    width: 50px;
    -webkit-animation: loading-rotate 2s linear infinite;
    animation: loading-rotate 2s linear infinite;
  }
  .path {
    -webkit-animation: loading-dash 1.5s ease-in-out infinite;
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: #13356f;
    stroke-linecap: round;
  }
  @-webkit-keyframes loading-rotate {
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes loading-rotate {
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes loading-dash {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -40px;
    }
    100% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -120px;
    }
  }
}
.home {
  display: flex;
  width: 100%;
  height: 100vh;
  min-width: 1200px;
  min-height: 700px;
  overflow: auto;
  //background: url('../assets/desktop/desktop-bg/desktopbg.png') no-repeat center;
  background: url('../assets/desktop/desktop-bg/desktopbg_light.jpg') no-repeat
    center;
  background-size: cover;
  flex-direction: column;
  transition: background-image 0.3s, background-color 0.2s, transform 0.3s;
}

.nuxt-view {
  flex: 1;
  overflow: auto;
}
</style>
