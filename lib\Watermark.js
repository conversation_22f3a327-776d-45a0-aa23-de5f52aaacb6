// 添加 watermark
export function __canvasWM({
  container = document.body,
  width = '700px',
  height = '400px',
  textAlign = 'center',
  textBaseline = 'top',
  font = '40px 宋体',
  fillStyle = 'rgba(184, 184, 184, 0.3)',
  content = '请勿外传',
  contentSub = '请勿外传-sub',
  rotate = '-20',
  zIndex = 99999
} = {}) {
  const args = arguments[0]
  const canvas = document.createElement('canvas')

  canvas.setAttribute('width', width)
  canvas.setAttribute('height', height)
  const ctx = canvas.getContext('2d')
  ctx.textAlign = textAlign
  ctx.textBaseline = textBaseline
  ctx.font = font
  ctx.fillStyle = fillStyle
  ctx.rotate((Math.PI / 180) * rotate)
  ctx.fillText(
    content,
    parseFloat(height) / 2 + 50,
    parseFloat(height) / 2 + 10
  )
  const ctx1 = canvas.getContext('2d')
  ctx1.textAlign = textAlign
  ctx1.textBaseline = textBaseline
  ctx1.font = font
  ctx1.fillStyle = fillStyle
  ctx1.fillText(
    contentSub,
    parseFloat(height) / 2 + 50,
    parseFloat(height) / 2 + 70
  )

  const base64Url = canvas.toDataURL()
  const __wm = document.querySelector('.__wm')

  const watermarkDiv = __wm || document.createElement('div')
  const styleStr = `
          position:absolute;
          top:0;
          left:0;
          width:100%;
          height:100%;
          z-index:${zIndex};
          pointer-events:none;
          background-repeat:repeat;
          background-image:url('${base64Url}')`

  watermarkDiv.setAttribute('style', styleStr)
  watermarkDiv.classList.add('__wm')

  if (!__wm) {
    container.style.position = 'relative'
    container.insertBefore(watermarkDiv, container.firstChild)
  }
  // 防止删除
  const MutationObserver =
    window.MutationObserver || window.WebKitMutationObserver
  if (MutationObserver) {
    let mo = new MutationObserver(() => {
      const __wm = document.querySelector('.__wm')
      // 只在__wm元素变动才重新调用 __canvasWM
      if ((__wm && __wm.getAttribute('style') !== styleStr) || !__wm) {
        // 避免一直触发
        mo.disconnect()
        mo = null
        this.__canvasWM(JSON.parse(JSON.stringify(args)))
      }
    })

    mo.observe(container, {
      attributes: true,
      subtree: true,
      childList: true
    })
  }
}

export function TpWatermark(CON, H, W, R, C, S, O) {
  // 判断水印是否存在，如果存在，那么不执行
  RemoveTpWatermark()
  let TpLine = parseInt(document.body.clientWidth / W) * 2 // 一行显示几列
  let StrLine = ''
  for (let i = 0; i < TpLine; i++) {
    StrLine +=
      '<span style="display: inline-block; height:' +
      H +
      'px; width:' +
      W +
      'px; text-align: center; transform:rotate(' +
      R +
      'deg); color:' +
      C +
      '; font-size:' +
      S +
      'px; opacity:' +
      O +
      ';"><div style="display: flex; align-items: center;justify-content: space-around;font-family: \'宋体\', serif">' +
      CON +
      '</div></span>'
  }
  let DivLine = document.createElement('div')
  DivLine.innerHTML = StrLine

  let TpColumn = parseInt(document.body.clientHeight / H) * 2 // 一列显示几行
  let StrColumn = ''
  for (let i = 0; i < TpColumn; i++) {
    StrColumn +=
      '<div style="white-space: nowrap;">' + DivLine.innerHTML + '</div>'
  }
  let DivLayer = document.createElement('div')
  DivLayer.innerHTML = StrColumn
  DivLayer.id = 'tp-watermark' // 给水印盒子添加类名
  const styleStr = `
          position:fixed;
          top:0px;
          left:-100px;
          width:100%;
          height:100%;
          z-index:${99999};
          pointer-events:none;`

  DivLayer.setAttribute('style', styleStr)
  document.body.appendChild(DivLayer) // 到页面中

  // 防止删除
  const MutationObserver =
    window.MutationObserver || window.WebKitMutationObserver
  if (MutationObserver) {
    let mo = new MutationObserver(() => {
      const __wm = document.getElementById('tp-watermark')
      // 只在__wm元素变动才重新调用 __canvasWM
      if ((__wm && __wm.getAttribute('style') !== styleStr) || !__wm) {
        // 避免一直触发
        mo.disconnect()
        moChild.disconnect()
        mo = null
        moChild = null
        TpWatermark(...arguments)
      }
    })

    mo.observe(document.body, {
      attributes: true,
      subtree: true,
      childList: true
    })

    let moChild = new MutationObserver(() => {
      // 避免一直触发
      mo.disconnect()
      moChild.disconnect()
      mo = null
      moChild = null
      TpWatermark(...arguments)
    })
    moChild.observe(DivLayer, {
      attributes: true,
      subtree: true,
      childList: true
    })
  }
}

// 移除水印方法
function RemoveTpWatermark() {
  // 判断水印是否存在，如果存在，那么执行
  if (document.getElementById('tp-watermark') == null) {
    return
  }
  document.body.removeChild(document.getElementById('tp-watermark'))
}
