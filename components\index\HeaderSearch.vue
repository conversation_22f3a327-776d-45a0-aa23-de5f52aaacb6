<template>
  <div
    class="header-search show">
    <i 
      class="el-icon-search search-icon"
      @click="show=true"/>
    <el-select
      ref="headerSearchSelect"
      v-model="search"
      :remote-method="querySearch"
      filterable
      default-first-option
      remote
      placeholder="输入关键字搜索菜单"
      class="header-search-select"
      @change="change"
    >
      <el-option 
        v-for="item in options" 
        :key="item.item.routes"
        :value="item.item"
        :label="item.item.routes" />
    </el-select>
  </div>
</template>

<script>
// fuse is a lightweight fuzzy-search module
// make search results more in line with expectations
import Fuse from 'fuse.js'
import path from 'path'
import { mapState } from 'vuex'
import { getPagesRoute } from '@/lib/Menu'

export default {
  name: 'HeaderSearch',
  data() {
    return {
      search: '',
      options: [],
      searchData: [],
      show: false,
      fuse: undefined
    }
  },
  computed: {
    routes() {
      return this.$store.state.menu.allMenus
    },
    ...mapState('menu', ['menuPages', 'allMenus'])
  },
  watch: {
    menuPages() {
      this.searchData = this.menuPages
    },
    searchData(list) {
      this.initFuse(list)
    },
    show(value) {
      if (value) {
        document.body.addEventListener('click', this.close)
      } else {
        document.body.removeEventListener('click', this.close)
      }
    }
  },
  mounted() {
    this.searchData = this.menuPages
  },
  methods: {
    click() {
      this.show = !this.show
      if (this.show) {
        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()
      }
    },
    close() {
      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()
      this.options = []
      this.show = false
    },
    change(val) {
      const menu = this.allMenus.find(item => item.id === val.id)
      this.$bus.$emit('open-iframe', menu, '_self')
      this.search = ''
    },
    initFuse(list) {
      this.fuse = new Fuse(list, {
        shouldSort: true, // 是否按分数对结果列表排序
        threshold: 0.1, // 匹配算法阈值。阈值为0.0需要完全匹配（字母和位置），阈值为1.0将匹配任何内容
        location: 0, // 确定文本中预期找到的模式的大致位置。
        distance: 100,
        maxPatternLength: 32, // 模式的最大长度
        minMatchCharLength: 1, // 模式的最小字符长度
        keys: [
          {
            // 搜索title与path
            name: 'name',
            weight: 0.8
          },
          {
            name: 'routes',
            weight: 0.2
          }
        ]
      })
    },
    formatData() {},
    querySearch(query) {
      if (query !== '') {
        this.options = this.fuse.search(query)
      } else {
        this.options = []
      }
    },
    ishttp(url) {
      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
    }
  }
}
</script>

<style lang="less" scoped>
.header-search {
  width: 100%;
  position: relative;
  font-size: 0 !important;
  .search-icon {
    position: absolute;
    left: 10px;
    top: 7px;
    cursor: pointer;
    font-size: 18px;
    color: #e0ecff;
    vertical-align: middle;
  }

  .header-search-select {
    width: 100%;
    font-size: 18px;
    transition: width 0.2s;
    overflow: hidden;
    background: transparent;
    border-radius: 0;
    display: inline-block;
    vertical-align: middle;

    /deep/ .el-input__inner {
      padding-left: 35px;
      padding-right: 0;
      border-radius: 50px;
      box-shadow: none !important;
      vertical-align: middle;
      background-color: transparent;
      border: 1px solid #e0ecff;
      color: #fff;
      &::placeholder {
        color: rgba(143, 175, 213, 0.95);
      }
    }
  }
}
</style>
