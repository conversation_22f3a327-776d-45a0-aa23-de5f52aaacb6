import uuid from 'uuid'

export const state = () => ({
  miniAppList: [], // 桌面已打开的app list
  shortcutList: [], // 快捷方式
  desktopData: {}, // 当前显示菜单数据
  desktopList: [], // 桌面列表
  activeIndex: null, // 激活桌面INDEX
  widgetDragging: false,
  desktopTheme: '深蓝',
  showSetting: false
})
export const mutations = {
  miniAppList(state, value) {
    state.miniAppList = value
  },
  shortcutList(state, value) {
    state.shortcutList = value
  },
  desktopData(state, value) {
    state.desktopData = value
  },
  desktopList(state, value) {
    state.desktopList = value
  },
  activeIndex(state, value) {
    state.activeIndex = value
  },
  widgetDragging(state, value) {
    state.widgetDragging = value
  },
  showSetting(state, value) {
    state.showSetting = value
  },
  desktopTheme(state, value) {
    state.desktopTheme = value
  },
  dragWidget(state, value) {
    state.dragWidget = value
  }
}
export const getters = {}
