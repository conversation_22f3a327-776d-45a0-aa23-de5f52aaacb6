<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title + '信息'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入标题',
              trigger: 'change'
            }
          ]"
          label="标题"
          prop="title"
        >
          <el-input
            v-model="formData.title"
            placeholder="请输入标题" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择时间',
              trigger: 'change'
            }
          ]"
          label="公告时间"
          prop="dateTime"
        >
          <el-date-picker
            v-model="formData.dateTime"
            :type="'datetimerange'"
            :append-to-body="true"
            :clearable="false"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="timeChange "/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请填写切换时间',
              trigger: 'change'
            }
          ]"
          label="切换时间"
          prop="switchTime"
        >
          <el-input 
            v-model="formData.switchTime" 
            placeholder="请输入切换时间"
            style="width: 200px;">
            <template slot="append">秒</template>
          </el-input>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择公告部门',
              trigger: 'change'
            }
          ]"
          label="公告部门"
          prop="noticeDept"
        >
          <template v-for="item in dptList">
            <el-radio
              v-model="formData.noticeDept"
              :key="item.value"
              :label="item.value">{{ item.label }}</el-radio>
          </template>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请公告类型',
              trigger: 'change'
            }
          ]"
          label="公告类型"
          prop="noticeType"
        >
          <template v-for="item in typeList">
            <el-radio
              v-model="formData.noticeType"
              :key="item.value"
              :label="item.value">{{ item.label }}</el-radio>
          </template>
        </el-form-item>
        <el-form-item
          v-if="formData.noticeType == 1"
          :rules="[
            {
              required: true,
              message: '请输入文字内容',
              trigger: 'change'
            }
          ]"
          label="文字内容"
          prop="content"
        >
          <el-input
            v-model="formData.content"
            :rows="6"
            type="textarea"
            placeholder="请输入文字内容" />
        </el-form-item>
        <el-form-item
          v-if="formData.noticeType == 2 || formData.noticeType == 3"
          label="上传文件"
          prop="roleCode"
        >
          <div
            v-if="formData.fileIds && formData.fileIds.length"
            class="video-list">
            <template v-for="item in formData.fileIds">
              <p
                :key="item.id"
                class="video-item">
                <span>
                  <i
                    :title="item.name"
                    :class="formData.noticeType == 2 ? 'el-icon-picture-outline':'el-icon-video-camera'"
                    class="play-icon el-icon-video-camera"/>
                  <em>{{ item.name }}</em>
                </span>
                <i
                  title="删除"
                  class="delete-icon el-icon-delete"
                  @click="deleteFile(item.id)"/>
              </p>
            </template>
          </div>
          <el-upload
            ref="upload"
            :auto-upload="false"
            :http-request="httpRequest"
            :accept="formData.noticeType == 2 ? 'image/*' : 'video/*'"
            :on-change="handleBeforeUpload"
            :multiple="formData.noticeType == 2 ? true : false"
            class="upload-demo"
            action="#">
            <el-button
              slot="trigger"
              size="small"
              type="primary">选取文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  attachFindAllByMultiCondition,
  deleteFileByIds,
  matterSave,
  saveFeedback,
  sysNoticeSave,
  uploadFile,
  weeklySummarySave
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    dptList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: sysNoticeSave,
        add: sysNoticeSave,
        upload: uploadFile
      },
      formData: {
        fileIds: [],
        noticeCategory: 2
      },
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    timeChange() {
      this.$nextTick(() => {
        this.formData.startTime = this.formData.dateTime[0]
        this.formData.endTime = this.formData.dateTime[1]
      })
    },
    httpRequest(params) {},
    submitBefore() {
      // 提交前操作
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handleBeforeUpload(file) {
      if (this.formData.noticeType == 2) {
        const isJPG =
          file.raw.type === 'image/jpeg' || file.raw.type === 'image/png'
        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG、PNG 格式!')
          this.$refs.upload.handleRemove(file)
          return
        }
      }
      if (this.formData.noticeType == 3) {
        const isVideo = file.raw.type === 'video/mp4'
        if (!isVideo) {
          this.$message.error('上传视频只能是 MP4 格式!')
          this.$refs.upload.handleRemove(file)
          return
        }
      }
      const isLt500M = file.raw.size / 1024 / 1024 < 500
      if (!isLt500M) {
        this.$message.error('上传文件大小不能超过 500MB!')
        this.$refs.upload.handleRemove(file)
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {
        dateTime: null,
        noticeType: 2,
        fileIds: [],
        noticeCategory: 1
      }
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    /**
     * 开启编辑
     * @param data 编辑元数据
     */
    edit(data) {
      this.title = '编辑'
      this.editType = 'edit'
      this.formData = Object.assign({}, this.formData, data, {
        dateTime: [data.startTime, data.endTime]
      })
      // 获取附件
      post(attachFindAllByMultiCondition, {
        relatedId: this.formData.id
      }).then(res => {
        Object.assign(this.formData, {
          fileIds: res.data
        })
      })
    },
    deleteFile(id) {
      post(deleteFileByIds, { ids: [id] }).then(res => {
        this.formData.fileIds = this.formData.fileIds.filter(
          item => item.id !== id
        )
        this.$message.success('删除成功')
      })
    },

    handelConfirm() {
      if (
        this.$refs['form'] &&
        typeof this.$refs['form'].validate == 'function'
      ) {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          switch (this.editType) {
          }
          this.loading = true
          if (this.editType === 'edit') {
            if (!this.url || !this.url.edit) {
              this.$message.warning('请设置url.edit属性!')
              return
            }
            this.submitBefore()
            post(this.url.edit, this.formData).then(res => {
              this.loading = false
              if (res.success) {
                this.formData.id = res.data.id
                this.handleUpload()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          } else if (this.editType === 'add') {
            if (!this.url || !this.url.add) {
              this.$message.warning('请设置url.add属性!')
              return
            }
            this.submitBefore()
            post(this.url.add, this.formData).then(res => {
              this.loading = false
              if (res.success) {
                this.formData.id = res.data.id
                this.handleUpload()
              } else {
                this.$message.warning('保存失败！')
              }
            })
          }
        })
      }
    },

    handleUpload() {
      if (this.formData.noticeType == 1) {
        return this.close()
      }
      const files = this.$refs.upload.uploadFiles
      if (
        this.formData.noticeType !== 1 &&
        this.formData.fileIds.length &&
        !files.length
      ) {
        return this.close()
      }
      if (!files.length) {
        return this.$message.warning('请选择上传文件')
      }
      this.loading = true
      const formData = new FormData()
      files.forEach(item => {
        formData.append('files', item.raw)
      })
      formData.append('relatedId', this.formData.id)
      formData.append('serviceNo', 'res')
      post(this.url.upload, formData, false, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        console.log(res)
        this.loading = false
        if (res.success) {
          this.$refs.upload.clearFiles()
          this.close()
        }
      })
    }
  }
}
</script>
<style scoped lang="less">
.video-list {
  border: 1px solid #eee;
  margin-bottom: 5px;
}
.video-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #eee;
  &:last-child {
    border: none;
  }
  .play-icon {
    font-size: 26px;
    cursor: pointer;
    vertical-align: middle;
  }
  .delete-icon {
    font-size: 18px;
    cursor: pointer;
  }
  em {
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
