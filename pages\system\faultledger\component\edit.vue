<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'编辑'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择模块',
              trigger: 'change'
            }
          ]"
          label="模块名称"
          prop="serviceName"
        >
          <el-select
            v-model="formData.serviceName"
            size="small"
            clearable
            placeholder="选择模块"
          >
            <el-option
              v-for="(item, index) in serviceList"
              :key="index"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入功能名称',
              trigger: 'change'
            }
          ]"
          label="功能名称"
          prop="functionName"
        >
          <el-input
            v-model="formData.functionName"
            placeholder="请输入功能名称" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入问题描述',
              trigger: 'change'
            }
          ]"
          label="问题描述"
          prop="errorInfo"
        >
          <el-input
            v-model="formData.errorInfo"
            type="textarea"
            placeholder="请输入问题描述" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入责任部门',
              trigger: 'change'
            }
          ]"
          label="责任部门"
          prop="departmentName"
        >
          <select-org
            v-model="formData.departmentName"
            :multiple="false"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入责任人',
              trigger: 'change'
            }
          ]"
          label="责任人"
          prop="dutyPerson"
        >
          <user-select v-model="formData.dutyPerson"/>
        </el-form-item>
        <el-form-item
          label="原因分析"
          prop="causeAnalysis"
        >
          <el-input
            v-model="formData.causeAnalysis"
            type="textarea"
            placeholder="请输入原因分析"/>
        </el-form-item>
        <el-form-item
          label="处置优化措施"
          prop="handle"
        >
          <el-input
            v-model="formData.handle"
            type="textarea"
            placeholder="请输入处置优化措施"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择计划完成时间',
              trigger: 'change'
            }
          ]"
          label="计划完成时间"
          prop="planFinishTime"
        >
          <el-date-picker
            v-model="formData.planFinishTime"
            placeholder="请选择时间"/>
        </el-form-item>
        <el-form-item
          label="实际完成时间"
          prop="actFinishTime"
        >
          <el-date-picker
            v-model="formData.actFinishTime"
            placeholder="请选择时间"/>
        </el-form-item>
        <el-form-item
          label="状态"
          prop="status"
        >
          <el-select
            v-model="formData.status"
            size="small"
            clearable
            placeholder="选择状态"
          >
            <el-option
              v-for="(item, index) in statusList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelSubmit"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import { errorRecordSave, uploadFile } from '@/api/system'
import ImgView from '@/components/ImgView'
import UserSelect from '@/pages/system/faultledger/component/userSelect'
import SelectOrg from '@/pages/system/faultledger/component/SelectOrg'

export default {
  components: { ImgView, UserSelect, SelectOrg },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    serviceList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: errorRecordSave,
        add: errorRecordSave,
        file: uploadFile
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      statusList: [
        { label: '完成', value: 1, type: 'success' },
        { label: '未完成', value: 0, type: 'danger' }
      ],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {},
  watch: {},
  created() {
    // console.log('')
  },
  methods: {
    httpRequest(params) {},
    async handelSubmit() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.loading = true
        this.handelConfirm()
      })
    },

    submitBefore() {
      if (!this.formData.attachList) this.formData.attachList = []
      this.formData.attachList.push(...this.uploadFiles, ...this.attachList)
      this.formData.anonymous = !!this.formData.anonymous
      this.formData.quesType = this.quesType
      this.formData.quesUserNo = localStorage.getItem('userId')
      this.formData.planFinishTime = this.$moment(
        this.formData.planFinishTime
      ).format('yyyy-MM-DD')
      this.formData.actFinishTime = this.$moment(
        this.formData.actFinishTime
      ).format('yyyy-MM-DD')
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    }
  }
}
</script>
<style scoped>
</style>
