<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'月总结详情'"
      :visible.sync="visible"
      v-bind="$attrs"
      width="1200px"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-row
        :gutter="2"
        type="flex"
        align="middle">
        <el-col :span="2">
          <div
            class="btn-arrow"
            title="上一条"
            @click="prev">
            <i class="el-icon-caret-left" />
          </div>
        </el-col>
        <el-col :span="20">
          <el-form
            v-if="visible"
            ref="form"
            :model="formData"
            label-width="140px"
            size="medium"
          >
            <el-form-item
              label="模块:"
              prop="module"
            >
              <el-tag
                disable-transitions
              >{{ getName('moduleList', formData.module).label }}
              </el-tag>
            </el-form-item>
            <el-form-item
              label="时间:"
              prop="module"
            >
              {{ formData.weekDate }}
            </el-form-item>
            <el-form-item
              label="本月总结:"
              prop="currWeekSummary"
            >
              <span
                style="font-size: 22px"
                v-html="formatText(formData.currWeekSummary)"/>
            </el-form-item>
            <el-form-item
              label="下月计划:"
              prop="nextWeekPlan"
            >
              <span
                style="font-size: 22px"
                v-html="formatText(formData.nextWeekPlan)"/>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="2">
          <div
            class="btn-arrow"
            title="下一条"
            @click="next">
            <i class="el-icon-caret-right" />
          </div>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  deleteFileByIds,
  dictionaryDtlFindByDictCode,
  saveFeedback,
  uploadFile
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {
    typeList: {
      type: Array,
      default: function() {
        return []
      }
    },
    statusList: {
      type: Array,
      default: function() {
        return []
      }
    },
    moduleList: {
      type: Array,
      default: function() {
        return []
      }
    },
    quesType: {
      type: Number,
      default: 1
    },
    detail: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: saveFeedback,
        add: saveFeedback,
        file: uploadFile,
        getDict: dictionaryDtlFindByDictCode
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: []
    }
  },
  computed: {
    week: function() {
      return this.formData.weekDate
        ? `第${this.$moment(this.formData.weekDate).week() +
            1}周（${this.$moment(this.formData.weekDate)
            .startOf('isoWeek')
            .format('yyyy-MM-DD')} - ${this.$moment(this.formData.weekDate)
            .endOf('isoWeek')
            .format('yyyy-MM-DD')}）`
        : ''
    }
  },
  watch: {
    detail: {
      deep: true,
      handler: function() {
        console.log(this.detail)
        this.formData = this.detail
      }
    }
  },
  async created() {
    // console.log('')
  },
  methods: {
    onOpen() {
      this.formData = this.detail
    },
    handleChange(file, fileList) {
      // console.log(file, fileList)
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getName: function(list, status) {
      return this[list].find(item => item.value == status) || {}
    },
    clearForm() {
      this.formData = {}
      this.uploadFiles = []
      this.attachList = []
      this.deleteIds = []
    },
    handleImgPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleImgDelete(file) {
      this.attachList = this.attachList.filter(res => res.id !== file.id)
      this.deleteIds.push(file.id)
    },
    next() {
      this.$emit('next')
    },
    prev() {
      this.$emit('prev')
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    }
  }
}
</script>
<style scoped lang="less">
.btn-arrow {
  margin: auto;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  background: #fff;
  color: #e1e0e0;
  border-radius: 50%;
  font-size: 22px;
  box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
  cursor: pointer;
  &:hover {
    background: #eee;
    color: #fff;
  }
}
</style>
