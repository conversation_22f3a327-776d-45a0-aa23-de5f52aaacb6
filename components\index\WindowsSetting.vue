<template>
  <el-drawer
    :direction="direction"
    :visible.sync="drawer"
    :size="'500px'"
    title="桌面设置">
    <div class="drawer-wrapper">
      <div class="setting-tit">桌面壁纸</div>
      <div class="wallpaper">
        <div
          v-for="(item, index) in Wallpapers"
          :key="index"
          :title="item.name"
          class="wallpaper-item"
          @click="desktopTheme=item.name">
          <img
            :src="item.img"
            :alt="item.name"
            class="img"
          >
          <div>
            <el-radio
              v-model="desktopTheme"
              :label="item.name">{{ item.name }}
            </el-radio>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { DesktopConfig } from '@/lib/Constant'

export default {
  name: 'WindowsSetting',
  data() {
    return {
      direction: 'rtl',
      Wallpapers: DesktopConfig.desktopWallpaper
    }
  },
  computed: {
    drawer: {
      get() {
        return !!this.$store.state.desktop.showSetting
      },
      set(value) {
        this.$store.commit('desktop/showSetting', value)
      }
    },
    desktopTheme: {
      get() {
        return this.$store.state.desktop.desktopTheme
      },
      set(value) {
        this.$store.commit('desktop/desktopTheme', value)
      }
    }
  }
}
</script>

<style scoped lang="less">
.drawer-wrapper {
  margin: 0 20px 20px;
  .setting-tit {
    margin-bottom: 10px;
    font-size: 16px;
    color: #999;
  }
}
.wallpaper {
  margin-left: -10px;
  margin-right: -10px;
  display: flex;
  flex-flow: wrap;
  .wallpaper-item {
    width: 33.333%;
    //margin: 10px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
  }
  .img {
    width: 100%;
  }
}
</style>
