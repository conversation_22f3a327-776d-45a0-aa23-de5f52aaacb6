<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="'分配用户'"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      width="1220px"
      v-on="$listeners"
    >
      <div class="page-content">
        <el-row :gutter="20">
          <el-col :span="12">

            <div class="search-wrapper">
              <el-form
                ref="searchForm"
                :label-width="'80px'"
                :model="searchForm"
                inline
                @keyup.enter.native="handleSearch"
              >
                <el-form-item
                  label="用户编号"
                  prop="userNo"
                >
                  <el-input
                    v-model="searchForm.userNo"
                    clearable
                    placeholder="请输入用户编号"
                    style="width: 180px"
                    type="text"
                  />
                </el-form-item>
                <el-form-item
                  label="用户姓名"
                  prop="userNo"
                >
                  <el-input
                    v-model="searchForm.userName"
                    clearable
                    placeholder="请输入用户姓名"
                    style="width: 180px"
                    type="text"
                  />
                </el-form-item>
                <el-form-item
                  label="机构"
                  prop="orgCode"
                >
                  <select-org @on-change="setOrg" />
                </el-form-item>
                <el-form-item
                  class="br"
                >
                  <el-button
                    icon="ios-search"
                    type="primary"
                    @click="handleSearch"
                  >搜索
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <el-table
              v-loading="loading"
              ref="table"
              :data="tableData"
              :size="size"
              border
              style="width: 100%"
              @select="onSelectChange"
              @select-all="onSelectChange"
            >
              <el-table-column
                type="selection"
                width="50"
              />
              <el-table-column
                label="序号"
                type="index"
                width="80"
              />
              <el-table-column
                label="员工编号"
                min-width="100"
                prop="userNo"
              />
              <el-table-column
                label="员工姓名"
                min-width="100"
                prop="userName"
              />
            </el-table>

            <el-row
              align="middle"
              class="table-pagination"
              justify="end"
              type="flex"
            >
              <el-pagination
                :current-page="page.pageIndex"
                :page-size="page.pageSize"
                :page-sizes="[10, 20, 30, 40, 100, 300, 500,1000, 5000]"
                :total="page.total"
                layout="sizes, total, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-row>
          </el-col>
          <el-col :span="12">
            <user-list
              :loading="loading"
              :size="size"
              :users="selectedUsers"
              :page-size="page.pageSize"
              @select="cancelSelect"
              @unSelectAll="handleUnselectAll" />
          </el-col>
        </el-row>
      </div>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import listMixins from '@/mixins/ListMixins'
import { relateUser, roleById, userDelete, userList } from '@/api/system'
import { post } from '@/lib/Util'
import UserList from '@/pages/system/role/component/UserList'
import SelectOrg from '@/components/SelectOrg'

export default {
  components: { SelectOrg, UserList },
  mixins: [listMixins],
  // eslint-disable-next-line vue/require-prop-types
  props: ['visible', 'role'],
  data: () => {
    return {
      selectedUsers: [],
      searchName: null,
      url: {
        list: userList, //分页接口地址
        delete: userDelete //删除接口地址
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set() {}
    }
  },
  methods: {
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      if (this.searchForm.userNo && this.searchForm.userNo.indexOf(',') != -1) {
        // 多个账号
        this.tableData = []
        this.page.pageIndex = 1
        this.page.totalPages = 0
        this.page.total = 0
        this.searchForm.userNo.split(',').forEach(item => {
          post(
            this.url.list,
            Object.assign(
              {},
              {
                userNo: item.trim()
              },
              {
                pageIndex: this.page.pageIndex,
                pageSize: this.page.pageSize
              }
            )
          ).then(res => {
            this.tableData.push(...res.data.content)
            this.afterHandleSearch(this.tableData)
          })
        })
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data.content : []
      this.page.pageSize = data.pageable.pageSize
      this.page.totalPages = data.totalPages
      this.page.total = data.totalElements
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    afterHandleSearch() {
      // 设置已选择用户
      this.tableData.forEach((data, index) => {
        const match = this.selectedUsers.find(
          item => item.userNo === data.userNo
        )
        if (match) {
          this.$nextTick(() =>
            this.$refs.table.toggleRowSelection(this.tableData[index])
          )
        }
      })
    },
    onSelectChange(selectedRowKeys) {
      // 根据选择结果更新全量选择列表
      // 添加选中用户
      selectedRowKeys.forEach(select => {
        !this.selectedUsers.find(item => item.userNo === select.userNo) &&
          this.selectedUsers.push(select)
      })
      // 去除未选用户
      const notSelectedList = this.tableData.filter(
        item =>
          selectedRowKeys.findIndex(
            selected => selected.userNo === item.userNo
          ) === -1
      )
      notSelectedList.forEach(noSelect => {
        const matchIndex = this.selectedUsers.findIndex(
          item => item.userNo === noSelect.userNo
        )
        matchIndex !== -1 && this.selectedUsers.splice(matchIndex, 1)
      })
      console.log('修改了', this.selectedUsers)
    },
    cancelSelect(data) {
      console.log(data)
      this.selectedUsers = this.selectedUsers.filter(
        item => item.userNo != data[0].userNo
      )
      const matchIndex = this.tableData.findIndex(
        item => item.userNo === data[0].userNo
      )
      if (matchIndex !== -1) {
        this.$nextTick(() => {
          this.$refs.table.toggleRowSelection(this.tableData[matchIndex], false)
        })
      }
    },
    handleUnselectAll() {
      this.selectedUsers = []
      this.tableData.forEach((item, index) =>
        this.$refs.table.toggleRowSelection(this.tableData[index], false)
      )
    },
    /*handelConfirm() {
      this.loading = true
      post(relateUser, {
        userIDs: this.selectedUsers.map(item => item.id)
      }).then(res => {
        this.loading = false
        if (res.success) {
          console.log('111', res)
          // this.formData.value = res.
          this.$message.success('分配成功')
          this.close()
        } else {
          this.$message.warning('分配失败')
        }
      })
    },*/
    handelConfirm() {
      console.log('选中的用户', this.selectedUsers)
      let inputStr = ''
      for (let i = 0; i < this.selectedUsers.length; i++) {
        inputStr +=
          this.selectedUsers[i].userName + '-' + this.selectedUsers[i].idNum
        if (i < this.selectedUsers.length - 1) {
          inputStr = inputStr + ','
        }
      }
      console.log(inputStr)
      this.$emit('getInputStr', inputStr)
      this.close()
    },
    close() {
      this.$emit('update:visible', false)
    },

    // 修改searName, 触发模拟搜索功能
    setSearchName() {
      this.searchName = this.searchForm.searchName || null
    },
    setOrg(value) {
      this.searchForm.orgCode = typeof value === 'string' ? [value] : value
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  box-shadow: 0 0 10px rgba(117, 116, 116, 0.1);
}

.search-wrapper {
  margin-bottom: 10px;
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}
</style>
