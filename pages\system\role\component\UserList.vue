<template>
  <div>

    <div class="search-wrapper">
      <el-form
        ref="searchForm"
        :label-width="'0px'"
      >
        <el-form-item
          prop="userNo"
        >
          <el-input
            v-model="searchName"
            clearable
            placeholder="请输入用户编号或姓名筛选"
            style="width: 200px"
            type="text"
          />
        </el-form-item>
        <el-form-item
          class="br"
        >
          <el-button
            icon="ios-search"
            @click="unselectAll"
          >取消所有
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      ref="tableSelected"
      :data="tableData"
      :size="size"
      border
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        type="index"
        width="80"
      />
      <el-table-column
        label="员工编号"
        min-width="100"
        prop="userNo"
      />
      <el-table-column
        label="员工姓名"
        min-width="100"
        prop="userName"
      />
      <el-table-column
        fixed="right"
        label="操作"
        width="80"
      >
        <template
          v-slot="{row, index}"
        >
          <el-button
            slot="reference"
            :style="{padding: 0}"
            type="text"
            @click="cancelSelect(row, index)"
          >取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row
      align="middle"
      class="table-pagination"
      justify="end"
      type="flex"
    >
      <el-pagination 
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalCount"
        layout="sizes, total, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"/>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'UserList',
  // eslint-disable-next-line vue/require-prop-types
  props: {
    users: {
      type: Array,
      default: function() {
        return []
      }
    },
    size: {
      type: String,
      default: 'medium'
    },
    loading: {
      type: Boolean,
      default: false
    },
    pageSize: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      // 默认显示第几页
      currentPage: 1,
      searchName: null
    }
  },
  computed: {
    // 总条数，根据接口获取数据长度(注意：这里不能为空)
    totalCount: function() {
      return this.users.length || 0
    },
    tableData: function() {
      return this.users
        .slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        )
        .filter(item => {
          return (
            !this.searchName ||
            item.userName.indexOf(this.searchName) !== -1 ||
            item.userNo.indexOf(this.searchName) !== -1
          )
        })
    }
  },
  methods: {
    cancelSelect(row, index) {
      this.$emit('select', [row, index])
    },
    unselectAll() {
      this.$emit('unSelectAll')
    },
    // 分页
    // 每页显示的条数
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    // 显示第几页
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped lang="less">
.page-title {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  margin-bottom: 15px;
}

.page-content {
  font-size: 18px;
  padding: 20px;
  background: #fff;
  box-shadow: 0 0 10px rgba(117, 116, 116, 0.1);
}

.search-wrapper {
  margin-bottom: 10px;
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .operate-icon {
    margin-left: 8px;
  }
}

.table-pagination {
  margin-top: 20px;
}
</style>
