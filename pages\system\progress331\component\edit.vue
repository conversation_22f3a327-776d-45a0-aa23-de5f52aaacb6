<template>
  <div v-if="visible">
    <el-dialog
      v-el-drag-dialog
      :title="title + '项目节点'"
      :visible.sync="visible"
      v-bind="$attrs"
      @close="clearForm"
      @open="onOpen"
      v-on="$listeners"
    >
      <el-form
        v-if="visible"
        ref="form"
        :model="formData"
        label-width="140px"
        size="medium"
        class="search-wrapper"
      >
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入节点名称',
              trigger: 'change'
            }
          ]"
          label="菜单名称："
          prop="name"
        >
          <el-input
            v-model="formData.name"
            placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请选择模块',
              trigger: 'change'
            }
          ]"
          label="模块："
          prop="module"
        >
          <el-select
            v-model="formData.module"
            size="small"
            placeholder="选择模块"
            @change="formData.parentId = null;getMenuList()"
          >
            <el-option
              v-for="(item, index) in moduleList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="上级菜单："
          prop="parentId"
        >
          <el-select
            v-model="formData.parentId"
            size="small"
            clearable
            filterable
            placeholder="选择上级菜单"
            @change="$forceUpdate()"
          >
            <el-option
              v-for="(item, index) in menuList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入功能开发',
              trigger: 'change'
            }
          ]"
          label="功能开发："
          prop="functionDev"
        >
          <el-input-number
            v-model="formData.functionDev"
            :min="0"
            :max="1000"
            style="width: 120px"/>
          <el-date-picker
            v-model="formData['1']"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @input="dateChange"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入内部测试',
              trigger: 'change'
            }
          ]"
          label="内部测试："
          prop="internalTest"
        >
          <el-input-number
            v-model="formData.internalTest"
            :min="0"
            :max="1000"
            style="width: 120px"/>
          <el-date-picker
            v-model="formData['2']"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @input="dateChange"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入外部试用',
              trigger: 'change'
            }
          ]"
          label="外部试用："
          prop="externalTrial"
        >
          <el-input-number
            v-model="formData.externalTrial"
            :min="0"
            :max="1000"
            style="width: 120px"/>
          <el-date-picker
            v-model="formData['3']"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @input="dateChange"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入功能培训',
              trigger: 'change'
            }
          ]"
          label="功能培训："
          prop="functionTrain"
        >
          <el-input-number
            v-model="formData.functionTrain"
            :min="0"
            :max="1000"
            style="width: 120px"/>
          <el-date-picker
            v-model="formData['4']"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @input="dateChange"/>
        </el-form-item>
        <el-form-item
          :rules="[
            {
              required: true,
              message: '请输入功能上线',
              trigger: 'change'
            }
          ]"
          label="功能上线："
          prop="functionOnline"
        >
          <el-input-number
            v-model="formData.functionOnline"
            :min="0"
            :max="1000"
            style="width: 120px"/>
          <el-date-picker
            v-model="formData['5']"
            type="daterange"
            value-format="yyyy-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @input="dateChange"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="handelConfirm"
        >确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import EditMixins from '@/mixins/EditMixins'
import {
  dictionaryDtlFindByDictCode,
  onlineFind,
  onlineSave,
  progressFind,
  progressSave,
  uploadFile
} from '@/api/system'
import { post } from '@/lib/Util'
import ImgView from '@/components/ImgView'
import { findOneUserByUserNo } from '@/api/desktop'
import { ENUM } from '@/lib/Constant'

export default {
  components: { ImgView },
  mixins: [EditMixins],
  inheritAttrs: false,
  props: {},
  data() {
    return {
      loading: false,
      visible: false,
      url: {
        edit: onlineSave,
        add: onlineSave,
        file: uploadFile,
        list: onlineFind,
        getDict: dictionaryDtlFindByDictCode
      },
      formData: {},
      dialogImageUrl: null,
      dialogVisible: false,
      moduleList: [],
      menuList: [],
      attachList: [],
      uploadFiles: [], // 选择的文件
      deleteIds: [],
      nodes: ['1', '2', '3', '4', '5']
    }
  },
  computed: {},
  watch: {},
  async created() {
    // console.log('')
    const { data: module } = await post(this.url.getDict, {
      dictCode: 'module'
    })
    this.moduleList = module.map(item => {
      return {
        value: item.code,
        label: item.value
      }
    })
  },
  methods: {
    dateChange() {
      this.$forceUpdate()
    },
    onOpen() {
      console.log(this.formData.ganttData)
      const nodes = ['1', '2', '3', '4', '5']
      nodes.forEach(item => {
        this.formData[item] =
          this.formData.ganttData[item] &&
          this.formData.ganttData[item].startDate
            ? [
                this.formData.ganttData[item].startDate,
                this.formData.ganttData[item].endDate
              ]
            : null
      })
    },
    submitBefore() {
      if (!this.formData.parentId) this.formData.parentId = '0'
      this.nodes.forEach(item => {
        this.formData.ganttData[item] = this.formData[item]
          ? {
              startDate: this.formData[item][0],
              endDate: this.formData[item][1]
            }
          : {}
      })
    },
    getMenuList() {
      post(
        this.url.list,
        {
          module: this.formData.module,
          parentId: '0',
          pageIndex: 1,
          pageSize: 2000
        },
        {}
      ).then(res => {
        this.menuList = [{ id: '0', name: '无上级' }].concat(res.data.content)
      })
    },
    clearForm() {
      this.formData = {}
    }
  }
}
</script>
<style scoped>
</style>
