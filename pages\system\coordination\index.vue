<template>
  <div>
    <div class="page-content">
      <div
        class="page-operate"
        style="align-items: flex-start">
        <div class="search-wrapper">
          <el-form
            ref="form"
            :model="searchForm"
            inline
            @keyup.enter.native="handleSearch(true)"
          >
            <el-form-item
              label=""
              prop="title"
            >
              <el-input
                v-model="searchForm.matter"
                clearable
                size="small"
                placeholder="请输入协调事项"
                suffix-icon="el-icon-search"
                style="width: 150px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label=""
              prop="quesUserNo"
            >
              <el-input
                v-model="searchForm.presenter"
                clearable
                size="small"
                placeholder="请输入提出人"
                suffix-icon="el-icon-search"
                style="width: 150px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label=""
              prop="quesUserNo"
            >
              <el-input
                v-model="searchForm.handleUnit"
                clearable
                size="small"
                placeholder="请输入责任单位"
                suffix-icon="el-icon-search"
                style="width: 150px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label=""
              prop="quesUserNo"
            >
              <el-input
                v-model="searchForm.handlePerson"
                clearable
                size="small"
                placeholder="请输入责任人"
                suffix-icon="el-icon-search"
                style="width: 150px"
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="事项类型"
              prop="matterType"
            >
              <el-select
                v-model="searchForm.matterType"
                :style="{width: '120px'}"
                size="small"
                clearable
                placeholder="事项类型"
              >
                <el-option
                  v-for="(item, index) in typeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="handleStatus"
            >
              <el-select
                v-model="searchForm.handleStatus"
                :style="{width: '100px'}"
                size="small"
                clearable
                placeholder="选择状态"
              >
                <el-option
                  v-for="(item, index) in statusList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="'查询模式'"
            >
              <el-select
                v-model="searchForm.mode"
                :style="{width: '100px'}"
                :clearable="false"
                size="small"
                placeholder="选择模式"
              >
                <el-option
                  :label="'按日期'"
                  :value="'daterange'"
                />
                <el-option
                  :label="'按周'"
                  :value="'week'"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="任务日期"
              prop="quesType"
            >
              <template>
                <el-date-picker
                  v-show="searchForm.mode === 'week'"
                  v-model="searchForm.week"
                  :type="'week'"
                  :placeholder="'选择周'"
                  :append-to-body="true"
                  :format="'yyyy-MM-dd'"
                  style="width: 130px"/>
                <span 
                  style="top: 2px; bottom: 2px; left: 20px;">{{ week }}</span>
              </template>
              <el-date-picker
                v-show="searchForm.mode !== 'week'"
                v-model="searchForm.date"
                :type="'daterange'"
                :append-to-body="true"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"/>
            </el-form-item>
          </el-form>
        </div>
        <div
          class="text-right"
          style="white-space: nowrap">
          <el-button
            icon="ios-search"
            type="primary"
            @click="handleSearch"
          >搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="success"
            @click="handleAdd"
          >新增
          </el-button>
          <div
            v-if="description"
            style="margin-bottom: 16px; text-align: right">{{ description }}</div>
        </div>
      </div>
      <div class="page-card shadow-light">
        <el-table
          v-loading="loading"
          :data="tableData"
          :size="size"
          :row-class-name="tableRowClassName"
          border
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
          />
          <el-table-column
            label="协调及要求事项"
            prop="matter"
            min-width="150"
          >
            <template v-slot="{ row }">
              <el-tooltip 
                class="item" 
                effect="dark"
                placement="top">
                <div class="one-line">
                  <span v-if="row.warning">
                    <el-tag
                      :type="'warning'"
                      disable-transitions
                    >
                      <i class="el-icon-warning-outline" />
                    </el-tag>
                  </span>
                  {{ row.priority ? '【置顶】' : '' }}{{ row.matter }}
                </div>
                <div
                  slot="content"
                  v-html="formatText(row.matter)"/>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="事项类型"
            prop="serviceNo"
            width="100"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="getName('typeList', row.matterType).type"
                disable-transitions
              >{{ getName('typeList', row.matterType).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="提出单位"
            prop="presenterUnit"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="提出人"
            prop="presenter"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="责任单位"
            prop="handleUnit"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="责任人"
            prop="handlePerson"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="任务日期"
            prop="taskDate"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="处理结果"
            prop="handleResults"
          >
            <template
              v-slot="{row}"
            >
              <el-tooltip
                class="item"
                effect="dark"
                placement="top">
                <div class="one-line">
                  {{ row.handleResults }}
                </div>

              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="计划完成时间"
            prop="planCompleteDate"
            width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="是否已处理"
            prop="handlerStatus"
          >
            <template
              v-slot="{row}"
            >
              <el-tag
                :type="getName('statusList', row.handleStatus).type"
                disable-transitions
              >{{ getName('statusList', row.handleStatus).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            min-width="200px"
            style="white-space: nowrap"
          >
            <template
              slot-scope="{row,$index}"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetail($index)"
                >详情
                </el-button>
              </span>
              <template>
                <span>
                  <el-divider direction="vertical" />
                  <el-button
                    size="small"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                </span>
                <span v-command="'/system/coordination/top'">
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleTop(row)"
                  >{{ !row.priority ? '置顶' : '取消置顶' }}
                  </el-button>
                </span>
                <span v-command="'/system/coordination/delete'">
                  <el-divider direction="vertical" />
                  <el-button
                    slot="reference"
                    type="text"
                    @click="handleDelete(row)"
                  >{{ '删除' }}
                  </el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-row
          align="middle"
          class="table-pagination"
          justify="end"
          type="flex"
        >
          <el-pagination
            :current-page="page.pageIndex"
            :page-size="page.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-row>
      </div>
    </div>
    <Detail
      ref="modalDetailForm"
      :status-list="statusList"
      :type-list="typeList"
      :ques-type="searchForm.quesType"
      :detail="showDetail"
      @success="handleSearch"
      @next="next"
      @prev="prev"
    />
    <Edit
      ref="modalForm"
      :status-list="statusList"
      :type-list="typeList"
      @success="handleSearch"
    />
    <Handle
      ref="modalHandleForm"
      :status-list="statusList"
      :type-list="typeList"
      @success="handleSearch"
    />
  </div>
</template>

<script>
import Edit from './component/edit'
import listMixins from '@/mixins/ListMixins'
import {
  delFeedback,
  findBasicDataConfigByType,
  matterDeleteByIds,
  matterFindFeedback,
  matterSave,
  matterTop
} from '@/api/system'
import { ENUM } from '@/lib/Constant'
import { post } from '@/lib/Util'
import Handle from './component/handle'
import Detail from './component/detail'
import Timer from './component/timer'

export default {
  layout: 'menuLayout',
  name: 'system-role',
  components: {
    Timer,
    Detail,
    Handle,
    Edit
  },
  mixins: [listMixins],
  data: () => {
    return {
      searchForm: {
        mode: 'daterange'
      },
      description: '',
      visibleDistribute: false,
      visibleResource: false,
      url: {
        list: matterFindFeedback, //分页接口地址
        delete: matterDeleteByIds, //删除接口地址
        save: matterSave
      },
      userId: null,
      roleStatusList: ENUM.roleStatus,
      editRole: null,
      serviceList: [],
      typeList: [
        { label: '协调事项', value: 1, type: 'primary' },
        { label: '会议要求', value: 2, type: 'info' },
        { label: '遗留事宜', value: 3, type: 'warning' },
        { label: '风险事项', value: 4, type: 'danger' }
      ], // 1-协调事项 2-会议要求 3-遗留事项
      statusList: [
        { label: '是', value: 1, type: 'success' },
        { label: '否', value: 2, type: 'warning' },
        { label: '进行中', value: 3, type: 'primary' }
      ],
      timer: null,
      activeIndex: 0,
      mode: 'daterange'
    }
  },
  computed: {
    showDetail: function() {
      return this.tableData.length && this.tableData[this.activeIndex]
        ? this.tableData[this.activeIndex]
        : {}
    },
    week: function() {
      return this.searchForm.week
        ? '第' + this.$moment(this.searchForm.week).week() + '周'
        : ''
    }
  },
  created() {
    this.handleSearch(true)
    this.userId = localStorage.getItem('userId')
  },
  destroyed() {},
  methods: {
    async handleSearch(reset = false) {
      if (!this.url || !this.url.list) {
        console.log(this.url)
        this.$message.warning('请设置url.list属性!')
        return
      }
      let valid = await this.beforeHandleSearch()
      if (!valid) {
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (reset) {
        this.page.pageIndex = 1
      }
      // 搜索
      this.loading = true
      const { data } = await post(
        this.url.list,
        Object.assign({}, this.searchForm, {
          pageIndex: this.page.pageIndex,
          pageSize: this.page.pageSize
        })
      )
      // console.log(data)
      this.tableData = data ? data.list.content : []
      this.page.pageSize = data.list.pageable.pageSize
      this.page.totalPages = data.list.totalPages
      this.page.total = data.list.totalElements
      this.description = data.statistics
      this.afterHandleSearch(this.tableData)
      this.loading = false
    },
    beforeHandleSearch() {
      if (
        this.searchForm.mode === 'daterange' &&
        this.searchForm.date &&
        this.searchForm.date.length
      ) {
        this.searchForm.startCreateDate = this.$moment(
          this.searchForm.date[0]
        ).format('yyyy-MM-DD')
        this.searchForm.endCreateDate = this.$moment(
          this.searchForm.date[1]
        ).format('yyyy-MM-DD')
      } else if (this.searchForm.mode === 'week' && this.searchForm.week) {
        this.searchForm.startCreateDate = this.$moment(this.searchForm.week)
          .add(0, 'days')
          .format('yyyy-MM-DD')
        this.searchForm.endCreateDate = this.$moment(this.searchForm.week)
          .add(6, 'days')
          .format('yyyy-MM-DD')
      } else {
        this.searchForm.startCreateDate = ''
        this.searchForm.endCreateDate = ''
      }
    },

    afterHandleSearch() {
      //
      this.tableData.forEach(item => {
        if (
          item.handleStatus !== 1 &&
          this.$moment().format('yyyy-MM-DD') > item.planCompleteDate
        ) {
          item.warning = true
        }
      })
    },

    getName: function(list, status) {
      return this[list].find(item => item.value === status) || {}
    },
    getServiceName: function(name) {
      return this.typeList.find(item => item.value === name) || {}
    },

    // 编辑
    handleEdit: function(row) {
      this.$refs.modalForm.edit(row)
      this.$refs.modalForm.visible = true
    },
    // 详情
    handleDetail: function(index) {
      this.activeIndex = index
      this.$refs.modalDetailForm.visible = true
    },

    handleTop: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm(
        '是否确认' + data.priority === null ? '置顶' : '取消置顶' + '此数据项?',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 删除操作
        post(matterTop, { id: data.id, flag: !data.priority }).then(res => {
          this.handleSearch()
        })
      })
    },

    handleDelete: function(data) {
      if (!this.url.delete) {
        this.$message('请设置url.delete属性!')
        return
      }
      this.$confirm('是否确认删除此数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除操作
        post(this.url.delete, { ids: [data.id] }).then(res => {
          this.handleSearch()
        })
      })
    },
    next() {
      if (this.activeIndex < this.tableData.length - 1) {
        this.activeIndex++
      } else if (this.page.pageIndex < this.page.totalPages) {
        this.page.pageIndex++
        this.handleSearch()
        this.activeIndex = 0
      } else {
        this.$message.info('已经是最后一条了')
      }
    },
    prev() {
      if (this.activeIndex > 0) {
        this.activeIndex--
      } else if (this.page.pageIndex > 1) {
        this.page.pageIndex--
        this.handleSearch()
        this.activeIndex = this.page.pageSize - 1
      } else {
        this.$message.warning('已经是第一条了')
      }
    },
    tableRowClassName({ row }) {
      if (row.matterType === 4) {
        return 'warning-row'
      }
      return ''
    },
    formatText(text) {
      if (!text) {
        return ''
      }
      return text
        .replace(' ', '')
        .replace('\r', '<br/>')
        .replace(/\n/g, '<br>')
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}
.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
