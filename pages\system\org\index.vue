<template>
  <div class="page-content">
    <el-row
      :gutter="20"
      class="row-bg full-height"
      justify="start"
      type="flex"
    >
      <el-col
        :span="7"
        class="full-height">
        <div class="tree-left shadow-light full-height">
          <div class="tree-box">
            <el-tree
              :data="data"
              :load="loadNode"
              :props="defaultProps"
              highlight-current
              lazy
              node-key="id"
              @node-click="handleNodeClick"
            />
          </div>
        </div>
      </el-col>
      <el-col
        :span="17"
        class="full-height overflow-auto">
        <div class="page-card shadow-light">
          <div class="table-title">当前选择：{{ selected.orgAllName || '' }}</div>
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            style="width: 100%"
          >
            <el-table-column
              label="序号"
              type="index"
              width="70"
            />
            <el-table-column
              label="部门编号"
              prop="orgCode"
              width="180"
            />
            <el-table-column
              label="部门名称"
              prop="orgAllName"
              show-overflow-tooltip
            />
            <el-table-column
              label="部门描述"
              prop="orgDesc"
              width="180"
            />
            <el-table-column
              label="父级部门"
              prop="parentOrgName"
            />
          </el-table>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { orgListByCode } from '@/api/system'
import { post } from '@/lib/Util'
import { ENUM } from '@/lib/Constant'

export default {
  layout: 'menuLayout',
  name: 'system-org',
  data: () => {
    return {
      loading: false,
      visibleEdit: false,
      selected: {},
      data: [], // 树状数据
      imgName: '', // 树状数据
      tableData: [], // 表格数据
      defaultProps: {
        children: 'children',
        label: 'orgAllName',
        isLeaf: 'leaf'
      }
    }
  },
  async created() {
    // this.data = await this.loadData('X')
  },
  methods: {
    setIMG() {
      this.imgName = test
    },
    async loadData(orgCode) {
      const data = await post(orgListByCode, { orgCode: orgCode })
      data.data.forEach(item => {
        item.leaf = !item.hasChildren
      })
      return Promise.resolve(data.success ? data.data : [])
    },

    async loadNode(node, resolve) {
      const parentCode = node.data.orgCode || 'X'
      let data = await this.loadData(parentCode)
      // *********,中厚板卷厂,X
      // *********,宽厚板厂,X
      // *********,板材事业部,X
      // *********,中板厂,X
      // *********,金石材料厂,X
      if (parentCode === 'X') {
        data = data.filter(item => ENUM.orgTop.indexOf(item.orgCode) !== -1)
      }
      resolve(data)
    },
    async handleNodeClick(data) {
      this.selected = data
      this.tableData = await this.loadData(data.orgCode)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.tree-left {
  overflow: auto;
  padding: 24px;
  background: #fff;
  border: 1px solid #eee;

  .tree-box {
    height: calc(100% - 55px);
    overflow: auto;
  }

  .tree-tit {
    margin-bottom: 20px;
    font-size: 18px;
    line-height: 1.5;
  }
}
.table-pagination {
  margin-top: 20px;
}
.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}
.tree-wrapper {
  height: 74vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
</style>
