<template>
  <div
    ref="gridWrapper"
    style="width: 100%;height: 100%;position: relative"
    @dragover.prevent="dragover"
    @drop.prevent="drop">
    <grid-layout
      ref="gridlayout"
      :layout.sync="layout"
      :col-num="rowNum"
      :row-height="rowHeight"
      :is-draggable="true"
      :is-resizable="true"
      :is-mirrored="false"
      :responsive="true"
      :vertical-compact="true"
      :margin="[20, 20]"
      :use-css-transforms="true"
    >
      <template
        v-for="(item, index) in layout">
        <grid-item
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          :key="item.i + index"
          :class="{
            'dropping': dropping,
            'showHeader': index === showHeaderIndex
          }"
          class="widget-item"
          @resized="setWidgetSize"
          @moved="changePosEvent"
          @mousedown.native.stop>
          <div class="widget">
            <div
              class="widget-header">
              {{ item.name }}
              <div
                class="widget-operate">
                <div class="operate-box">
                  <i
                    slot="reference"
                    class="el-icon-setting"/>
                  <ul
                    class="d-contextmenu v-contextmenu">
                    <li class="d-contextmenu-item hover">
                      <i class="el-icon-s-grid"/>
                      布局
                      <p class="contextmenu-layout">
                        <em @click="setWidgetSize(item.i, 2, 3)">3x2</em>
                        <em @click="setWidgetSize(item.i, 4, 4)">4x4</em>
                        <em @click="setWidgetSize(item.i, 4, 6)">6x4</em>
                        <em @click="setWidgetSize(item.i, 5, 12)">12x5</em>
                      </p>
                    </li>
                    <li
                      class="d-contextmenu-item hover"
                      @click="delWidget(index)">
                      <i class="el-icon-delete"/>
                      删除</li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="widget-content">
              <div class="iframe-wrapper">
                <iframe
                  :id="'iframe'"
                  :src="getUrl(item)"
                  allowfullscreen
                  class="iframe"
                  width="100%"
                  height="100%"
                  frameborder="0"
                  scrolling="auto" />
              </div>
            </div>
          </div>
          <div class="cover"/>
        </grid-item>
      </template>
    </grid-layout>
  </div>

</template>
<script>
import { mapGetters, mapState } from 'vuex'
import VueGridLayout from 'vue-grid-layout'
import { cloneDeep } from 'lodash'
import uuid from 'uuid'
import { initWidget } from '@/lib/Util'
import { ikbToken } from '@/config'
let mouseXY = { x: null, y: null }
let DragPos = { x: null, y: null, w: 1, h: 1, i: null }

export default {
  name: 'WindowsWidget',
  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem
  },
  props: {
    widgetList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      rowNum: 12,
      rowHeight: 30,
      // layout: this.widgetList,
      blankService: ['ikb'], //无需token的页面
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId'),
      dropping: false,
      showHeaderIndex: null, // 显示header组件index
      dragWidget: null
    }
  },
  computed: {
    layout: {
      get() {
        // console.log('元数据', this.widgetList)
        return this.widgetList.filter(obj => obj.i !== 'drop')
      },
      set() {}
    }
    // ...mapState('desktop', ['dragWidget'])
    // ...mapGetters('desktop', ['dragWidget'])
  },
  mounted() {
    this.$nextTick(this.calculateHeight)
    window.addEventListener('resize', this.calculateHeight, false)
    this.subscribe()
  },
  destroyed() {
    this.$bus.$off('widget-dragend')
    this.$bus.$off('widget-dragstart')
  },
  methods: {
    calculateHeight() {
      this.rowHeight = parseInt(
        (this.$refs.gridWrapper.offsetWidth - (this.rowNum + 1) * 20) /
          this.rowNum
      )
    },
    subscribe() {
      this.$bus.$on('widget-dragend', () => {
        this.dragEnd()
      })
      this.$bus.$on('widget-dragstart', data => {
        this.dragWidget = data
      })
    },
    getUrl(widget) {
      const data = widget
      if (!data.url) return ''
      if (this.blankService.indexOf(data.serviceName) !== -1) {
        // 新窗口打开
        return `http://${data.ip}:${data.port}${data.url}&token=${ikbToken}`
      } else {
        const hasParams = data.url.includes('?')
        return `http://${data.ip}:${data.port}${data.url}${
          hasParams ? '&' : '?'
        }org=redirect&token=${this.token}&userId=${this.userId}`
      }
    },
    changePosEvent: function(i, x, y) {
      let index = this.layout.findIndex(item => item.i === i)
      this.$emit('change-pos', [index, x, y])
    },
    changeEvent: function() {
      this.$emit('change', {})
    },
    // 删除小部件
    delWidget(index) {
      this.$emit('delete-widget', index)
    },
    // 设置尺寸
    setWidgetSize(i, h, w) {
      let index = this.layout.findIndex(item => item.i === i)
      this.$emit('change-size', [index, h, w])
    },
    dragover(e) {
      // console.log(e)
      if (!this.dragWidget) return e.preventDefault()
      let parentRect = this.$refs.gridWrapper.getBoundingClientRect()
      let mouseInGrid = false
      mouseXY.x = e.clientX
      mouseXY.y = e.clientY
      if (
        mouseXY.x > parentRect.left &&
        mouseXY.x < parentRect.right &&
        (mouseXY.y > parentRect.top && mouseXY.y < parentRect.bottom)
      ) {
        mouseInGrid = true
        this.dropping = true
      }
      if (
        mouseInGrid === true &&
        this.layout.findIndex(item => item.i === 'drop') === -1
      ) {
        // 添加临时grid
        this.layout.push({
          x: (this.layout.length * 2) % (this.colNum || 12),
          y: this.layout.length + (this.colNum || 12), // puts it at the bottom
          w: this.dragWidget.w,
          h: this.dragWidget.h,
          i: 'drop'
        })
      }

      let index = this.layout.findIndex(item => item.i === 'drop')
      if (index !== -1) {
        try {
          this.$nextTick(() => {
            const childrenRef = this.$refs.gridlayout.$children[
              this.layout.length
            ]
            childrenRef && (childrenRef.$refs.item.style.display = 'none')
          })
        } catch (e) {
          // console.log(e)
        }
        let el = this.$refs.gridlayout.$children[index]
        el.dragging = {
          top: mouseXY.y - parentRect.top,
          left: mouseXY.x - parentRect.left
        }
        let new_pos = el.calcXY(
          mouseXY.y - parentRect.top,
          mouseXY.x - parentRect.left
        )
        if (mouseInGrid === true) {
          this.$refs.gridlayout.dragEvent(
            'dragstart',
            'drop',
            new_pos.x,
            new_pos.y,
            this.dragWidget.h,
            this.dragWidget.w
          )
          DragPos.i = String(index)
          DragPos.x = this.layout[index].x
          DragPos.y = this.layout[index].y
        }
        if (mouseInGrid === false) {
          this.$refs.gridlayout.dragEvent(
            'dragend',
            'drop',
            new_pos.x,
            new_pos.y,
            this.dragWidget.h,
            this.dragWidget.w
          )
          this.layout = this.layout.filter(obj => obj.i !== 'drop')
          this.dragging = false
        }
      }
    },
    drop(e) {
      // console.log('放置')
      if (!this.dragWidget) return e.preventDefault()
      let parentRect = this.$refs.gridWrapper.getBoundingClientRect()
      let mouseInGrid = false
      if (
        mouseXY.x > parentRect.left &&
        mouseXY.x < parentRect.right &&
        (mouseXY.y > parentRect.top && mouseXY.y < parentRect.bottom)
      ) {
        mouseInGrid = true
      }
      if (mouseInGrid === true) {
        this.$refs.gridlayout.dragEvent(
          'dragend',
          'drop',
          DragPos.x,
          DragPos.y,
          this.dragWidget.h,
          this.dragWidget.w
        )
        this.layout = this.layout.filter(obj => obj.i !== 'drop')
        this.dragging = false
        // UNCOMMENT below if you want to add a grid-item
        Object.assign(this.dragWidget, {
          x: DragPos.x,
          y: DragPos.y
        })
        this.dropping = false
        this.$refs.gridlayout.dragEvent(
          'dragend',
          this.dragWidget.i,
          DragPos.x,
          DragPos.y,
          this.dragWidget.h,
          this.dragWidget.w
        )
        try {
          this.$refs.gridLayout.$children[
            this.layout.length
          ].$refs.item.style.display = 'block'
        } catch {}
        this.$bus.$emit('add-widget', this.dragWidget)
      }
    },
    dragEnd() {
      // 结束拖拽取消拖放
      this.dropping = false
      try {
        if (this.layout.find(obj => obj.i !== 'drop')) {
          this.$refs.gridlayout.dragEvent(
            'dragend',
            'drop',
            DragPos.x,
            DragPos.y,
            this.dragWidget.h,
            this.dragWidget.w
          )
          this.layout = this.layout.filter(obj => obj.i !== 'drop')
        }
      } catch {}
    },
    hideHeader() {
      this.showHeaderIndex = null
    },
    showHeader(index) {
      this.showHeaderIndex = index
    }
  }
}
</script>

<style scoped lang="less">
.widget-item {
  position: relative;
  background-size: cover;
  color: #fff;
  //background: #757474;
  border-radius: 12px;
  overflow: hidden;
  &.dropping .cover {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  .iframe {
    position: relative;
  }
  .operate-box {
    position: relative;
    font-size: 20px;
    background-size: cover;
    padding: 8px;
    border-radius: 0 4px 4px 0;
    transition: all ease-in-out 0.5s;
    transition-delay: 0.2s;
    &:hover {
      .d-contextmenu {
        opacity: 1;
        visibility: visible;
      }
    }

    .d-contextmenu {
      position: absolute;
      right: 0;
      top: 100%;
      width: 140px;
      border-radius: 8px;
      padding: 5px 0;
      background-color: #0b0b0bcc;
      backdrop-filter: blur(6px);
      box-shadow: 0 2px 8px #0000004d;
      border: 1px solid rgba(11, 11, 11, 0.2);
      opacity: 0;
      visibility: hidden;
      z-index: 999;
    }

    .d-contextmenu-item {
      font-size: 13px;
      line-height: 30px;
      color: #fff;
      padding: 0 4px;
      cursor: pointer;
    }

    .d-contextmenu-item .item-icon {
      display: inline-block;
      width: 20px;
      vertical-align: -2px;
      font-size: 14px;
    }

    .d-contextmenu-item.hover:hover {
      background-color: #ffffff1a;
    }

    .contextmenu-layout {
      margin-left: 13px;
    }
    .contextmenu-layout em {
      display: inline-block;
      font-size: 12px;
      height: 22px;
      line-height: 22px;
      width: 42px;
      background-color: #ffffff1a;
      border-radius: 12px;
      text-align: center;
      margin: 4px;
      cursor: pointer;
      transition: background-color 0.25s;
    }

    .contextmenu-layout em:hover {
      background-color: #fff3;
    }

    .contextmenu-layout em.active {
      background-color: #ffffff4d;
    }
  }
  &:hover {
    z-index: 9;
    .widget-header {
    }
  }

  .widget {
    display: flex;
    height: 100%;
    flex-direction: column;
    .widget-header {
      z-index: 9;
      user-select: none;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      padding: 0 15px;
      letter-spacing: 2px;
      color: #fff;
      backdrop-filter: blur(10px);
      background: rgba(222, 222, 222, 0.5)
        linear-gradient(135deg, rgba(20, 20, 20, 0.5), rgba(20, 20, 20, 0.5))
        center;
      font-weight: bolder;
      transition: all ease-in-out 0.5s;
      &-icon {
        font-weight: normal;
        font-size: 24px;
        margin-left: 5px;
        cursor: pointer;
      }
      .widget-header-icon {
        color: #fff;
      }
      .widget-operate {
        display: flex;
      }
    }
    .widget-content {
      flex: 1;
      height: calc(100% - 40px);
    }
    .iframe-wrapper {
      height: 100%;
      overflow: hidden;
      padding: 10px;
      background: rgba(255, 255, 255, 0.9);
    }
  }
}
/deep/ .vue-grid-item.vue-grid-placeholder {
  background: rgb(149, 203, 225) !important;
  opacity: 0.5;
}
</style>
