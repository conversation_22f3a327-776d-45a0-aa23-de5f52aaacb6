<template>
  <div class="main">
    <div class="box">
      <ul>
        <li
          v-for="item in banerList"
          :key="item.code"
          class="box-item"
          @click="pageGo(item)"/>
      </ul>
    </div>
  </div>
</template>

<script>
import { findRootResource } from '@/api/desktop'
import { post } from '@/lib/Util'
export default {
  layout: 'staticPage',
  name: 'Main',
  data() {
    return {
      //http://*************:9093/?org=redirect&token=eyJ0eXBlIjoiand0IiwiYWxnIjoiSFMyNTYiLCJ0eXAiOiJKV1QifQ.eyJ1c2VyTmFtZSI6IuiMhuiNo-S8nyIsImV4cCI6MTY1NjU2OTM1OSwidXNlcklkIjoiMDIzOTU4In0.s-DnR3by398RrnrQX7bg79JChAAWQeBhdZKnY4AG6jY&userId=023958
      banerList: [
        {
          urlIp: ''
        },
        {
          urlIp: ''
        },
        {
          urlIp: ''
        },
        {
          urlIp: ''
        },
        {
          urlIp: ''
        },
        {
          urlIp: ''
        },
        {
          urlIp: ''
        }
      ]
    }
  },
  mounted() {
    this.findRootResource()
  },
  methods: {
    pageGo(item) {
      window.open(item.urlIp)
    },
    findRootResource() {
      post(findRootResource, {
        userNo: localStorage.getItem('userId'), //传入
        type: 'menu',
        serviceName: ''
      }).then(res => {
        for (let i = 0; i < res.data.length; i++) {
          res.data[i].urlIp =
            'http://' +
            res.data[i].ip +
            ':' +
            res.data[i].port +
            res.data[i].url +
            '?org=redirect&token=' +
            localStorage.getItem('token') +
            '&userId=' +
            localStorage.getItem('userId')
          switch (res.data[i].serviceName) {
            case 'ipd':
              this.banerList[0].urlIp = res.data[i].urlIp
              break
            case 'ems':
              this.banerList[1].urlIp = res.data[i].urlIp
              break
            case 'iom':
              this.banerList[2].urlIp = res.data[i].urlIp
              break
            case 'idm':
              this.banerList[3].urlIp = res.data[i].urlIp
              break
            case 'ifc':
              this.banerList[4].urlIp = res.data[i].urlIp
              break
            case 'qms':
              this.banerList[5].urlIp = res.data[i].urlIp
              break
          }
        }
        console.log(this.banerList)
      })
    }
  }
}
</script>

<style scoped lang="less">
.main {
  width: 100%;
  height: 100%;
  background: url('../assets/bannerList/Frame 22.png') no-repeat;
  overflow: hidden;
  .box {
    width: 1120px;
    height: auto;
    margin: 247px auto;
    > ul {
      overflow: hidden;
      .box-item {
        width: 300px;
        height: 300px;
        float: left;
        //margin-right: 28px;
        cursor: pointer;
        transition: all 0.3s ease-in-out 0.2s;
        &:nth-child(1) {
          background: url('../assets/bannerList/Frame-1.png');
          &:hover {
            background: url('../assets/bannerList/Frame-1-h.png');
          }
        }
        &:nth-child(2) {
          background: url('../assets/bannerList/Frame-2.png');
          &:hover {
            background: url('../assets/bannerList/Frame-2-h.png');
          }
        }
        &:nth-child(3) {
          background: url('../assets/bannerList/Frame-3.png');
          &:hover {
            background: url('../assets/bannerList/Frame-3-h.png');
          }
        }
        &:nth-child(4) {
          margin-left: 150px;
          background: url('../assets/bannerList/Frame-4.png');
          &:hover {
            background: url('../assets/bannerList/Frame-4-h.png');
          }
        }
        &:nth-child(5) {
          background: url('../assets/bannerList/Frame-5.png');
          &:hover {
            background: url('../assets/bannerList/Frame-5-h.png');
          }
        }
        &:nth-child(6) {
          background: url('../assets/bannerList/Frame-6.png');
          &:hover {
            background: url('../assets/bannerList/Frame-6-h.png');
          }
        }
      }
    }
  }
}
</style>
